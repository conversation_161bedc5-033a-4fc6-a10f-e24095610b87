#!/usr/bin/env node

// 浏览器调试模拟器 - 模拟浏览器行为来调试前端页面
const https = require('https');
const http = require('http');
const { URL } = require('url');

class BrowserDebugSimulator {
    constructor() {
        this.cookies = new Map();
        this.localStorage = new Map();
        this.sessionStorage = new Map();
        this.authToken = null;
    }

    // 模拟HTTP请求
    async request(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const client = isHttps ? https : http;

            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'User-Agent': 'BrowserDebugSimulator/1.0',
                    'Accept': 'application/json, text/html, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    ...options.headers
                }
            };

            const req = client.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            data: jsonData,
                            raw: data
                        });
                    } catch (e) {
                        resolve({
                            status: res.statusCode,
                            headers: res.headers,
                            data: null,
                            raw: data
                        });
                    }
                });
            });

            req.on('error', (err) => {
                reject(err);
            });

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    }

    // 模拟登录
    async login(baseUrl, username = 'admin', password = 'admin123') {
        console.log('🔐 模拟登录...');
        
        try {
            const response = await this.request(`${baseUrl}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            if (response.status === 200 && response.data && response.data.token) {
                this.authToken = response.data.token;
                this.localStorage.set('token', this.authToken);
                console.log('✅ 登录成功');
                console.log(`Token: ${this.authToken.substring(0, 50)}...`);
                return true;
            } else {
                console.log('❌ 登录失败');
                console.log(`状态码: ${response.status}`);
                console.log(`响应: ${response.raw}`);
                return false;
            }
        } catch (error) {
            console.log('❌ 登录错误:', error.message);
            return false;
        }
    }

    // 模拟访问仪表板
    async testDashboard(baseUrl) {
        console.log('\n📊 测试仪表板页面...');
        
        if (!this.authToken) {
            console.log('❌ 未登录，无法访问仪表板');
            return false;
        }

        try {
            // 1. 测试仪表板API
            const dashboardResponse = await this.request(`${baseUrl}/api/dashboard/overview`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            console.log(`仪表板API状态: ${dashboardResponse.status}`);
            
            if (dashboardResponse.status === 200 && dashboardResponse.data) {
                const data = dashboardResponse.data;
                console.log('✅ 仪表板数据获取成功');
                console.log('📈 关键指标:');
                console.log(`  设备利用率: ${data.machine_status?.utilization_rate || 0}%`);
                console.log(`  质量合格率: ${data.quality_metrics?.quality_rate || 0}%`);
                console.log(`  总工单数: ${data.production_summary?.total_work_orders || 0}`);
                console.log(`  进行中工单: ${data.work_order_status?.in_progress_orders || 0}`);
                console.log(`  待处理工单: ${data.work_order_status?.pending_orders || 0}`);
                
                return true;
            } else {
                console.log('❌ 仪表板数据获取失败');
                console.log(`响应: ${dashboardResponse.raw}`);
                return false;
            }
        } catch (error) {
            console.log('❌ 仪表板测试错误:', error.message);
            return false;
        }
    }

    // 模拟访问其他页面
    async testOtherPages(baseUrl) {
        console.log('\n📋 测试其他页面...');
        
        const pages = [
            { name: '项目管理', endpoint: '/api/projects' },
            { name: '零件管理', endpoint: '/api/parts' },
            { name: '设备管理', endpoint: '/api/machines' },
            { name: '工单管理', endpoint: '/api/work-orders' }
        ];

        for (const page of pages) {
            try {
                const response = await this.request(`${baseUrl}${page.endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                });

                if (response.status === 200 && response.data) {
                    const dataKey = Object.keys(response.data)[0];
                    const items = response.data[dataKey];
                    console.log(`✅ ${page.name}: ${Array.isArray(items) ? items.length : 0} 条记录`);
                } else {
                    console.log(`❌ ${page.name}: 获取失败 (${response.status})`);
                }
            } catch (error) {
                console.log(`❌ ${page.name}: 错误 - ${error.message}`);
            }
        }
    }

    // 模拟前端页面加载
    async simulatePageLoad(baseUrl) {
        console.log('\n🌐 模拟前端页面加载...');
        
        try {
            // 1. 获取主页面
            const mainPageResponse = await this.request(baseUrl);
            console.log(`主页面状态: ${mainPageResponse.status}`);
            
            if (mainPageResponse.status === 200) {
                console.log('✅ 前端页面加载成功');
                
                // 检查是否包含React相关内容
                if (mainPageResponse.raw.includes('react') || mainPageResponse.raw.includes('React')) {
                    console.log('✅ 检测到React应用');
                }
                
                // 检查是否包含Vite开发服务器
                if (mainPageResponse.raw.includes('vite') || mainPageResponse.raw.includes('@vite/client')) {
                    console.log('✅ 检测到Vite开发服务器');
                }
                
                return true;
            } else {
                console.log('❌ 前端页面加载失败');
                return false;
            }
        } catch (error) {
            console.log('❌ 页面加载错误:', error.message);
            return false;
        }
    }

    // 生成调试报告
    generateReport() {
        console.log('\n📋 调试报告');
        console.log('================================');
        console.log(`认证状态: ${this.authToken ? '✅ 已登录' : '❌ 未登录'}`);
        console.log(`LocalStorage项目: ${this.localStorage.size}`);
        console.log(`SessionStorage项目: ${this.sessionStorage.size}`);
        
        if (this.authToken) {
            console.log(`Token长度: ${this.authToken.length}`);
            console.log(`Token前缀: ${this.authToken.substring(0, 20)}...`);
        }
    }

    // 运行完整测试
    async runFullTest(baseUrl = 'http://localhost:3000') {
        console.log('🧪 开始完整的前端调试测试');
        console.log('================================');
        console.log(`目标URL: ${baseUrl}`);
        
        // 1. 测试页面加载
        const pageLoadSuccess = await this.simulatePageLoad(baseUrl);
        
        // 2. 测试登录
        const loginSuccess = await this.login(baseUrl);
        
        // 3. 测试仪表板
        let dashboardSuccess = false;
        if (loginSuccess) {
            dashboardSuccess = await this.testDashboard(baseUrl);
        }
        
        // 4. 测试其他页面
        if (loginSuccess) {
            await this.testOtherPages(baseUrl);
        }
        
        // 5. 生成报告
        this.generateReport();
        
        console.log('\n🎯 测试总结');
        console.log('================================');
        console.log(`页面加载: ${pageLoadSuccess ? '✅ 成功' : '❌ 失败'}`);
        console.log(`用户登录: ${loginSuccess ? '✅ 成功' : '❌ 失败'}`);
        console.log(`仪表板数据: ${dashboardSuccess ? '✅ 成功' : '❌ 失败'}`);
        
        if (pageLoadSuccess && loginSuccess && dashboardSuccess) {
            console.log('\n🎉 所有测试通过！前端应用工作正常。');
            console.log('💡 如果仍然看不到数据，请检查:');
            console.log('   1. 浏览器控制台是否有JavaScript错误');
            console.log('   2. 网络请求是否被阻止');
            console.log('   3. React组件是否正确渲染');
        } else {
            console.log('\n⚠️ 部分测试失败，请检查相关组件。');
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const simulator = new BrowserDebugSimulator();
    simulator.runFullTest().catch(console.error);
}

module.exports = BrowserDebugSimulator;
