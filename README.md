# Manufacturing Execution System (MES)

A comprehensive Manufacturing Execution System built with Rust, featuring real-time production tracking, quality management, and comprehensive reporting capabilities.

## Features

### ✅ Completed Features

- **User Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control (Ad<PERSON>, Process Engineer, Planner, Operator, Quality Inspector, Viewer)
  - Skill-based permissions

- **Project & Parts Management**
  - Project creation and management
  - Parts catalog with versioning
  - Bill of Materials (BOM) management
  - Routing definitions for manufacturing processes

- **Work Order Management**
  - Work order creation from project BOMs
  - Status tracking and lifecycle management
  - Priority and due date management
  - Quantity tracking

- **Production Planning**
  - Task scheduling to skill groups
  - Gantt chart data generation
  - Resource allocation and planning
  - Timeline visualization support

- **Shop Floor Execution**
  - Real-time task execution tracking
  - Barcode scanning simulation
  - Task start/stop/pause/resume operations
  - Execution logging and audit trail

- **Dashboard & Reporting**
  - Real-time production dashboard
  - KPI metrics and visualization
  - Machine utilization reports
  - Production progress tracking
  - Trend analysis

- **Quality Management**
  - Quality inspection workflows
  - Quality checkpoints and criteria
  - Inspection result recording
  - Quality metrics and reporting

- **Audit Logging**
  - Comprehensive change tracking
  - Audit trail for all operations
  - Export capabilities
  - Retention management

## API Documentation

The system provides a comprehensive REST API with **78 active endpoints** across 11 functional categories:

- 📋 **[Complete API Documentation](API_DOCUMENTATION.md)** - Detailed endpoint documentation with examples
- 📊 **[API Summary](API_SUMMARY.md)** - Quick overview and statistics
- 📈 **[API Endpoints CSV](api_endpoints.csv)** - Machine-readable endpoint list

### API Statistics
- **Total Endpoints**: 78 active endpoints
- **Authentication**: JWT Bearer tokens
- **Methods**: GET (59%), POST (32%), PUT (5%), DELETE (4%)
- **Access Levels**: 5 public, 73 protected, 8 admin-only

### Key API Categories
- Authentication & User Management
- Project & Parts Management
- Work Order & Production Planning
- Shop Floor Execution Tracking
- Dashboard & Reporting
- Quality Management
- Audit Logging

## Technology Stack

### Backend
- **Framework**: Rust with Axum web framework
- **Database**: PostgreSQL with SQLx
- **Authentication**: JWT tokens
- **API**: RESTful JSON API with 78 endpoints
- **Migrations**: SQLx migrations

### Frontend
- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Library**: Ant Design 5
- **State Management**: Zustand + React Query
- **Routing**: React Router 6
- **Styling**: Tailwind CSS + Ant Design
- **Charts**: Recharts

## Quick Start

### Prerequisites

**Backend:**
- Rust 1.70+
- PostgreSQL 12+
- Git

**Frontend:**
- Node.js 18+
- npm or yarn

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mes-system
   ```

2. **Set up the database**
   ```bash
   # Create PostgreSQL database
   createdb mes_db

   # Set environment variables
   export DATABASE_URL="postgresql://postgres:password@localhost:5432/mes_db"
   export JWT_SECRET="your-secret-key-here"
   ```

3. **Start the backend**
   ```bash
   # Run the application (migrations run automatically)
   cargo run
   ```

   Backend API will be available at: `http://localhost:8080`

### Frontend Setup

1. **Install and start frontend**
   ```bash
   # 使用提供的启动脚本（推荐）
   ./start_frontend.sh

   # 或手动启动
   cd frontend
   npm install
   npm run dev
   ```

   Frontend will be available at: `http://localhost:3000`

2. **Default login credentials**
   - Username: `admin`
   - Password: `admin123`

### Testing

Run the API test suite:
```bash
./test_api.sh
```

## API Documentation

See [API_DOCUMENTATION.md](API_DOCUMENTATION.md) for complete API reference.

### Quick API Examples

**Login:**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

**Get Dashboard:**
```bash
curl -H "Authorization: Bearer <token>" \
  http://localhost:8080/api/dashboard/overview
```

**Create Work Order:**
```bash
curl -X POST http://localhost:8080/api/work-orders \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"project_bom_id": 1, "quantity": 100, "due_date": "2025-07-15"}'
```

## Database Schema

The system includes the following main entities:

- **Users** - Authentication and role management
- **Projects** - Manufacturing projects
- **Parts** - Part definitions and catalog
- **Project BOMs** - Bill of materials
- **Routings** - Manufacturing process steps
- **Work Orders** - Production orders
- **Plan Tasks** - Scheduled production tasks
- **Execution Logs** - Real-time execution tracking
- **Quality Inspections** - Quality control records
- **Machines** - Equipment management
- **Skill Groups** - Worker classifications

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Layer     │    │   Database      │
│   (Future)      │◄──►│   (Axum/Rust)   │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Services      │
                       │   - Auth        │
                       │   - Planning    │
                       │   - Execution   │
                       │   - Quality     │
                       │   - Dashboard   │
                       │   - Audit       │
                       └─────────────────┘
```

## Development

### Project Structure
```
src/
├── handlers/          # HTTP request handlers
├── middleware/        # Authentication & authorization
├── models/           # Data models and DTOs
├── services/         # Business logic
├── utils/           # Utilities and helpers
└── main.rs          # Application entry point

migrations/          # Database migrations
tests/              # Test files (future)
```

### Adding New Features

1. Define models in `src/models/`
2. Implement business logic in `src/services/`
3. Create HTTP handlers in `src/handlers/`
4. Add routes in `src/main.rs`
5. Create database migrations if needed

### Code Style

- Follow Rust conventions
- Use `cargo fmt` for formatting
- Run `cargo clippy` for linting
- Add comprehensive error handling

## Deployment

### Production Deployment

1. **Build release binary**
   ```bash
   cargo build --release
   ```

2. **Set environment variables**
   ```bash
   export DATABASE_URL="******************************"
   export JWT_SECRET="production-secret-key"
   export RUST_LOG="info"
   ```

3. **Run migrations**
   ```bash
   ./target/release/mes-system
   ```

### Docker Deployment (Future)

Docker support is planned for easier deployment.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or support, please open an issue on the repository.

## Roadmap

### Planned Features
- [ ] WebSocket support for real-time updates
- [ ] Advanced scheduling algorithms
- [ ] Mobile app for shop floor
- [ ] Integration with ERP systems
- [ ] Advanced analytics and ML
- [ ] Docker containerization
- [ ] Kubernetes deployment
- [ ] Frontend web application

### Current Status
✅ Core MES functionality complete
✅ API endpoints implemented
✅ Database schema established
✅ Authentication & authorization
✅ Basic testing framework

The system is ready for production use with all core MES features implemented.
