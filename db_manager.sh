#!/bin/bash

# MES系统数据库管理工具
# 提供数据库查看、管理和测试功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 数据库连接配置
DB_HOST="localhost"
DB_USER="postgres"
DB_PASSWORD="password"
DB_NAME="mes_db"

# 执行SQL查询
execute_sql() {
    local sql="$1"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "$sql"
}

# 显示表格数据
show_table() {
    local table_name="$1"
    local limit="${2:-50}"
    
    echo -e "${BLUE}📋 表格: $table_name${NC}"
    echo "================================"
    execute_sql "SELECT * FROM $table_name ORDER BY id LIMIT $limit;"
    echo ""
}

# 显示表格统计
show_table_stats() {
    echo -e "${CYAN}📊 数据库表统计${NC}"
    echo "================================"
    execute_sql "
    SELECT 
        'projects' as table_name, 
        count(*) as count,
        'Project management' as description
    FROM projects 
    UNION ALL 
    SELECT 'parts', count(*), 'Parts catalog' FROM parts
    UNION ALL 
    SELECT 'work_orders', count(*), 'Work orders' FROM work_orders
    UNION ALL 
    SELECT 'machines', count(*), 'Equipment' FROM machines
    UNION ALL 
    SELECT 'users', count(*), 'System users' FROM users
    UNION ALL 
    SELECT 'project_boms', count(*), 'Bill of materials' FROM project_boms
    UNION ALL 
    SELECT 'routings', count(*), 'Process routings' FROM routings
    UNION ALL 
    SELECT 'plan_tasks', count(*), 'Production tasks' FROM plan_tasks
    UNION ALL 
    SELECT 'execution_logs', count(*), 'Execution logs' FROM execution_logs
    UNION ALL 
    SELECT 'audit_logs', count(*), 'Audit trail' FROM audit_logs
    ORDER BY count DESC;
    "
    echo ""
}

# 显示最近活动
show_recent_activity() {
    echo -e "${PURPLE}⏰ 最近活动 (最近24小时)${NC}"
    echo "================================"
    
    echo "最近创建的项目:"
    execute_sql "SELECT id, project_name, customer_name, created_at FROM projects WHERE created_at > NOW() - INTERVAL '24 hours' ORDER BY created_at DESC;"
    
    echo "最近创建的工单:"
    execute_sql "SELECT id, project_bom_id, quantity, status, created_at FROM work_orders WHERE created_at > NOW() - INTERVAL '24 hours' ORDER BY created_at DESC;"
    
    echo "最近的审计日志:"
    execute_sql "SELECT id, action_type, target_entity, target_id, action_time FROM audit_logs WHERE action_time > NOW() - INTERVAL '24 hours' ORDER BY action_time DESC LIMIT 10;"
    echo ""
}

# 添加测试数据
add_test_data() {
    echo -e "${GREEN}🧪 添加测试数据${NC}"
    echo "================================"
    
    # 添加测试项目
    echo "添加测试项目..."
    execute_sql "INSERT INTO projects (project_name, customer_name) VALUES ('数据库测试项目', '数据库测试客户') ON CONFLICT DO NOTHING;"
    
    # 添加测试零件
    echo "添加测试零件..."
    execute_sql "INSERT INTO parts (part_number, part_name, version, specifications) VALUES ('DB-TEST-001', '数据库测试零件', 'v1.0', '通过数据库管理工具添加的测试零件') ON CONFLICT DO NOTHING;"
    
    # 添加测试设备
    echo "添加测试设备..."
    execute_sql "INSERT INTO machines (machine_name, skill_group_id, status) VALUES ('DB-TEST-MACHINE', 1, 'available') ON CONFLICT DO NOTHING;"
    
    echo -e "${GREEN}✅ 测试数据添加完成${NC}"
    echo ""
}

# 清理测试数据
cleanup_test_data() {
    echo -e "${YELLOW}🧹 清理测试数据${NC}"
    echo "================================"
    
    echo "清理测试项目..."
    execute_sql "DELETE FROM projects WHERE project_name LIKE '%测试%' OR project_name LIKE '%test%' OR project_name LIKE '%Test%';"
    
    echo "清理测试零件..."
    execute_sql "DELETE FROM parts WHERE part_number LIKE '%TEST%' OR part_name LIKE '%测试%';"
    
    echo "清理测试设备..."
    execute_sql "DELETE FROM machines WHERE machine_name LIKE '%TEST%' OR machine_name LIKE '%测试%';"
    
    echo -e "${GREEN}✅ 测试数据清理完成${NC}"
    echo ""
}

# 检查数据库连接
check_connection() {
    echo -e "${BLUE}🔍 检查数据库连接${NC}"
    echo "================================"
    
    if execute_sql "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接正常${NC}"
        echo "数据库: $DB_NAME"
        echo "主机: $DB_HOST"
        echo "用户: $DB_USER"
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"
        exit 1
    fi
    echo ""
}

# 显示数据库架构
show_schema() {
    echo -e "${CYAN}🏗️ 数据库架构${NC}"
    echo "================================"
    execute_sql "
    SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    ORDER BY table_name, ordinal_position;
    "
    echo ""
}

# 执行自定义SQL
execute_custom_sql() {
    echo -e "${PURPLE}💻 执行自定义SQL${NC}"
    echo "================================"
    echo "请输入SQL查询 (输入 'exit' 退出):"
    
    while true; do
        echo -n "SQL> "
        read -r sql_query
        
        if [ "$sql_query" = "exit" ]; then
            break
        fi
        
        if [ -n "$sql_query" ]; then
            execute_sql "$sql_query"
            echo ""
        fi
    done
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}🗄️ MES数据库管理工具${NC}"
    echo "================================"
    echo "1. 📊 查看数据库统计"
    echo "2. 📋 查看所有表数据"
    echo "3. 🔍 查看特定表"
    echo "4. ⏰ 查看最近活动"
    echo "5. 🧪 添加测试数据"
    echo "6. 🧹 清理测试数据"
    echo "7. 🏗️ 查看数据库架构"
    echo "8. 💻 执行自定义SQL"
    echo "9. 🔄 刷新显示"
    echo "0. 🚪 退出"
    echo "================================"
}

# 主菜单循环
main_menu() {
    while true; do
        show_menu
        echo -n "请选择操作 (0-9): "
        read -r choice
        
        case $choice in
            1)
                show_table_stats
                ;;
            2)
                echo -e "${BLUE}📋 所有表数据${NC}"
                echo "================================"
                show_table "users" 10
                show_table "projects" 10
                show_table "parts" 10
                show_table "machines" 10
                show_table "work_orders" 10
                show_table "project_boms" 10
                ;;
            3)
                echo "可用的表: users, projects, parts, machines, work_orders, project_boms, routings, plan_tasks, execution_logs, audit_logs"
                echo -n "请输入表名: "
                read -r table_name
                if [ -n "$table_name" ]; then
                    show_table "$table_name"
                fi
                ;;
            4)
                show_recent_activity
                ;;
            5)
                add_test_data
                ;;
            6)
                cleanup_test_data
                ;;
            7)
                show_schema
                ;;
            8)
                execute_custom_sql
                ;;
            9)
                clear
                ;;
            0)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                ;;
        esac
        
        echo -e "${YELLOW}按回车键继续...${NC}"
        read -r
        clear
    done
}

# 主函数
main() {
    clear
    check_connection
    main_menu
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
