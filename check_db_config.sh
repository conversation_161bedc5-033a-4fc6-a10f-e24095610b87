#!/bin/bash

# 数据库配置一致性检查脚本
# 检查项目中所有数据库配置是否一致

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 MES系统数据库配置一致性检查${NC}"
echo "========================================"

# 定义标准配置
STANDARD_DB_NAME="mes_db"
STANDARD_DB_USER="postgres"
STANDARD_DB_PASSWORD="password"
STANDARD_DB_HOST="localhost"
STANDARD_DB_PORT="5432"
STANDARD_DATABASE_URL="postgresql://${STANDARD_DB_USER}:${STANDARD_DB_PASSWORD}@${STANDARD_DB_HOST}:${STANDARD_DB_PORT}/${STANDARD_DB_NAME}"

echo -e "${GREEN}📋 标准配置:${NC}"
echo "  数据库名: $STANDARD_DB_NAME"
echo "  用户名: $STANDARD_DB_USER"
echo "  密码: $STANDARD_DB_PASSWORD"
echo "  主机: $STANDARD_DB_HOST:$STANDARD_DB_PORT"
echo "  完整URL: $STANDARD_DATABASE_URL"
echo ""

# 检查函数
check_file() {
    local file="$1"
    local description="$2"
    
    if [ ! -f "$file" ]; then
        echo -e "${YELLOW}⚠️  文件不存在: $file${NC}"
        return
    fi
    
    echo -e "${BLUE}📄 检查 $description ($file):${NC}"
    
    # 检查数据库名称
    if grep -q "mes_db" "$file"; then
        echo -e "  ${GREEN}✅ 数据库名称正确 (mes_db)${NC}"
    elif grep -q "mes-db\|mes_system" "$file"; then
        echo -e "  ${RED}❌ 数据库名称错误${NC}"
        grep -n "mes-db\|mes_system" "$file" | head -3
    else
        echo -e "  ${YELLOW}⚠️  未找到数据库名称配置${NC}"
    fi
    
    # 检查用户名
    if grep -q "postgres:" "$file" || grep -q "postgres@" "$file"; then
        echo -e "  ${GREEN}✅ 用户名正确 (postgres)${NC}"
    elif grep -q "mes_user" "$file"; then
        echo -e "  ${RED}❌ 用户名错误 (应为postgres)${NC}"
        grep -n "mes_user" "$file" | head -3
    fi
    
    # 检查完整URL
    if grep -q "$STANDARD_DATABASE_URL" "$file"; then
        echo -e "  ${GREEN}✅ 完整数据库URL正确${NC}"
    elif grep -q "DATABASE_URL" "$file"; then
        echo -e "  ${YELLOW}⚠️  数据库URL可能不一致${NC}"
        grep -n "DATABASE_URL" "$file" | head -3
    fi
    
    echo ""
}

# 检查各个文件
check_file ".env" "环境变量配置"
check_file ".env.example" "环境变量示例"
check_file "README.md" "项目文档"
check_file "db_manager.sh" "数据库管理脚本"
check_file "test_user_creation.sh" "用户创建测试脚本"

# 检查其他可能包含数据库配置的文件
echo -e "${BLUE}🔍 搜索其他可能的数据库配置:${NC}"
echo "========================================"

# 搜索所有包含数据库名称的文件
echo -e "${YELLOW}包含数据库名称的文件:${NC}"
grep -r "mes_db\|mes-db\|mes_system" . --exclude-dir=target --exclude-dir=node_modules --exclude="*.lock" 2>/dev/null | grep -v ".git" | head -10

echo ""
echo -e "${YELLOW}包含DATABASE_URL的文件:${NC}"
grep -r "DATABASE_URL" . --exclude-dir=target --exclude-dir=node_modules --exclude="*.lock" 2>/dev/null | grep -v ".git" | head -10

echo ""
echo -e "${BLUE}🧪 测试数据库连接:${NC}"
echo "========================================"

# 测试数据库连接
if command -v psql >/dev/null 2>&1; then
    echo "测试连接到 $STANDARD_DB_NAME..."
    if PGPASSWORD=$STANDARD_DB_PASSWORD psql -h $STANDARD_DB_HOST -U $STANDARD_DB_USER -d $STANDARD_DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接成功${NC}"
        
        # 检查表是否存在
        table_count=$(PGPASSWORD=$STANDARD_DB_PASSWORD psql -h $STANDARD_DB_HOST -U $STANDARD_DB_USER -d $STANDARD_DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
        echo -e "${GREEN}📊 数据库中有 $table_count 个表${NC}"
        
        # 检查用户数量
        user_count=$(PGPASSWORD=$STANDARD_DB_PASSWORD psql -h $STANDARD_DB_HOST -U $STANDARD_DB_USER -d $STANDARD_DB_NAME -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | tr -d ' ')
        echo -e "${GREEN}👥 数据库中有 $user_count 个用户${NC}"
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"
        echo "请检查数据库是否运行，配置是否正确"
    fi
else
    echo -e "${YELLOW}⚠️  psql 未安装，无法测试数据库连接${NC}"
fi

echo ""
echo -e "${BLUE}📝 建议:${NC}"
echo "========================================"
echo "1. 确保所有配置文件使用统一的数据库名称: mes_db"
echo "2. 确保所有配置文件使用统一的用户名: postgres"
echo "3. 确保所有配置文件使用统一的密码: password"
echo "4. 定期运行此脚本检查配置一致性"
echo "5. 在部署前验证所有数据库连接配置"

echo ""
echo -e "${GREEN}✅ 数据库配置检查完成${NC}"
