<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 技能组标签页测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border: none;
            background: none;
            border-bottom: 3px solid transparent;
            margin-right: 8px;
            font-size: 14px;
            color: #666;
            transition: all 0.3s;
        }
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f6f8ff;
        }
        .tab:hover {
            color: #1890ff;
            background: #f6f8ff;
        }
        .badge {
            background: #1890ff;
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            margin-left: 8px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .machine-card {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            margin: 8px 0;
            background: #fafafa;
        }
        .machine-name {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
        }
        .machine-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-available {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status-in_use {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .status-maintenance {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .status-offline {
            background: #f0f0f0;
            color: #8c8c8c;
            border: 1px solid #d9d9d9;
        }
        .skill-group-info {
            color: #666;
            font-size: 14px;
            margin-top: 8px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 设备管理 - 技能组标签页测试</h1>
        <p>此页面演示设备管理按技能组分组的标签页功能。</p>
        
        <div class="tabs" id="tabs">
            <!-- 标签页将通过JavaScript动态生成 -->
        </div>
        
        <div id="tabContents">
            <!-- 标签页内容将通过JavaScript动态生成 -->
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = '';
        let machines = [];
        let skillGroups = [];

        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                authToken = data.token;
                return true;
            } catch (error) {
                console.error('登录失败:', error);
                return false;
            }
        }

        // 获取设备数据
        async function fetchMachines() {
            try {
                const response = await fetch(`${API_BASE}/machines`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                machines = data;
                return machines;
            } catch (error) {
                console.error('获取设备失败:', error);
                return [];
            }
        }

        // 获取技能组数据
        async function fetchSkillGroups() {
            try {
                const response = await fetch(`${API_BASE}/skill-groups`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                skillGroups = data;
                return skillGroups;
            } catch (error) {
                console.error('获取技能组失败:', error);
                return [];
            }
        }

        // 按技能组分组设备
        function groupMachinesBySkillGroup() {
            const grouped = {};
            
            // 初始化所有技能组
            skillGroups.forEach(group => {
                grouped[group.id] = {
                    name: group.group_name,
                    machines: []
                };
            });
            
            // 分组设备
            machines.forEach(machine => {
                if (grouped[machine.skill_group_id]) {
                    grouped[machine.skill_group_id].machines.push(machine);
                }
            });
            
            return grouped;
        }

        // 获取状态标签
        function getStatusTag(status) {
            const statusMap = {
                'available': { text: '可用', class: 'status-available' },
                'in_use': { text: '使用中', class: 'status-in_use' },
                'maintenance': { text: '维护中', class: 'status-maintenance' },
                'offline': { text: '离线', class: 'status-offline' }
            };
            
            const statusInfo = statusMap[status] || { text: status, class: 'status-offline' };
            return `<span class="machine-status ${statusInfo.class}">${statusInfo.text}</span>`;
        }

        // 渲染设备卡片
        function renderMachineCard(machine) {
            return `
                <div class="machine-card">
                    <div class="machine-name">
                        🔧 ${machine.machine_name}
                        ${getStatusTag(machine.status)}
                    </div>
                    <div class="skill-group-info">
                        技能组: ${machine.skill_group_name || '未分配'}
                    </div>
                </div>
            `;
        }

        // 渲染标签页
        function renderTabs() {
            const groupedMachines = groupMachinesBySkillGroup();
            const tabsContainer = document.getElementById('tabs');
            const contentsContainer = document.getElementById('tabContents');
            
            // 清空容器
            tabsContainer.innerHTML = '';
            contentsContainer.innerHTML = '';
            
            // 添加"全部设备"标签页
            const allTab = document.createElement('button');
            allTab.className = 'tab active';
            allTab.innerHTML = `🔧 全部设备 <span class="badge">${machines.length}</span>`;
            allTab.onclick = () => switchTab('all');
            tabsContainer.appendChild(allTab);
            
            // 添加"全部设备"内容
            const allContent = document.createElement('div');
            allContent.id = 'tab-all';
            allContent.className = 'tab-content active';
            allContent.innerHTML = machines.map(machine => renderMachineCard(machine)).join('');
            contentsContainer.appendChild(allContent);
            
            // 添加技能组标签页
            Object.entries(groupedMachines).forEach(([groupId, groupData]) => {
                // 创建标签页
                const tab = document.createElement('button');
                tab.className = 'tab';
                tab.innerHTML = `🔧 ${groupData.name} <span class="badge">${groupData.machines.length}</span>`;
                tab.onclick = () => switchTab(groupId);
                tabsContainer.appendChild(tab);
                
                // 创建内容
                const content = document.createElement('div');
                content.id = `tab-${groupId}`;
                content.className = 'tab-content';
                
                if (groupData.machines.length === 0) {
                    content.innerHTML = '<p style="text-align: center; color: #999; padding: 40px;">该技能组暂无设备</p>';
                } else {
                    content.innerHTML = groupData.machines.map(machine => renderMachineCard(machine)).join('');
                }
                
                contentsContainer.appendChild(content);
            });
        }

        // 切换标签页
        function switchTab(tabId) {
            // 更新标签页状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${tabId}`).classList.add('active');
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        // 初始化
        async function init() {
            const loginSuccess = await login();
            if (!loginSuccess) {
                showResult('登录失败，请检查后端服务是否正常运行。', 'error');
                return;
            }

            await Promise.all([fetchMachines(), fetchSkillGroups()]);
            renderTabs();
            
            showResult(`✅ 设备管理标签页功能测试完成！共加载 ${machines.length} 台设备，${skillGroups.length} 个技能组。`, 'success');
        }

        init();
    </script>
</body>
</html>
