#!/bin/bash

# MES System Startup Script for External Access
echo "🚀 Starting MES System with External Access"
echo "============================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get IP addresses
PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "unknown")
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")

echo -e "${BLUE}📡 Server Access Information:${NC}"
echo "   Local Access:"
echo "     Backend:  http://localhost:8080"
echo "     Frontend: http://localhost:3000"
echo ""
echo "   Network Access:"
if [ "$LOCAL_IP" != "unknown" ]; then
    echo "     Backend:  http://$LOCAL_IP:8080"
    echo "     Frontend: http://$LOCAL_IP:3000"
fi
if [ "$PUBLIC_IP" != "unknown" ] && [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
    echo "     Backend:  http://$PUBLIC_IP:8080"
    echo "     Frontend: http://$PUBLIC_IP:3000"
fi
echo ""

# Start backend
echo -e "${GREEN}🔧 Starting Backend Server...${NC}"
cargo run &
BACKEND_PID=$!

# Wait for backend to start
sleep 5

# Start frontend
echo -e "${GREEN}🎨 Starting Frontend Server...${NC}"
cd frontend
npm run dev -- --host 0.0.0.0 &
FRONTEND_PID=$!

echo ""
echo -e "${GREEN}✅ MES System Started Successfully!${NC}"
echo ""
echo "🔗 Access URLs:"
echo "   Local: http://localhost:3000"
if [ "$LOCAL_IP" != "unknown" ]; then
    echo "   Network: http://$LOCAL_IP:3000"
fi
if [ "$PUBLIC_IP" != "unknown" ] && [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
    echo "   Public: http://$PUBLIC_IP:3000"
fi
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for interrupt
trap 'echo ""; echo "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
