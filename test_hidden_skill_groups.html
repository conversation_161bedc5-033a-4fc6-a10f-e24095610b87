<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技能组隐藏功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .skill-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 3px;
        }
        .hidden { background-color: #ffebee; }
        .visible { background-color: #e8f5e8; }
        button {
            padding: 8px 12px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .success { background-color: #4CAF50; color: white; }
        .danger { background-color: #f44336; color: white; }
        .info { background-color: #2196F3; color: white; }
        .warning { background-color: #ff9800; color: white; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            background-color: #f5f5f5;
        }
        kbd {
            background-color: #f1f1f1;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>设备管理技能组隐藏功能测试</h1>
    
    <div class="test-section">
        <h3>📋 当前技能组状态</h3>
        <button class="info" onclick="loadSkillGroups()">刷新技能组列表</button>
        <div id="skill-groups-list" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔧 隐藏功能说明</h3>
        <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px;">
            <h4>功能特性：</h4>
            <ul>
                <li><strong>自动隐藏</strong>：Assembly（装配）、Quality Control（质检）、钳工 等手工操作部门</li>
                <li><strong>管理界面</strong>：按 <kbd>Ctrl+Shift+H</kbd> 打开隐藏管理界面</li>
                <li><strong>动态控制</strong>：可以实时切换技能组的显示/隐藏状态</li>
                <li><strong>数据保护</strong>：隐藏不影响数据，只是界面显示</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 测试步骤</h3>
        <ol>
            <li>打开设备管理页面：<a href="http://localhost:3000/machines" target="_blank">http://localhost:3000/machines</a></li>
            <li>观察左侧分组导航，应该看不到"装配"、"质检"、"钳工"等分组</li>
            <li>按 <kbd>Ctrl+Shift+H</kbd> 打开隐藏管理界面</li>
            <li>在管理界面中切换技能组的显示/隐藏状态</li>
            <li>观察左侧导航的实时变化</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🧪 API测试</h3>
        <button class="success" onclick="testVisibleGroups()">测试可见技能组过滤</button>
        <button class="warning" onclick="testHiddenGroups()">测试隐藏技能组列表</button>
        <div id="api-test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>📊 预期结果</h3>
        <div style="background-color: #f3e5f5; padding: 15px; border-radius: 5px;">
            <h4>应该隐藏的技能组：</h4>
            <div class="skill-group hidden">
                <span>🔧 Assembly（装配）- 手工操作为主</span>
                <span style="color: #d32f2f;">已隐藏</span>
            </div>
            <div class="skill-group hidden">
                <span>🔍 Quality Control（质检）- 检测工具为主</span>
                <span style="color: #d32f2f;">已隐藏</span>
            </div>
            <div class="skill-group hidden">
                <span>🔨 钳工 - 手工操作</span>
                <span style="color: #d32f2f;">已隐藏</span>
            </div>
            
            <h4 style="margin-top: 20px;">应该显示的技能组：</h4>
            <div class="skill-group visible">
                <span>⚙️ CNC Machining（数控加工）</span>
                <span style="color: #388e3c;">显示中</span>
            </div>
            <div class="skill-group visible">
                <span>🔄 Milling（铣削）</span>
                <span style="color: #388e3c;">显示中</span>
            </div>
            <div class="skill-group visible">
                <span>🌀 Turning（车削）</span>
                <span style="color: #388e3c;">显示中</span>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        // 隐藏的技能组配置（与前端保持一致）
        const hiddenSkillGroups = [
            'Assembly',
            'Quality Control', 
            '钳工'
        ];

        async function loadSkillGroups() {
            try {
                const response = await fetch(`${API_BASE}/skill-groups`);
                const skillGroups = await response.json();
                
                const listDiv = document.getElementById('skill-groups-list');
                let html = '<h4>所有技能组：</h4>';
                
                skillGroups.forEach(group => {
                    const isHidden = hiddenSkillGroups.includes(group.group_name);
                    const statusClass = isHidden ? 'hidden' : 'visible';
                    const statusText = isHidden ? '应该隐藏' : '应该显示';
                    const statusColor = isHidden ? '#d32f2f' : '#388e3c';
                    
                    html += `
                        <div class="skill-group ${statusClass}">
                            <span><strong>${group.id}</strong>: ${group.group_name}</span>
                            <span style="color: ${statusColor};">${statusText}</span>
                        </div>
                    `;
                });
                
                listDiv.innerHTML = html;
            } catch (error) {
                document.getElementById('skill-groups-list').innerHTML = 
                    `<span style="color: red;">加载失败: ${error.message}</span>`;
            }
        }

        async function testVisibleGroups() {
            try {
                const response = await fetch(`${API_BASE}/skill-groups`);
                const allGroups = await response.json();
                
                // 模拟前端过滤逻辑
                const visibleGroups = allGroups.filter(group => 
                    !hiddenSkillGroups.includes(group.group_name)
                );
                
                const resultDiv = document.getElementById('api-test-result');
                resultDiv.innerHTML = `
                    <h4>✅ 可见技能组过滤测试结果：</h4>
                    <p><strong>总技能组数：</strong> ${allGroups.length}</p>
                    <p><strong>可见技能组数：</strong> ${visibleGroups.length}</p>
                    <p><strong>隐藏技能组数：</strong> ${allGroups.length - visibleGroups.length}</p>
                    <div style="margin-top: 10px;">
                        <strong>可见技能组：</strong><br>
                        ${visibleGroups.map(g => `• ${g.group_name}`).join('<br>')}
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-test-result').innerHTML = 
                    `<span style="color: red;">测试失败: ${error.message}</span>`;
            }
        }

        async function testHiddenGroups() {
            try {
                const response = await fetch(`${API_BASE}/skill-groups`);
                const allGroups = await response.json();
                
                const hiddenGroups = allGroups.filter(group => 
                    hiddenSkillGroups.includes(group.group_name)
                );
                
                const resultDiv = document.getElementById('api-test-result');
                resultDiv.innerHTML = `
                    <h4>🔒 隐藏技能组测试结果：</h4>
                    <p><strong>配置的隐藏规则：</strong> ${hiddenSkillGroups.join(', ')}</p>
                    <p><strong>实际隐藏的技能组：</strong> ${hiddenGroups.length} 个</p>
                    <div style="margin-top: 10px;">
                        <strong>隐藏的技能组：</strong><br>
                        ${hiddenGroups.map(g => `• ${g.group_name} (ID: ${g.id})`).join('<br>')}
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-test-result').innerHTML = 
                    `<span style="color: red;">测试失败: ${error.message}</span>`;
            }
        }

        // 页面加载时自动加载技能组
        window.onload = () => {
            loadSkillGroups();
        };
    </script>
</body>
</html>
