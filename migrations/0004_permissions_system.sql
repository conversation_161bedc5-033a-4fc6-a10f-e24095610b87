-- 权限系统数据库设计
-- 支持动态权限配置和基于角色的访问控制

-- 权限定义表
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL DEFAULT 'general',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted BOOLEAN NOT NULL DEFAULT true,
    granted_by INTEGER REFERENCES users(id),
    granted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- 为角色表添加权限配置相关字段
ALTER TABLE roles ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE roles ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE roles ADD COLUMN IF NOT EXISTS role_type VARCHAR(20) DEFAULT 'custom';
ALTER TABLE roles ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();

-- 更新现有角色为系统角色
UPDATE roles SET role_type = 'system' WHERE role_name IN ('admin', 'process_engineer', 'planner', 'operator', 'quality_inspector');

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_permissions_code ON permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category);
CREATE INDEX IF NOT EXISTS idx_permissions_is_active ON permissions(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_granted ON role_permissions(granted);

-- 插入基础权限定义
INSERT INTO permissions (permission_code, permission_name, description, category) VALUES
-- 页面访问权限
('PAGE_DASHBOARD', '访问仪表板', '允许访问系统仪表板页面', 'page'),
('PAGE_PROJECTS', '访问项目管理', '允许访问项目管理页面', 'page'),
('PAGE_PARTS', '访问零件管理', '允许访问零件管理页面', 'page'),
('PAGE_MACHINES', '访问设备管理', '允许访问设备管理页面', 'page'),
('PAGE_WORK_ORDERS', '访问工单管理', '允许访问工单管理页面', 'page'),
('PAGE_PLAN_TASKS', '访问生产计划', '允许访问生产计划页面', 'page'),
('PAGE_EXECUTION', '访问生产执行', '允许访问生产执行页面', 'page'),
('PAGE_OPERATOR_EXECUTION', '访问操作员执行', '允许访问操作员专用执行页面', 'page'),
('PAGE_QUALITY', '访问质量管理', '允许访问质量管理页面', 'page'),
('PAGE_BOM', '访问BOM管理', '允许访问BOM管理页面', 'page'),
('PAGE_ROUTINGS', '访问工艺路径', '允许访问工艺路径管理页面', 'page'),
('PAGE_USERS', '访问用户管理', '允许访问用户管理页面', 'page'),
('PAGE_ROLE_PERMISSIONS', '访问角色权限', '允许访问角色权限管理页面', 'page'),

-- 创建权限
('CREATE_PROJECT', '创建项目', '允许创建新项目', 'create'),
('CREATE_PART', '创建零件', '允许创建新零件', 'create'),
('CREATE_MACHINE', '创建设备', '允许创建新设备', 'create'),
('CREATE_WORK_ORDER', '创建工单', '允许创建新工单', 'create'),
('CREATE_PLAN_TASK', '创建计划任务', '允许创建新的生产计划任务', 'create'),
('CREATE_ROUTING', '创建工艺路径', '允许创建新的工艺路径', 'create'),
('CREATE_USER', '创建用户', '允许创建新用户', 'create'),
('CREATE_ROLE', '创建角色', '允许创建新角色', 'create'),

-- 编辑权限
('EDIT_PROJECT', '编辑项目', '允许编辑项目信息', 'edit'),
('EDIT_PART', '编辑零件', '允许编辑零件信息', 'edit'),
('EDIT_MACHINE', '编辑设备', '允许编辑设备信息', 'edit'),
('EDIT_WORK_ORDER', '编辑工单', '允许编辑工单信息', 'edit'),
('EDIT_PLAN_TASK', '编辑计划任务', '允许编辑生产计划任务', 'edit'),
('EDIT_ROUTING', '编辑工艺路径', '允许编辑工艺路径', 'edit'),
('EDIT_USER', '编辑用户', '允许编辑用户信息', 'edit'),
('EDIT_ROLE', '编辑角色', '允许编辑角色信息', 'edit'),

-- 删除权限
('DELETE_PROJECT', '删除项目', '允许删除项目', 'delete'),
('DELETE_PART', '删除零件', '允许删除零件', 'delete'),
('DELETE_MACHINE', '删除设备', '允许删除设备', 'delete'),
('DELETE_WORK_ORDER', '删除工单', '允许删除工单', 'delete'),
('DELETE_PLAN_TASK', '删除计划任务', '允许删除生产计划任务', 'delete'),
('DELETE_ROUTING', '删除工艺路径', '允许删除工艺路径', 'delete'),
('DELETE_USER', '删除用户', '允许删除用户', 'delete'),
('DELETE_ROLE', '删除角色', '允许删除角色', 'delete'),

-- 操作权限
('UPDATE_MACHINE_STATUS', '更新设备状态', '允许更新设备运行状态', 'operation'),
('SUBMIT_QUALITY_DATA', '提交质量数据', '允许提交质量检验数据', 'operation'),
('EXECUTE_TASK', '执行任务', '允许执行生产任务', 'operation'),
('START_TASK', '开始任务', '允许开始生产任务', 'operation'),
('COMPLETE_TASK', '完成任务', '允许完成生产任务', 'operation'),
('PAUSE_TASK', '暂停任务', '允许暂停生产任务', 'operation'),

-- 管理权限
('MANAGE_USERS', '管理用户', '允许管理系统用户', 'management'),
('MANAGE_ROLES', '管理角色', '允许管理系统角色', 'management'),
('MANAGE_SKILLS', '管理技能组', '允许管理技能组', 'management'),
('MANAGE_PERMISSIONS', '管理权限', '允许管理系统权限配置', 'management'),
('VIEW_AUDIT_LOGS', '查看审计日志', '允许查看系统审计日志', 'management'),
('EXPORT_DATA', '导出数据', '允许导出系统数据', 'management')
ON CONFLICT (permission_code) DO NOTHING;

-- 为现有角色分配默认权限
-- 管理员角色 - 拥有所有权限
INSERT INTO role_permissions (role_id, permission_id, granted)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE r.role_name = 'admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 工艺工程师角色权限
INSERT INTO role_permissions (role_id, permission_id, granted)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE r.role_name = 'process_engineer'
AND p.permission_code IN (
    'PAGE_DASHBOARD', 'PAGE_PROJECTS', 'PAGE_PARTS', 'PAGE_MACHINES', 'PAGE_WORK_ORDERS',
    'PAGE_PLAN_TASKS', 'PAGE_EXECUTION', 'PAGE_QUALITY', 'PAGE_BOM', 'PAGE_ROUTINGS',
    'CREATE_PROJECT', 'CREATE_PART', 'CREATE_MACHINE', 'CREATE_ROUTING',
    'EDIT_PROJECT', 'EDIT_PART', 'EDIT_MACHINE', 'EDIT_ROUTING',
    'UPDATE_MACHINE_STATUS', 'MANAGE_SKILLS'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 生产计划员角色权限
INSERT INTO role_permissions (role_id, permission_id, granted)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE r.role_name = 'planner'
AND p.permission_code IN (
    'PAGE_DASHBOARD', 'PAGE_PROJECTS', 'PAGE_PARTS', 'PAGE_MACHINES', 'PAGE_WORK_ORDERS',
    'PAGE_PLAN_TASKS', 'PAGE_EXECUTION', 'PAGE_BOM', 'PAGE_ROUTINGS',
    'CREATE_WORK_ORDER', 'CREATE_PLAN_TASK',
    'EDIT_WORK_ORDER', 'EDIT_PLAN_TASK',
    'UPDATE_MACHINE_STATUS'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 操作员角色权限
INSERT INTO role_permissions (role_id, permission_id, granted)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE r.role_name = 'operator'
AND p.permission_code IN (
    'PAGE_DASHBOARD', 'PAGE_EXECUTION', 'PAGE_OPERATOR_EXECUTION', 'PAGE_QUALITY',
    'EXECUTE_TASK', 'START_TASK', 'COMPLETE_TASK', 'PAUSE_TASK',
    'SUBMIT_QUALITY_DATA'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 质量检验员角色权限
INSERT INTO role_permissions (role_id, permission_id, granted)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE r.role_name = 'quality_inspector'
AND p.permission_code IN (
    'PAGE_DASHBOARD', 'PAGE_QUALITY', 'PAGE_EXECUTION', 'PAGE_WORK_ORDERS',
    'SUBMIT_QUALITY_DATA'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;
