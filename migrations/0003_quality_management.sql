-- Quality Management Tables

-- Quality Inspections Table
CREATE TABLE quality_inspections (
    id SERIAL PRIMARY KEY,
    plan_task_id INTEGER NOT NULL REFERENCES plan_tasks(id) ON DELETE CASCADE,
    inspector_user_id INTEGER NOT NULL REFERENCES users(id),
    inspection_type VARCHAR(50) NOT NULL DEFAULT 'in_process',
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    result VARCHAR(50) NOT NULL DEFAULT 'pending',
    notes TEXT,
    inspection_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Quality Checkpoints Table
CREATE TABLE quality_checkpoints (
    id SERIAL PRIMARY KEY,
    routing_step_id INTEGER NOT NULL REFERENCES routings(id) ON DELETE CASCADE,
    checkpoint_name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    required BOOLEAN NOT NULL DEFAULT false,
    inspection_criteria TEXT,
    measurement_type VARCHAR(50) NOT NULL DEFAULT 'visual',
    tolerance_min DECIMAL(10,4),
    tolerance_max DECIMAL(10,4),
    unit_of_measure VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Quality Measurements Table
CREATE TABLE quality_measurements (
    id SERIAL PRIMARY KEY,
    inspection_id INTEGER NOT NULL REFERENCES quality_inspections(id) ON DELETE CASCADE,
    checkpoint_id INTEGER NOT NULL REFERENCES quality_checkpoints(id) ON DELETE CASCADE,
    measured_value DECIMAL(10,4),
    text_value TEXT,
    pass_fail VARCHAR(20) NOT NULL DEFAULT 'pending',
    notes TEXT,
    measured_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Quality Alerts Table
CREATE TABLE quality_alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    inspection_id INTEGER REFERENCES quality_inspections(id),
    part_id INTEGER REFERENCES parts(id),
    threshold_value DECIMAL(10,4),
    actual_value DECIMAL(10,4),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    acknowledged BOOLEAN NOT NULL DEFAULT false,
    acknowledged_by VARCHAR(255),
    acknowledged_at TIMESTAMPTZ
);

-- Indexes for better performance
CREATE INDEX idx_quality_inspections_plan_task_id ON quality_inspections(plan_task_id);
CREATE INDEX idx_quality_inspections_inspector_user_id ON quality_inspections(inspector_user_id);
CREATE INDEX idx_quality_inspections_status ON quality_inspections(status);
CREATE INDEX idx_quality_inspections_result ON quality_inspections(result);
CREATE INDEX idx_quality_inspections_created_at ON quality_inspections(created_at);

CREATE INDEX idx_quality_checkpoints_routing_step_id ON quality_checkpoints(routing_step_id);
CREATE INDEX idx_quality_checkpoints_required ON quality_checkpoints(required);

CREATE INDEX idx_quality_measurements_inspection_id ON quality_measurements(inspection_id);
CREATE INDEX idx_quality_measurements_checkpoint_id ON quality_measurements(checkpoint_id);
CREATE INDEX idx_quality_measurements_pass_fail ON quality_measurements(pass_fail);

CREATE INDEX idx_quality_alerts_alert_type ON quality_alerts(alert_type);
CREATE INDEX idx_quality_alerts_severity ON quality_alerts(severity);
CREATE INDEX idx_quality_alerts_acknowledged ON quality_alerts(acknowledged);
CREATE INDEX idx_quality_alerts_created_at ON quality_alerts(created_at);

-- Add some constraints
ALTER TABLE quality_inspections 
ADD CONSTRAINT chk_inspection_type 
CHECK (inspection_type IN ('incoming', 'in_process', 'final', 'first_article', 'random'));

ALTER TABLE quality_inspections 
ADD CONSTRAINT chk_inspection_status 
CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled'));

ALTER TABLE quality_inspections 
ADD CONSTRAINT chk_inspection_result 
CHECK (result IN ('pass', 'fail', 'conditional', 'pending'));

ALTER TABLE quality_checkpoints 
ADD CONSTRAINT chk_measurement_type 
CHECK (measurement_type IN ('visual', 'dimensional', 'functional', 'material'));

ALTER TABLE quality_measurements 
ADD CONSTRAINT chk_pass_fail 
CHECK (pass_fail IN ('pass', 'fail', 'pending'));

ALTER TABLE quality_alerts 
ADD CONSTRAINT chk_alert_severity 
CHECK (severity IN ('low', 'medium', 'high', 'critical'));
