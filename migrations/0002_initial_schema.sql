-- MES System Database Schema
-- Version: 2.3.1

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Roles table
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE
);

-- User roles association table
CREATE TABLE user_roles (
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

-- Skill groups/equipment types table
CREATE TABLE skill_groups (
    id SERIAL PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL UNIQUE
);

-- User skills association table
CREATE TABLE user_skills (
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    skill_group_id INTEGER NOT NULL REFERENCES skill_groups(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, skill_group_id)
);

-- Machines/equipment table
CREATE TABLE machines (
    id SERIAL PRIMARY KEY,
    machine_name VARCHAR(255) NOT NULL,
    skill_group_id INTEGER NOT NULL REFERENCES skill_groups(id),
    status VARCHAR(50) NOT NULL DEFAULT 'available'
);

-- Projects/molds table
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    project_name VARCHAR(255) NOT NULL,
    customer_name VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Parts master data/material library
CREATE TABLE parts (
    id SERIAL PRIMARY KEY,
    part_number VARCHAR(255) NOT NULL,
    part_name VARCHAR(255),
    version VARCHAR(50) NOT NULL,
    specifications TEXT,
    UNIQUE(part_number, version)
);

-- Project BOM (Bill of Materials)
CREATE TABLE project_boms (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    part_id INTEGER NOT NULL REFERENCES parts(id),
    quantity INTEGER NOT NULL CHECK (quantity > 0)
);

-- Work orders table
CREATE TABLE work_orders (
    id SERIAL PRIMARY KEY,
    project_bom_id INTEGER NOT NULL REFERENCES project_boms(id),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    due_date DATE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Process routing table
CREATE TABLE routings (
    id SERIAL PRIMARY KEY,
    part_id INTEGER NOT NULL REFERENCES parts(id),
    step_number INTEGER NOT NULL CHECK (step_number > 0),
    process_name VARCHAR(255) NOT NULL,
    work_instructions TEXT,
    standard_hours DECIMAL(10,2) CHECK (standard_hours >= 0),
    UNIQUE(part_id, step_number)
);

-- Production planning table
CREATE TABLE plan_tasks (
    id SERIAL PRIMARY KEY,
    work_order_id INTEGER NOT NULL REFERENCES work_orders(id),
    routing_step_id INTEGER NOT NULL REFERENCES routings(id),
    skill_group_id INTEGER NOT NULL REFERENCES skill_groups(id),
    planned_start TIMESTAMPTZ NOT NULL,
    planned_end TIMESTAMPTZ NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'planned',
    CHECK (planned_end > planned_start)
);

-- Execution logs table
CREATE TABLE execution_logs (
    id SERIAL PRIMARY KEY,
    plan_task_id INTEGER NOT NULL REFERENCES plan_tasks(id),
    machine_id INTEGER REFERENCES machines(id),
    user_id INTEGER NOT NULL REFERENCES users(id),
    event_type VARCHAR(50) NOT NULL,
    event_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    notes TEXT
);

-- Audit logs table
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    action_type VARCHAR(50) NOT NULL,
    target_entity VARCHAR(100) NOT NULL,
    target_id INTEGER NOT NULL,
    change_details JSONB,
    action_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_parts_part_number ON parts(part_number);
CREATE INDEX idx_parts_version ON parts(version);
CREATE INDEX idx_work_orders_status ON work_orders(status);
CREATE INDEX idx_plan_tasks_status ON plan_tasks(status);
CREATE INDEX idx_plan_tasks_planned_start ON plan_tasks(planned_start);
CREATE INDEX idx_execution_logs_event_time ON execution_logs(event_time);
CREATE INDEX idx_audit_logs_action_time ON audit_logs(action_time);
CREATE INDEX idx_audit_logs_target_entity ON audit_logs(target_entity);

-- Insert default roles
INSERT INTO roles (role_name) VALUES
    ('admin'),
    ('process_engineer'),
    ('planner'),
    ('operator'),
    ('quality_inspector');

-- Insert default skill groups
INSERT INTO skill_groups (group_name) VALUES
    ('CNC Machining'),
    ('Milling'),
    ('Turning'),
    ('Grinding'),
    ('Assembly'),
    ('Quality Control'),
    ('Packaging');

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password_hash, full_name, is_active) VALUES
    ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlJO', 'System Administrator', true);

-- Assign admin role to admin user
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u, roles r
WHERE u.username = 'admin' AND r.role_name = 'admin';
