use axum::{
    Router,
    middleware::from_fn_with_state,
    routing::{get, post},
};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod handlers;
mod middleware;
mod models;
mod services;
mod utils;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Load environment variables
    dotenvy::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "mes_system=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Initialize database connection
    tracing::info!("Connecting to database...");
    let pool = utils::database::create_pool()
        .await
        .expect("Failed to create database pool");

    // Run migrations
    tracing::info!("Running database migrations...");
    utils::database::run_migrations(&pool)
        .await
        .expect("Failed to run migrations");

    tracing::info!("Starting MES System...");

    // Protected routes that require authentication
    let protected_routes = Router::new()
        .route("/api/auth/me", get(handlers::auth::get_current_user))
        .route("/api/auth/users", post(handlers::auth::create_user))
        // User management routes
        .route("/api/users", get(handlers::users::get_all_users))
        .route("/api/users/:id", get(handlers::users::get_user_by_id))
        .route(
            "/api/users/:id/status",
            post(handlers::users::update_user_status),
        )
        .route(
            "/api/users/:id/roles",
            post(handlers::users::update_user_roles),
        )
        .route(
            "/api/users/:id/skills",
            post(handlers::users::update_user_skills),
        )
        .route(
            "/api/users/:id",
            axum::routing::delete(handlers::users::delete_user),
        )
        // Machine management routes
        .route("/api/machines", get(handlers::machines::get_all_machines))
        .route("/api/machines", post(handlers::machines::create_machine))
        .route(
            "/api/machines/:id",
            get(handlers::machines::get_machine_by_id),
        )
        .route(
            "/api/machines/:id",
            axum::routing::put(handlers::machines::update_machine),
        )
        .route(
            "/api/machines/:id",
            axum::routing::delete(handlers::machines::delete_machine),
        )
        // Role management routes
        .route("/api/roles", post(handlers::roles::create_role))
        .route("/api/roles/:id", axum::routing::put(handlers::roles::update_role))
        .route("/api/roles/:id", axum::routing::delete(handlers::roles::delete_role))
        .route("/api/roles/:id/dependencies", get(handlers::roles::check_role_dependencies))
        // Permission management routes
        .route("/api/permissions", get(handlers::permissions::get_all_permissions))
        .route("/api/permissions", post(handlers::permissions::create_permission))
        .route("/api/roles/:id/permissions", get(handlers::permissions::get_role_permissions))
        .route("/api/roles/:id/permissions", axum::routing::put(handlers::permissions::update_role_permissions))
        // Skill group management routes
        .route("/api/skill-groups", get(handlers::auth::get_skill_groups))
        .route("/api/skill-groups", post(handlers::skill_groups::create_skill_group))
        .route("/api/skill-groups/:id", axum::routing::put(handlers::skill_groups::update_skill_group))
        .route("/api/skill-groups/:id", axum::routing::delete(handlers::skill_groups::delete_skill_group))
        .route("/api/skill-groups/:id/dependencies", get(handlers::skill_groups::check_skill_group_dependencies))
        .route(
            "/api/machines/:id/status",
            post(handlers::machines::update_machine_status),
        )
        // Part management routes
        .route("/api/parts", get(handlers::parts::get_all_parts))
        .route("/api/parts", post(handlers::parts::create_part))
        .route("/api/parts/:id", get(handlers::parts::get_part_by_id))
        .route(
            "/api/parts/:id",
            axum::routing::put(handlers::parts::update_part),
        )
        .route(
            "/api/parts/:id",
            axum::routing::delete(handlers::parts::delete_part),
        )
        // Project management routes
        .route("/api/projects", get(handlers::projects::get_all_projects))
        .route("/api/projects", post(handlers::projects::create_project))
        .route(
            "/api/projects/:id",
            get(handlers::projects::get_project_by_id),
        )
        .route(
            "/api/projects/:id/full",
            get(handlers::projects::get_project_with_bom),
        )
        .route(
            "/api/projects/:id",
            axum::routing::put(handlers::projects::update_project),
        )
        .route(
            "/api/projects/:id",
            axum::routing::delete(handlers::projects::delete_project),
        )
        // BOM management routes
        .route(
            "/api/projects/:id/bom",
            get(handlers::projects::get_project_bom),
        )
        .route(
            "/api/projects/:id/bom",
            post(handlers::projects::add_bom_item),
        )
        .route(
            "/api/bom/:id",
            axum::routing::put(handlers::projects::update_bom_item),
        )
        .route(
            "/api/bom/:id",
            axum::routing::delete(handlers::projects::remove_bom_item),
        )
        // Routing management routes
        .route("/api/routings", get(handlers::routings::get_all_routings))
        .route("/api/routings", post(handlers::routings::create_routing))
        .route("/api/routings/:id", get(handlers::routings::get_routing_by_id))
        .route("/api/routings/:id", axum::routing::put(handlers::routings::update_routing))
        .route("/api/routings/:id", axum::routing::delete(handlers::routings::delete_routing))
        .route("/api/parts/:id/routing", get(handlers::routings::get_part_routing))
        .route("/api/parts/:id/routing/reorder", post(handlers::routings::reorder_routing_steps))
        .route("/api/parts/:id/routing/copy", post(handlers::routings::copy_routing))
        // Work order management routes
        .route(
            "/api/work-orders",
            get(handlers::work_orders::get_all_work_orders),
        )
        .route(
            "/api/work-orders",
            post(handlers::work_orders::create_work_order),
        )
        .route(
            "/api/work-orders/:id",
            get(handlers::work_orders::get_work_order_by_id),
        )
        .route(
            "/api/work-orders/:id",
            axum::routing::put(handlers::work_orders::update_work_order),
        )
        .route(
            "/api/work-orders/:id",
            axum::routing::delete(handlers::work_orders::delete_work_order),
        )
        .route(
            "/api/work-orders/:id/status",
            post(handlers::work_orders::update_work_order_status),
        )
        .route(
            "/api/projects/:id/work-orders",
            get(handlers::work_orders::get_work_orders_by_project),
        )
        .route(
            "/api/projects/work-orders/create",
            post(handlers::work_orders::create_work_orders_from_project),
        )
        // Production planning routes
        .route(
            "/api/plan-tasks/:id",
            get(handlers::plan_tasks::get_plan_task_by_id),
        )
        .route(
            "/api/plan-tasks/:id",
            axum::routing::put(handlers::plan_tasks::update_plan_task),
        )
        .route(
            "/api/plan-tasks/:id",
            axum::routing::delete(handlers::plan_tasks::delete_plan_task),
        )
        .route(
            "/api/plan-tasks/:id/status",
            post(handlers::plan_tasks::update_plan_task_status),
        )
        .route(
            "/api/plan-tasks/:id/reschedule",
            post(handlers::plan_tasks::reschedule_plan_task),
        )
        .route(
            "/api/work-orders/:id/plan-tasks",
            post(handlers::plan_tasks::create_plan_tasks_from_work_order),
        )
        .route(
            "/api/planning/gantt",
            get(handlers::plan_tasks::get_gantt_chart_data),
        )
        .route(
            "/api/planning/plan-tasks",
            get(handlers::plan_tasks::get_all_plan_tasks),
        )
        // Execution tracking routes
        .route(
            "/api/execution/logs",
            get(handlers::execution::get_execution_logs),
        )
        .route(
            "/api/execution/logs",
            post(handlers::execution::create_execution_log),
        )
        .route(
            "/api/execution/tasks/start",
            post(handlers::execution::start_task),
        )
        .route(
            "/api/execution/tasks/complete",
            post(handlers::execution::complete_task),
        )
        .route(
            "/api/execution/tasks/pause",
            post(handlers::execution::pause_task),
        )
        .route(
            "/api/execution/tasks/resume",
            post(handlers::execution::resume_task),
        )
        .route(
            "/api/execution/barcode/validate",
            post(handlers::execution::validate_barcode),
        )
        .route(
            "/api/execution/tasks/active",
            get(handlers::execution::get_active_tasks),
        )
        .route(
            "/api/execution/dashboard",
            get(handlers::execution::get_shop_floor_dashboard),
        )
        .route(
            "/api/execution/my-tasks",
            get(handlers::execution::get_my_skill_group_tasks),
        )
        // Dashboard and reporting routes
        .route(
            "/api/dashboard/overview",
            get(handlers::dashboard::get_dashboard_overview),
        )
        .route(
            "/api/dashboard/production-summary",
            get(handlers::dashboard::get_production_summary),
        )
        .route(
            "/api/dashboard/work-order-status",
            get(handlers::dashboard::get_work_order_status),
        )
        .route(
            "/api/dashboard/machine-utilization",
            get(handlers::dashboard::get_machine_utilization),
        )
        .route(
            "/api/dashboard/skill-group-performance",
            get(handlers::dashboard::get_skill_group_performance),
        )
        .route(
            "/api/dashboard/recent-activities",
            get(handlers::dashboard::get_recent_activities),
        )
        .route(
            "/api/dashboard/kpi-metrics",
            get(handlers::dashboard::get_kpi_metrics),
        )
        .route(
            "/api/dashboard/trends",
            get(handlers::dashboard::get_trend_data),
        )
        .route(
            "/api/reports/production",
            post(handlers::dashboard::get_production_report),
        )
        // Quality management routes
        .route(
            "/api/quality/inspections",
            get(handlers::quality::get_all_quality_inspections),
        )
        .route(
            "/api/quality/inspections",
            post(handlers::quality::create_quality_inspection),
        )
        .route(
            "/api/quality/inspections/:id",
            get(handlers::quality::get_quality_inspection_by_id),
        )
        .route(
            "/api/quality/inspections/:id",
            axum::routing::put(handlers::quality::update_quality_inspection),
        )
        .route(
            "/api/quality/checkpoints",
            post(handlers::quality::create_quality_checkpoint),
        )
        .route(
            "/api/quality/reports",
            post(handlers::quality::get_quality_report),
        )
        .route(
            "/api/quality/metrics",
            get(handlers::quality::get_quality_metrics),
        )
        .route(
            "/api/quality/pending",
            get(handlers::quality::get_pending_inspections),
        )
        // Audit logging routes
        .route("/api/audit/logs", get(handlers::audit::get_audit_logs))
        .route(
            "/api/audit/trail/:entity_type/:entity_id",
            get(handlers::audit::get_audit_trail),
        )
        .route(
            "/api/audit/summary",
            get(handlers::audit::get_audit_summary),
        )
        .route(
            "/api/audit/statistics",
            get(handlers::audit::get_audit_statistics),
        )
        .route(
            "/api/audit/reports",
            post(handlers::audit::generate_audit_report),
        )
        .route("/api/audit/export", get(handlers::audit::export_audit_logs))
        .route(
            "/api/audit/cleanup",
            post(handlers::audit::cleanup_old_audit_logs),
        )
        .layer(from_fn_with_state(
            pool.clone(),
            middleware::auth::auth_middleware,
        ));

    // Build our application with routes
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(health_check))
        // Public auth routes
        .route("/api/auth/login", post(handlers::auth::login))
        .route("/api/auth/roles", get(handlers::auth::get_roles))
        .route(
            "/api/auth/skill-groups",
            get(handlers::auth::get_skill_groups),
        )
        // Temporary public plan-tasks routes for testing
        .route(
            "/api/plan-tasks",
            get(handlers::plan_tasks::get_all_plan_tasks_public)
                .post(handlers::plan_tasks::create_plan_task_public),
        )
        // Merge protected routes
        .merge(protected_routes)
        .with_state(pool)
        .layer(CorsLayer::permissive());

    // Get server configuration from environment
    let host = std::env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = std::env::var("SERVER_PORT")
        .unwrap_or_else(|_| "8080".to_string())
        .parse::<u16>()?;

    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    tracing::info!("Server listening on {}:{}", host, port);

    // Run the server
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

async fn root() -> &'static str {
    "MES System API - Running"
}

async fn health_check() -> &'static str {
    "OK"
}
