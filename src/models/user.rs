use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: i32,
    pub username: String,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: i32,
    pub role_name: String,
    pub description: Option<String>,
    pub is_active: bool,
    pub role_type: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct SkillGroup {
    pub id: i32,
    pub group_name: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Permission {
    pub id: i32,
    pub permission_code: String,
    pub permission_name: String,
    pub description: Option<String>,
    pub category: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct RolePermission {
    pub id: i32,
    pub role_id: i32,
    pub permission_id: i32,
    pub granted: bool,
    pub granted_by: Option<i32>,
    pub granted_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleWithPermissions {
    pub id: i32,
    pub role_name: String,
    pub description: Option<String>,
    pub is_active: bool,
    pub role_type: String,
    pub created_at: DateTime<Utc>,
    pub permissions: Vec<PermissionInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionInfo {
    pub id: i32,
    pub permission_code: String,
    pub permission_name: String,
    pub description: Option<String>,
    pub category: String,
    pub granted: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserWithRolesAndSkills {
    pub id: i32,
    pub username: String,
    pub full_name: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
}
