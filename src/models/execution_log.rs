use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct ExecutionLog {
    pub id: i32,
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub user_id: i32,
    pub event_type: String,
    pub event_time: DateTime<Utc>,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionLogWithDetails {
    pub id: i32,
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub user_id: i32,
    pub event_type: String,
    pub event_time: DateTime<Utc>,
    pub notes: Option<String>,
    // Plan task details
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub task_status: String,
    // Work order details
    pub work_order_quantity: i32,
    pub work_order_status: String,
    // Project details
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    // Part details
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    // Routing step details
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    // User details
    pub username: String,
    pub user_full_name: Option<String>,
    // Machine details (if applicable)
    pub machine_name: Option<String>,
    pub skill_group_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateExecutionLogRequest {
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub event_type: String,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionLogQuery {
    pub plan_task_id: Option<i32>,
    pub work_order_id: Option<i32>,
    pub user_id: Option<i32>,
    pub machine_id: Option<i32>,
    pub event_type: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionLogSearchResult {
    pub execution_logs: Vec<ExecutionLogWithDetails>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskExecutionRequest {
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub barcode: Option<String>, // For barcode scanning simulation
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskExecutionResponse {
    pub success: bool,
    pub message: String,
    pub execution_log: Option<ExecutionLog>,
    pub updated_task_status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BarcodeValidationRequest {
    pub barcode: String,
    pub expected_part_number: Option<String>,
    pub expected_work_order_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BarcodeValidationResponse {
    pub valid: bool,
    pub message: String,
    pub part_number: Option<String>,
    pub work_order_id: Option<i32>,
    pub plan_task_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ActiveTasksResponse {
    pub active_tasks: Vec<ActiveTaskInfo>,
    pub user_id: i32,
    pub username: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ActiveTaskInfo {
    pub plan_task_id: i32,
    pub work_order_id: i32,
    pub part_number: String,
    pub process_name: String,
    pub machine_name: Option<String>,
    pub started_at: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskProgressUpdate {
    pub plan_task_id: i32,
    pub progress_percentage: f32, // 0.0 to 100.0
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ShopFloorDashboard {
    pub active_tasks: Vec<ActiveTaskInfo>,
    pub pending_tasks: Vec<PendingTaskInfo>,
    pub completed_today: i64,
    pub machine_status: Vec<MachineStatusInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PendingTaskInfo {
    pub plan_task_id: i32,
    pub work_order_id: i32,
    pub part_number: String,
    pub process_name: String,
    pub planned_start: DateTime<Utc>,
    pub priority: String, // "high", "medium", "low"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineStatusInfo {
    pub machine_id: i32,
    pub machine_name: String,
    pub status: String,
    pub current_task: Option<String>,
    pub skill_group_name: String,
}

// Execution event type constants
pub const EVENT_TYPE_TASK_START: &str = "task_start";
pub const EVENT_TYPE_TASK_PAUSE: &str = "task_pause";
pub const EVENT_TYPE_TASK_RESUME: &str = "task_resume";
pub const EVENT_TYPE_TASK_COMPLETE: &str = "task_complete";
pub const EVENT_TYPE_TASK_CANCEL: &str = "task_cancel";
pub const EVENT_TYPE_QUALITY_CHECK: &str = "quality_check";
pub const EVENT_TYPE_MACHINE_SETUP: &str = "machine_setup";
pub const EVENT_TYPE_MATERIAL_SCAN: &str = "material_scan";
pub const EVENT_TYPE_PROGRESS_UPDATE: &str = "progress_update";

pub fn get_valid_event_types() -> Vec<&'static str> {
    vec![
        EVENT_TYPE_TASK_START,
        EVENT_TYPE_TASK_PAUSE,
        EVENT_TYPE_TASK_RESUME,
        EVENT_TYPE_TASK_COMPLETE,
        EVENT_TYPE_TASK_CANCEL,
        EVENT_TYPE_QUALITY_CHECK,
        EVENT_TYPE_MACHINE_SETUP,
        EVENT_TYPE_MATERIAL_SCAN,
        EVENT_TYPE_PROGRESS_UPDATE,
    ]
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionSummary {
    pub plan_task_id: i32,
    pub total_time_minutes: i64,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub pause_count: i64,
    pub total_pause_time_minutes: i64,
    pub events: Vec<ExecutionLog>,
}
