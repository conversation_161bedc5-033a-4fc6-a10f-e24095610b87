use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, FromRow)]
pub struct Part {
    pub id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePartRequest {
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePartRequest {
    pub part_name: Option<String>,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PartSearchQuery {
    pub part_number: Option<String>,
    pub part_name: Option<String>,
    pub version: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PartSearchResult {
    pub parts: Vec<Part>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}
