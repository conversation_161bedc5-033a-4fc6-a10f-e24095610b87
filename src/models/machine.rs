use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, FromRow)]
pub struct Machine {
    pub id: i32,
    pub machine_name: String,
    pub skill_group_id: i32,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineWithSkillGroup {
    pub id: i32,
    pub machine_name: String,
    pub skill_group_id: i32,
    pub skill_group_name: String,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMachineRequest {
    pub machine_name: String,
    pub skill_group_id: i32,
    pub status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateMachineRequest {
    pub machine_name: Option<String>,
    pub skill_group_id: Option<i32>,
    pub status: Option<String>,
}
