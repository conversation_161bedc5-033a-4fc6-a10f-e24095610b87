use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AuditLog {
    pub id: i32,
    pub user_id: Option<i32>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: i32,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditLogWithDetails {
    pub id: i32,
    pub user_id: Option<i32>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: i32,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: DateTime<Utc>,
    // User details
    pub username: Option<String>,
    pub user_full_name: Option<String>,
    // Change summary
    pub changes_summary: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAuditLogRequest {
    pub user_id: Option<i32>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: i32,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditLogQuery {
    pub user_id: Option<i32>,
    pub action: Option<String>,
    pub entity_type: Option<String>,
    pub entity_id: Option<i32>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditLogSearchResult {
    pub audit_logs: Vec<AuditLogWithDetails>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditTrail {
    pub entity_type: String,
    pub entity_id: i32,
    pub audit_logs: Vec<AuditLogWithDetails>,
    pub total_changes: i64,
    pub first_change: Option<DateTime<Utc>>,
    pub last_change: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditSummary {
    pub total_actions: i64,
    pub actions_by_type: Vec<ActionTypeSummary>,
    pub actions_by_user: Vec<UserActionSummary>,
    pub actions_by_entity: Vec<EntityActionSummary>,
    pub recent_activities: Vec<AuditLogWithDetails>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ActionTypeSummary {
    pub action: String,
    pub count: i64,
    pub percentage: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserActionSummary {
    pub user_id: i32,
    pub username: String,
    pub full_name: Option<String>,
    pub action_count: i64,
    pub last_action: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EntityActionSummary {
    pub entity_type: String,
    pub action_count: i64,
    pub last_modified: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditReportRequest {
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub entity_types: Option<Vec<String>>,
    pub actions: Option<Vec<String>>,
    pub user_ids: Option<Vec<i32>>,
    pub include_details: bool,
    pub format: String, // "json", "csv", "pdf"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditReport {
    pub report_period: AuditReportPeriod,
    pub summary: AuditSummary,
    pub detailed_logs: Option<Vec<AuditLogWithDetails>>,
    pub generated_at: DateTime<Utc>,
    pub generated_by: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuditReportPeriod {
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub period_description: String,
}

// Audit action constants
#[allow(dead_code)]
pub const AUDIT_ACTION_CREATE: &str = "CREATE";
#[allow(dead_code)]
pub const AUDIT_ACTION_UPDATE: &str = "UPDATE";
#[allow(dead_code)]
pub const AUDIT_ACTION_DELETE: &str = "DELETE";
#[allow(dead_code)]
pub const AUDIT_ACTION_LOGIN: &str = "LOGIN";
#[allow(dead_code)]
pub const AUDIT_ACTION_LOGOUT: &str = "LOGOUT";
#[allow(dead_code)]
pub const AUDIT_ACTION_VIEW: &str = "VIEW";
#[allow(dead_code)]
pub const AUDIT_ACTION_EXPORT: &str = "EXPORT";
#[allow(dead_code)]
pub const AUDIT_ACTION_IMPORT: &str = "IMPORT";

// Entity type constants
#[allow(dead_code)]
pub const ENTITY_TYPE_USER: &str = "user";
#[allow(dead_code)]
pub const ENTITY_TYPE_PROJECT: &str = "project";
#[allow(dead_code)]
pub const ENTITY_TYPE_PART: &str = "part";
#[allow(dead_code)]
pub const ENTITY_TYPE_WORK_ORDER: &str = "work_order";
#[allow(dead_code)]
pub const ENTITY_TYPE_PLAN_TASK: &str = "plan_task";
#[allow(dead_code)]
pub const ENTITY_TYPE_EXECUTION_LOG: &str = "execution_log";
#[allow(dead_code)]
pub const ENTITY_TYPE_QUALITY_INSPECTION: &str = "quality_inspection";
#[allow(dead_code)]
pub const ENTITY_TYPE_MACHINE: &str = "machine";
#[allow(dead_code)]
pub const ENTITY_TYPE_SKILL_GROUP: &str = "skill_group";

#[allow(dead_code)]
pub fn get_valid_audit_actions() -> Vec<&'static str> {
    vec![
        AUDIT_ACTION_CREATE,
        AUDIT_ACTION_UPDATE,
        AUDIT_ACTION_DELETE,
        AUDIT_ACTION_LOGIN,
        AUDIT_ACTION_LOGOUT,
        AUDIT_ACTION_VIEW,
        AUDIT_ACTION_EXPORT,
        AUDIT_ACTION_IMPORT,
    ]
}

#[allow(dead_code)]
pub fn get_valid_entity_types() -> Vec<&'static str> {
    vec![
        ENTITY_TYPE_USER,
        ENTITY_TYPE_PROJECT,
        ENTITY_TYPE_PART,
        ENTITY_TYPE_WORK_ORDER,
        ENTITY_TYPE_PLAN_TASK,
        ENTITY_TYPE_EXECUTION_LOG,
        ENTITY_TYPE_QUALITY_INSPECTION,
        ENTITY_TYPE_MACHINE,
        ENTITY_TYPE_SKILL_GROUP,
    ]
}

// Helper function to generate change summary
#[allow(dead_code)]
pub fn generate_changes_summary(
    action: &str,
    old_values: &Option<serde_json::Value>,
    new_values: &Option<serde_json::Value>,
) -> String {
    match action {
        AUDIT_ACTION_CREATE => {
            if let Some(new_vals) = new_values {
                format!("Created with {} fields", count_fields(new_vals))
            } else {
                "Created".to_string()
            }
        }
        AUDIT_ACTION_UPDATE => {
            let changes = count_changes(old_values, new_values);
            if changes > 0 {
                format!("Updated {} field(s)", changes)
            } else {
                "Updated".to_string()
            }
        }
        AUDIT_ACTION_DELETE => "Deleted".to_string(),
        _ => action.to_string(),
    }
}

#[allow(dead_code)]
fn count_fields(value: &serde_json::Value) -> usize {
    match value {
        serde_json::Value::Object(map) => map.len(),
        _ => 1,
    }
}

#[allow(dead_code)]
fn count_changes(
    old_values: &Option<serde_json::Value>,
    new_values: &Option<serde_json::Value>,
) -> usize {
    match (old_values, new_values) {
        (Some(old), Some(new)) => {
            if let (serde_json::Value::Object(old_map), serde_json::Value::Object(new_map)) =
                (old, new)
            {
                let mut changes = 0;
                for (key, new_val) in new_map {
                    if let Some(old_val) = old_map.get(key) {
                        if old_val != new_val {
                            changes += 1;
                        }
                    } else {
                        changes += 1;
                    }
                }
                changes
            } else {
                1
            }
        }
        (None, Some(new)) => count_fields(new),
        (Some(_), None) => 1,
        (None, None) => 0,
    }
}

// Trait for entities that can be audited
#[allow(dead_code)]
pub trait Auditable {
    fn entity_type() -> &'static str;
    fn entity_id(&self) -> i32;
    fn to_audit_json(&self) -> serde_json::Value;
}

// Macro to help implement Auditable trait
#[macro_export]
macro_rules! impl_auditable {
    ($struct_name:ident, $entity_type:expr, $id_field:ident) => {
        impl Auditable for $struct_name {
            fn entity_type() -> &'static str {
                $entity_type
            }

            fn entity_id(&self) -> i32 {
                self.$id_field
            }

            fn to_audit_json(&self) -> serde_json::Value {
                serde_json::to_value(self).unwrap_or(serde_json::Value::Null)
            }
        }
    };
}
