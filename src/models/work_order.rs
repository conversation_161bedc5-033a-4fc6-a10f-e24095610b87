use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct WorkOrder {
    pub id: i32,
    pub project_bom_id: i32,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderWithDetails {
    pub id: i32,
    pub project_bom_id: i32,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
    // Project details
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    // Part details
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
    // BOM details
    pub bom_quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateWorkOrderRequest {
    pub project_bom_id: i32,
    pub quantity: i32,
    pub due_date: Option<NaiveDate>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateWorkOrderRequest {
    pub quantity: Option<i32>,
    pub status: Option<String>,
    pub due_date: Option<NaiveDate>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateWorkOrdersFromProjectRequest {
    pub project_id: i32,
    pub due_date: Option<NaiveDate>,
    pub multiplier: Option<f64>, // Optional multiplier for quantities (default 1.0)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderQuery {
    pub project_id: Option<i32>,
    pub status: Option<String>,
    pub part_id: Option<i32>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderSearchResult {
    pub work_orders: Vec<WorkOrderWithDetails>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderStatusUpdate {
    pub status: String,
}

// Work order status constants
pub const WORK_ORDER_STATUS_PENDING: &str = "pending";
pub const WORK_ORDER_STATUS_PLANNED: &str = "planned";
pub const WORK_ORDER_STATUS_IN_PROGRESS: &str = "in_progress";
pub const WORK_ORDER_STATUS_COMPLETED: &str = "completed";
pub const WORK_ORDER_STATUS_CANCELLED: &str = "cancelled";

pub fn get_valid_work_order_statuses() -> Vec<&'static str> {
    vec![
        WORK_ORDER_STATUS_PENDING,
        WORK_ORDER_STATUS_PLANNED,
        WORK_ORDER_STATUS_IN_PROGRESS,
        WORK_ORDER_STATUS_COMPLETED,
        WORK_ORDER_STATUS_CANCELLED,
    ]
}
