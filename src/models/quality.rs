use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct QualityInspection {
    pub id: i32,
    pub plan_task_id: i32,
    pub inspector_user_id: i32,
    pub inspection_type: String,
    pub status: String,
    pub result: String,
    pub notes: Option<String>,
    pub inspection_date: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityInspectionWithDetails {
    pub id: i32,
    pub plan_task_id: i32,
    pub inspector_user_id: i32,
    pub inspection_type: String,
    pub status: String,
    pub result: String,
    pub notes: Option<String>,
    pub inspection_date: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    // Plan task details
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    // Work order details
    pub work_order_quantity: i32,
    pub work_order_status: String,
    // Project details
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    // Part details
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    // Routing step details
    pub step_number: i32,
    pub process_name: String,
    // Inspector details
    pub inspector_username: String,
    pub inspector_full_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateQualityInspectionRequest {
    pub plan_task_id: i32,
    pub inspection_type: String,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateQualityInspectionRequest {
    pub status: Option<String>,
    pub result: Option<String>,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityInspectionQuery {
    pub plan_task_id: Option<i32>,
    pub work_order_id: Option<i32>,
    pub inspector_user_id: Option<i32>,
    pub inspection_type: Option<String>,
    pub status: Option<String>,
    pub result: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityInspectionSearchResult {
    pub inspections: Vec<QualityInspectionWithDetails>,
    pub total_count: i64,
    pub limit: i64,
    pub offset: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityCheckpoint {
    pub id: i32,
    pub routing_step_id: i32,
    pub checkpoint_name: String,
    pub description: Option<String>,
    pub required: bool,
    pub inspection_criteria: Option<String>,
    pub measurement_type: String, // "visual", "dimensional", "functional", "material"
    pub tolerance_min: Option<f64>,
    pub tolerance_max: Option<f64>,
    pub unit_of_measure: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateQualityCheckpointRequest {
    pub routing_step_id: i32,
    pub checkpoint_name: String,
    pub description: Option<String>,
    pub required: bool,
    pub inspection_criteria: Option<String>,
    pub measurement_type: String,
    pub tolerance_min: Option<f64>,
    pub tolerance_max: Option<f64>,
    pub unit_of_measure: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityMeasurement {
    pub id: i32,
    pub inspection_id: i32,
    pub checkpoint_id: i32,
    pub measured_value: Option<f64>,
    pub text_value: Option<String>,
    pub pass_fail: String,
    pub notes: Option<String>,
    pub measured_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateQualityMeasurementRequest {
    pub inspection_id: i32,
    pub checkpoint_id: i32,
    pub measured_value: Option<f64>,
    pub text_value: Option<String>,
    pub pass_fail: String,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityReport {
    pub report_period: QualityReportPeriod,
    pub overall_metrics: QualityOverallMetrics,
    pub inspection_summary: Vec<QualityInspectionSummary>,
    pub defect_analysis: Vec<DefectAnalysis>,
    pub trend_data: Vec<QualityTrendData>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityReportPeriod {
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub period_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityOverallMetrics {
    pub total_inspections: i64,
    pub passed_inspections: i64,
    pub failed_inspections: i64,
    pub pending_inspections: i64,
    pub pass_rate: f64,
    pub first_pass_yield: f64,
    pub defect_rate: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityInspectionSummary {
    pub inspection_type: String,
    pub total_count: i64,
    pub passed_count: i64,
    pub failed_count: i64,
    pub pass_rate: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DefectAnalysis {
    pub defect_type: String,
    pub defect_count: i64,
    pub affected_parts: Vec<String>,
    pub root_cause: Option<String>,
    pub corrective_action: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityTrendData {
    pub date: DateTime<Utc>,
    pub pass_rate: f64,
    pub defect_rate: f64,
    pub inspection_count: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityAlert {
    pub id: i32,
    pub alert_type: String,
    pub severity: String,
    pub title: String,
    pub description: String,
    pub inspection_id: Option<i32>,
    pub part_id: Option<i32>,
    pub threshold_value: Option<f64>,
    pub actual_value: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub acknowledged: bool,
    pub acknowledged_by: Option<String>,
    pub acknowledged_at: Option<DateTime<Utc>>,
}

// Quality inspection status constants
#[allow(dead_code)]
pub const INSPECTION_STATUS_PENDING: &str = "pending";
#[allow(dead_code)]
pub const INSPECTION_STATUS_IN_PROGRESS: &str = "in_progress";
#[allow(dead_code)]
pub const INSPECTION_STATUS_COMPLETED: &str = "completed";
#[allow(dead_code)]
pub const INSPECTION_STATUS_CANCELLED: &str = "cancelled";

// Quality inspection result constants
#[allow(dead_code)]
pub const INSPECTION_RESULT_PASS: &str = "pass";
#[allow(dead_code)]
pub const INSPECTION_RESULT_FAIL: &str = "fail";
#[allow(dead_code)]
pub const INSPECTION_RESULT_CONDITIONAL: &str = "conditional";
#[allow(dead_code)]
pub const INSPECTION_RESULT_PENDING: &str = "pending";

// Quality inspection type constants
#[allow(dead_code)]
pub const INSPECTION_TYPE_INCOMING: &str = "incoming";
#[allow(dead_code)]
pub const INSPECTION_TYPE_IN_PROCESS: &str = "in_process";
#[allow(dead_code)]
pub const INSPECTION_TYPE_FINAL: &str = "final";
#[allow(dead_code)]
pub const INSPECTION_TYPE_FIRST_ARTICLE: &str = "first_article";
#[allow(dead_code)]
pub const INSPECTION_TYPE_RANDOM: &str = "random";

// Measurement type constants
#[allow(dead_code)]
pub const MEASUREMENT_TYPE_VISUAL: &str = "visual";
#[allow(dead_code)]
pub const MEASUREMENT_TYPE_DIMENSIONAL: &str = "dimensional";
#[allow(dead_code)]
pub const MEASUREMENT_TYPE_FUNCTIONAL: &str = "functional";
#[allow(dead_code)]
pub const MEASUREMENT_TYPE_MATERIAL: &str = "material";

#[allow(dead_code)]
pub fn get_valid_inspection_statuses() -> Vec<&'static str> {
    vec![
        INSPECTION_STATUS_PENDING,
        INSPECTION_STATUS_IN_PROGRESS,
        INSPECTION_STATUS_COMPLETED,
        INSPECTION_STATUS_CANCELLED,
    ]
}

#[allow(dead_code)]
pub fn get_valid_inspection_results() -> Vec<&'static str> {
    vec![
        INSPECTION_RESULT_PASS,
        INSPECTION_RESULT_FAIL,
        INSPECTION_RESULT_CONDITIONAL,
        INSPECTION_RESULT_PENDING,
    ]
}

#[allow(dead_code)]
pub fn get_valid_inspection_types() -> Vec<&'static str> {
    vec![
        INSPECTION_TYPE_INCOMING,
        INSPECTION_TYPE_IN_PROCESS,
        INSPECTION_TYPE_FINAL,
        INSPECTION_TYPE_FIRST_ARTICLE,
        INSPECTION_TYPE_RANDOM,
    ]
}

#[allow(dead_code)]
pub fn get_valid_measurement_types() -> Vec<&'static str> {
    vec![
        MEASUREMENT_TYPE_VISUAL,
        MEASUREMENT_TYPE_DIMENSIONAL,
        MEASUREMENT_TYPE_FUNCTIONAL,
        MEASUREMENT_TYPE_MATERIAL,
    ]
}
