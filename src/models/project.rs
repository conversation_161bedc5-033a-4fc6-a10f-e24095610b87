use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Project {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub project_name: String,
    pub customer_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectRequest {
    pub project_name: Option<String>,
    pub customer_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ProjectBom {
    pub id: i32,
    pub project_id: i32,
    pub part_id: i32,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectBomWithDetails {
    pub id: i32,
    pub project_id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub quantity: i32,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectBomRequest {
    pub part_id: i32,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectBomRequest {
    pub quantity: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectWithBom {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub created_at: DateTime<Utc>,
    pub bom_items: Vec<ProjectBomWithDetails>,
}
