use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::audit_log::{AuditLogQuery, AuditReportRequest},
    services::audit_service::AuditService,
    utils::validation::ErrorResponse,
};

pub async fn get_audit_logs(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(query): Query<AuditLogQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can view audit logs
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can view audit logs".to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    match audit_service.get_audit_logs(query).await {
        Ok(result) => Ok(Json(serde_json::json!(result))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_audit_trail(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path((entity_type, entity_id)): Path<(String, i32)>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can view audit trails
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can view audit trails"
                    .to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    match audit_service.get_audit_trail(&entity_type, entity_id).await {
        Ok(trail) => Ok(Json(serde_json::json!(trail))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_audit_summary(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(_params): Query<serde_json::Value>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can view audit summary
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can view audit summary".to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    // Default to last 30 days if no dates provided
    let end_date = chrono::Utc::now();
    let start_date = end_date - chrono::Duration::days(30);

    match audit_service.get_audit_summary(start_date, end_date).await {
        Ok(summary) => Ok(Json(serde_json::json!(summary))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "summary_generation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn generate_audit_report(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<AuditReportRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can generate audit reports
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can generate audit reports".to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    match audit_service.generate_audit_report(request).await {
        Ok(report) => Ok(Json(serde_json::json!(report))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "report_generation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_audit_statistics(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can view audit statistics
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can view audit statistics".to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    match audit_service.get_audit_statistics().await {
        Ok(stats) => Ok(Json(stats)),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "statistics_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn export_audit_logs(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(mut query): Query<AuditLogQuery>,
    Query(params): Query<serde_json::Value>,
) -> Result<axum::response::Response, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can export audit logs
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can export audit logs".to_string(),
            }),
        ));
    }

    let format = params
        .get("format")
        .and_then(|v| v.as_str())
        .unwrap_or("json");

    // Set a reasonable limit for exports
    query.limit = Some(query.limit.unwrap_or(1000).min(10000));

    let audit_service = AuditService::new(pool);

    match audit_service.export_audit_logs(query, format).await {
        Ok(data) => {
            let content_type = match format {
                "csv" => "text/csv",
                "json" => "application/json",
                _ => "application/octet-stream",
            };

            let filename = format!(
                "audit_logs_{}.{}",
                chrono::Utc::now().format("%Y%m%d_%H%M%S"),
                format
            );

            Ok(axum::response::Response::builder()
                .status(StatusCode::OK)
                .header("Content-Type", content_type)
                .header(
                    "Content-Disposition",
                    format!("attachment; filename=\"{}\"", filename),
                )
                .body(axum::body::Body::from(data))
                .unwrap())
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "export_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn cleanup_old_audit_logs(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<serde_json::Value>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can cleanup audit logs
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can cleanup audit logs".to_string(),
            }),
        ));
    }

    let retention_days = params
        .get("retention_days")
        .and_then(|v| v.as_i64())
        .unwrap_or(365) as i32;

    if retention_days < 30 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_retention_period".to_string(),
                message: "Retention period must be at least 30 days".to_string(),
            }),
        ));
    }

    let audit_service = AuditService::new(pool);

    match audit_service.cleanup_old_logs(retention_days).await {
        Ok(deleted_count) => Ok(Json(serde_json::json!({
            "message": "Audit log cleanup completed",
            "deleted_count": deleted_count,
            "retention_days": retention_days
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "cleanup_failed".to_string(),
                message: error,
            }),
        )),
    }
}
