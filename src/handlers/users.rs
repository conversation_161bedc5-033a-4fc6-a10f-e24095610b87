use axum::{
    extract::{Extension, Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser, services::user_service::UserService,
    utils::validation::ErrorResponse,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserStatusRequest {
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserRolesRequest {
    pub role_ids: Vec<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateUserSkillsRequest {
    pub skill_group_ids: Vec<i32>,
}

pub async fn get_all_users(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Check if user has admin or process_engineer role
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can view users".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.get_all_users().await {
        Ok(users) => Ok(Json(serde_json::json!({ "users": users }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_user_by_id(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Check if user has admin role or is requesting their own info
    if !auth_user.roles.contains(&"admin".to_string()) && auth_user.id != user_id {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "You can only view your own user information".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.get_user_by_id(user_id).await {
        Ok(Some(user)) => Ok(Json(serde_json::json!({ "user": user }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "user_not_found".to_string(),
                message: "User not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserStatusRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can update user status
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update user status".to_string(),
            }),
        ));
    }

    // Prevent admin from deactivating themselves
    if auth_user.id == user_id && !request.is_active {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_operation".to_string(),
                message: "You cannot deactivate your own account".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_status(user_id, request.is_active)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User status updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_user(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete users
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete users".to_string(),
            }),
        ));
    }

    // Prevent admin from deleting themselves
    if auth_user.id == user_id {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_operation".to_string(),
                message: "You cannot delete your own account".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service.delete_user(user_id).await {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User deleted successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_roles(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserRolesRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can update user roles
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update user roles".to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_roles(user_id, request.role_ids)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User roles updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_user_skills(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(user_id): Path<i32>,
    Json(request): Json<UpdateUserSkillsRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Admins and process engineers can update user skills
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update user skills"
                    .to_string(),
            }),
        ));
    }

    let user_service = UserService::new(pool);

    match user_service
        .update_user_skills(user_id, request.skill_group_ids)
        .await
    {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "User skills updated successfully"
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}
