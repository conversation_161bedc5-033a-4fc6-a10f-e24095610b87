use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::quality::{
        CreateQualityCheckpointRequest, CreateQualityInspectionRequest, QualityInspectionQuery,
        QualityReportPeriod, UpdateQualityInspectionRequest,
    },
    services::quality_service::QualityService,
    utils::validation::ErrorResponse,
};

pub async fn create_quality_inspection(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): <PERSON><PERSON><CreateQualityInspectionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only quality inspectors and above can create inspections
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"quality_inspector".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            <PERSON>son(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and quality inspectors can create inspections".to_string(),
            }),
        ));
    }

    let quality_service = QualityService::new(pool);

    match quality_service
        .create_quality_inspection(auth_user.id, request)
        .await
    {
        Ok(inspection) => Ok(Json(serde_json::json!({
            "message": "Quality inspection created successfully",
            "inspection": inspection
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "inspection_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_quality_inspections(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<QualityInspectionQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let quality_service = QualityService::new(pool);

    match quality_service.get_all_quality_inspections(query).await {
        Ok(result) => Ok(Json(serde_json::json!(result))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_quality_inspection_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(inspection_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let quality_service = QualityService::new(pool);

    match quality_service
        .get_quality_inspection_by_id(inspection_id)
        .await
    {
        Ok(Some(inspection)) => Ok(Json(serde_json::json!({ "inspection": inspection }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "inspection_not_found".to_string(),
                message: "Quality inspection not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_quality_inspection(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(inspection_id): Path<i32>,
    Json(request): Json<UpdateQualityInspectionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only quality inspectors and above can update inspections
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"quality_inspector".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and quality inspectors can update inspections".to_string(),
            }),
        ));
    }

    let quality_service = QualityService::new(pool);

    match quality_service
        .update_quality_inspection(inspection_id, request)
        .await
    {
        Ok(Some(inspection)) => Ok(Json(serde_json::json!({
            "message": "Quality inspection updated successfully",
            "inspection": inspection
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "inspection_not_found".to_string(),
                message: "Quality inspection not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "inspection_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn create_quality_checkpoint(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateQualityCheckpointRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can create checkpoints
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can create quality checkpoints"
                    .to_string(),
            }),
        ));
    }

    let quality_service = QualityService::new(pool);

    match quality_service.create_quality_checkpoint(request).await {
        Ok(checkpoint) => Ok(Json(serde_json::json!({
            "message": "Quality checkpoint created successfully",
            "checkpoint": checkpoint
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "checkpoint_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_quality_report(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(period): Json<QualityReportPeriod>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and quality inspectors can generate reports
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"quality_inspector".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and quality inspectors can generate quality reports".to_string(),
            }),
        ));
    }

    let quality_service = QualityService::new(pool);

    match quality_service.get_quality_report(period).await {
        Ok(report) => Ok(Json(serde_json::json!(report))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "report_generation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_quality_metrics(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let quality_service = QualityService::new(pool);

    // Create a simple period for current metrics (last 30 days)
    let end_date = chrono::Utc::now();
    let start_date = end_date - chrono::Duration::days(30);

    let period = QualityReportPeriod {
        start_date,
        end_date,
        period_type: "last_30_days".to_string(),
    };

    match quality_service.get_quality_report(period).await {
        Ok(report) => Ok(Json(serde_json::json!({
            "overall_metrics": report.overall_metrics
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "metrics_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_pending_inspections(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let quality_service = QualityService::new(pool);

    let query = QualityInspectionQuery {
        plan_task_id: None,
        work_order_id: None,
        inspector_user_id: None,
        inspection_type: None,
        status: Some("pending".to_string()),
        result: None,
        start_date: None,
        end_date: None,
        limit: Some(50),
        offset: Some(0),
    };

    match quality_service.get_all_quality_inspections(query).await {
        Ok(result) => Ok(Json(serde_json::json!({
            "pending_inspections": result.inspections
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
