use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::work_order::{
        CreateWorkOrderRequest, CreateWorkOrdersFromProjectRequest, UpdateWorkOrderRequest,
        WorkOrderQuery, WorkOrderStatusUpdate,
    },
    services::work_order_service::WorkOrderService,
    utils::validation::ErrorResponse,
};

pub async fn create_work_order(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): J<PERSON><CreateWorkOrderRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create work orders
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            <PERSON><PERSON>(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can create work orders"
                        .to_string(),
            }),
        ));
    }

    let work_order_service = WorkOrderService::new(pool);

    match work_order_service.create_work_order(request).await {
        Ok(work_order) => Ok(Json(serde_json::json!({
            "message": "Work order created successfully",
            "work_order": work_order
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "work_order_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_work_orders(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<WorkOrderQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let work_order_service = WorkOrderService::new(pool);

    match work_order_service.get_all_work_orders(query).await {
        Ok(result) => Ok(Json(serde_json::json!({"data": result}))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_work_order_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(work_order_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let work_order_service = WorkOrderService::new(pool);

    match work_order_service.get_work_order_by_id(work_order_id).await {
        Ok(Some(work_order)) => Ok(Json(serde_json::json!({ "work_order": work_order }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "work_order_not_found".to_string(),
                message: "Work order not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_work_order(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(work_order_id): Path<i32>,
    Json(request): Json<UpdateWorkOrderRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can update work orders
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can update work orders"
                        .to_string(),
            }),
        ));
    }

    let work_order_service = WorkOrderService::new(pool);

    match work_order_service
        .update_work_order(work_order_id, request)
        .await
    {
        Ok(Some(work_order)) => Ok(Json(serde_json::json!({
            "message": "Work order updated successfully",
            "work_order": work_order
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "work_order_not_found".to_string(),
                message: "Work order not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "work_order_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_work_order(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(work_order_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete work orders
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete work orders".to_string(),
            }),
        ));
    }

    let work_order_service = WorkOrderService::new(pool);

    match work_order_service.delete_work_order(work_order_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Work order deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "work_order_not_found".to_string(),
                message: "Work order not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn create_work_orders_from_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateWorkOrdersFromProjectRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create work orders from projects
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can create work orders from projects".to_string(),
            }),
        ));
    }

    let work_order_service = WorkOrderService::new(pool);

    match work_order_service
        .create_work_orders_from_project(request)
        .await
    {
        Ok(work_orders) => Ok(Json(serde_json::json!({
            "message": format!("Created {} work orders successfully", work_orders.len()),
            "work_orders": work_orders
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "work_orders_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_work_orders_by_project(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let work_order_service = WorkOrderService::new(pool);

    match work_order_service
        .get_work_orders_by_project(project_id)
        .await
    {
        Ok(work_orders) => Ok(Json(serde_json::json!({ "work_orders": work_orders }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_work_order_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(work_order_id): Path<i32>,
    Json(request): Json<WorkOrderStatusUpdate>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Operators and above can update work order status
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Insufficient permissions to update work order status".to_string(),
            }),
        ));
    }

    let work_order_service = WorkOrderService::new(pool);
    let update_request = UpdateWorkOrderRequest {
        quantity: None,
        status: Some(request.status),
        due_date: None,
    };

    match work_order_service
        .update_work_order(work_order_id, update_request)
        .await
    {
        Ok(Some(work_order)) => Ok(Json(serde_json::json!({
            "message": "Work order status updated successfully",
            "work_order": work_order
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "work_order_not_found".to_string(),
                message: "Work order not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "status_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}
