use axum::{
    extract::{Extension, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use serde::Deserialize;
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::dashboard::{DashboardQuery, ReportRequest},
    services::dashboard_service::DashboardService,
    utils::validation::ErrorResponse,
};

#[derive(Debug, Deserialize)]
pub struct TrendQuery {
    pub metric_name: String,
    pub days: Option<i32>,
}

pub async fn get_dashboard_overview(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_dashboard_overview(query).await {
        Ok(overview) => Ok(Json(serde_json::json!(overview))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "dashboard_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_production_report(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<ReportRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can generate reports
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can generate reports"
                        .to_string(),
            }),
        ));
    }

    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_production_report(request).await {
        Ok(report) => Ok(Json(serde_json::json!(report))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "report_generation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_kpi_metrics(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_kpi_metrics().await {
        Ok(metrics) => Ok(Json(serde_json::json!(metrics))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "kpi_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_trend_data(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<TrendQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let days = query.days.unwrap_or(30);

    if days > 365 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "invalid_range".to_string(),
                message: "Maximum trend data range is 365 days".to_string(),
            }),
        ));
    }

    let dashboard_service = DashboardService::new(pool);

    match dashboard_service
        .get_trend_data(&query.metric_name, days)
        .await
    {
        Ok(trend_data) => Ok(Json(serde_json::json!(trend_data))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "trend_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_machine_utilization(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    // Create a simple report request for machine utilization
    let report_request = ReportRequest {
        report_type: "machine_utilization".to_string(),
        period: crate::models::dashboard::ReportPeriod {
            start_date: query
                .start_date
                .unwrap_or_else(|| chrono::Utc::now().date_naive() - chrono::Duration::days(30)),
            end_date: query
                .end_date
                .unwrap_or_else(|| chrono::Utc::now().date_naive()),
            period_type: "custom".to_string(),
        },
        filters: crate::models::dashboard::ReportFilters {
            skill_group_ids: query.skill_group_id.map(|id| vec![id]),
            machine_ids: query.machine_id.map(|id| vec![id]),
            project_ids: query.project_id.map(|id| vec![id]),
            user_ids: None,
            include_cancelled: Some(false),
        },
        format: "json".to_string(),
    };

    match dashboard_service
        .get_production_report(report_request)
        .await
    {
        Ok(report) => Ok(Json(serde_json::json!({
            "machine_utilization": report.machine_utilization
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "utilization_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_skill_group_performance(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    // Create a simple report request for skill group performance
    let report_request = ReportRequest {
        report_type: "skill_group_performance".to_string(),
        period: crate::models::dashboard::ReportPeriod {
            start_date: query
                .start_date
                .unwrap_or_else(|| chrono::Utc::now().date_naive() - chrono::Duration::days(30)),
            end_date: query
                .end_date
                .unwrap_or_else(|| chrono::Utc::now().date_naive()),
            period_type: "custom".to_string(),
        },
        filters: crate::models::dashboard::ReportFilters {
            skill_group_ids: query.skill_group_id.map(|id| vec![id]),
            machine_ids: query.machine_id.map(|id| vec![id]),
            project_ids: query.project_id.map(|id| vec![id]),
            user_ids: None,
            include_cancelled: Some(false),
        },
        format: "json".to_string(),
    };

    match dashboard_service
        .get_production_report(report_request)
        .await
    {
        Ok(report) => Ok(Json(serde_json::json!({
            "skill_group_performance": report.skill_group_performance
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "performance_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_production_summary(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_dashboard_overview(query).await {
        Ok(overview) => Ok(Json(serde_json::json!({
            "production_summary": overview.production_summary
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "summary_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_work_order_status(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_dashboard_overview(query).await {
        Ok(overview) => Ok(Json(serde_json::json!({
            "work_order_status": overview.work_order_status
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "status_calculation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_recent_activities(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let dashboard_service = DashboardService::new(pool);

    match dashboard_service.get_dashboard_overview(query).await {
        Ok(overview) => Ok(Json(serde_json::json!({
            "recent_activities": overview.recent_activities
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "activities_fetch_failed".to_string(),
                message: error,
            }),
        )),
    }
}
