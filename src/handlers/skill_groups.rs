use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use validator::Validate;

use crate::middleware::auth::AuthUser;
use crate::utils::validation::ErrorResponse;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateSkillGroupRequest {
    #[validate(length(min = 2, max = 100, message = "Skill group name must be between 2 and 100 characters"))]
    pub group_name: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateSkillGroupRequest {
    pub group_name: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DeleteSkillGroupRequest {
    pub replacement_skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SkillGroupDependencyInfo {
    pub skill_group_id: i32,
    pub group_name: String,
    pub can_delete: bool,
    pub blocking_reason: Option<String>,
    pub affected_users: Vec<UserInfo>,
    pub affected_machines: Vec<MachineInfo>,
    pub affected_plan_tasks: Vec<PlanTaskInfo>,
    pub user_count: i32,
    pub machine_count: i32,
    pub plan_task_count: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i32,
    pub username: String,
    pub full_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineInfo {
    pub id: i32,
    pub machine_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskInfo {
    pub id: i32,
    pub work_order_id: i32,
}

// 创建技能组
pub async fn create_skill_group(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateSkillGroupRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限 - 只有管理员可以创建技能组
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can create skill groups".to_string(),
            }),
        ));
    }

    // 验证请求
    if let Err(errors) = request.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    // 检查技能组名是否已存在
    let existing_skill_group = sqlx::query!(
        "SELECT id FROM skill_groups WHERE group_name = $1",
        request.group_name
    )
    .fetch_optional(&pool)
    .await;

    match existing_skill_group {
        Ok(Some(_)) => {
            return Err((
                StatusCode::CONFLICT,
                Json(ErrorResponse {
                    error: "skill_group_exists".to_string(),
                    message: "Skill group with this name already exists".to_string(),
                }),
            ));
        }
        Ok(None) => {
            // 技能组不存在，可以创建
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    }

    // 创建技能组
    let result = sqlx::query!(
        "INSERT INTO skill_groups (group_name) VALUES ($1) RETURNING id, group_name",
        request.group_name
    )
    .fetch_one(&pool)
    .await;

    match result {
        Ok(skill_group) => Ok(Json(serde_json::json!({
            "message": "Skill group created successfully",
            "skill_group": {
                "id": skill_group.id,
                "group_name": skill_group.group_name
            }
        }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: format!("Failed to create skill group: {}", error),
            }),
        )),
    }
}

// 更新技能组
pub async fn update_skill_group(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(skill_group_id): Path<i32>,
    Json(request): Json<UpdateSkillGroupRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can update skill groups".to_string(),
            }),
        ));
    }

    // 检查是否为系统技能组
    let skill_group = sqlx::query!(
        "SELECT group_name FROM skill_groups WHERE id = $1",
        skill_group_id
    )
    .fetch_optional(&pool)
    .await;

    match skill_group {
        Ok(Some(skill_group)) => {
            let system_skill_groups = vec!["CNC Machining", "Milling", "Turning", "Grinding", "Assembly", "Quality Control", "Packaging"];
            if system_skill_groups.contains(&skill_group.group_name.as_str()) {
                return Err((
                    StatusCode::FORBIDDEN,
                    Json(ErrorResponse {
                        error: "system_skill_group_protected".to_string(),
                        message: "System skill groups cannot be modified".to_string(),
                    }),
                ));
            }
        }
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "skill_group_not_found".to_string(),
                    message: "Skill group not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    }

    // 更新技能组
    if let Some(group_name) = &request.group_name {
        let result = sqlx::query!(
            "UPDATE skill_groups SET group_name = $1 WHERE id = $2 RETURNING id, group_name",
            group_name,
            skill_group_id
        )
        .fetch_one(&pool)
        .await;

        match result {
            Ok(skill_group) => Ok(Json(serde_json::json!({
                "message": "Skill group updated successfully",
                "skill_group": {
                    "id": skill_group.id,
                    "group_name": skill_group.group_name
                }
            }))),
            Err(error) => Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "update_failed".to_string(),
                    message: format!("Failed to update skill group: {}", error),
                }),
            )),
        }
    } else {
        Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "no_updates".to_string(),
                message: "No updates provided".to_string(),
            }),
        ))
    }
}

// 检查技能组依赖
pub async fn check_skill_group_dependencies(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(skill_group_id): Path<i32>,
) -> Result<Json<SkillGroupDependencyInfo>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can check skill group dependencies".to_string(),
            }),
        ));
    }

    // 获取技能组信息
    let skill_group = sqlx::query!(
        "SELECT group_name FROM skill_groups WHERE id = $1",
        skill_group_id
    )
    .fetch_optional(&pool)
    .await;

    let skill_group = match skill_group {
        Ok(Some(skill_group)) => skill_group,
        Ok(None) => {
            return Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "skill_group_not_found".to_string(),
                    message: "Skill group not found".to_string(),
                }),
            ));
        }
        Err(error) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "database_error".to_string(),
                    message: format!("Database error: {}", error),
                }),
            ));
        }
    };

    // 检查是否为系统技能组
    let system_skill_groups = vec!["CNC Machining", "Milling", "Turning", "Grinding", "Assembly", "Quality Control", "Packaging"];
    let is_system_skill_group = system_skill_groups.contains(&skill_group.group_name.as_str());

    // 获取受影响的用户
    let affected_users = sqlx::query!(
        "SELECT u.id, u.username, u.full_name 
         FROM users u 
         JOIN user_skills us ON u.id = us.user_id 
         WHERE us.skill_group_id = $1",
        skill_group_id
    )
    .fetch_all(&pool)
    .await
    .unwrap_or_default()
    .into_iter()
    .map(|u| UserInfo {
        id: u.id,
        username: u.username,
        full_name: u.full_name,
    })
    .collect::<Vec<_>>();

    // 获取受影响的设备
    let affected_machines = sqlx::query!(
        "SELECT id, machine_name FROM machines WHERE skill_group_id = $1",
        skill_group_id
    )
    .fetch_all(&pool)
    .await
    .unwrap_or_default()
    .into_iter()
    .map(|m| MachineInfo {
        id: m.id,
        machine_name: m.machine_name,
    })
    .collect::<Vec<_>>();

    // 获取受影响的生产计划任务
    let affected_plan_tasks = sqlx::query!(
        "SELECT id, work_order_id FROM plan_tasks WHERE skill_group_id = $1",
        skill_group_id
    )
    .fetch_all(&pool)
    .await
    .unwrap_or_default()
    .into_iter()
    .map(|pt| PlanTaskInfo {
        id: pt.id,
        work_order_id: pt.work_order_id,
    })
    .collect::<Vec<_>>();

    let user_count = affected_users.len() as i32;
    let machine_count = affected_machines.len() as i32;
    let plan_task_count = affected_plan_tasks.len() as i32;

    let can_delete = !is_system_skill_group && user_count == 0 && machine_count == 0 && plan_task_count == 0;
    let blocking_reason = if is_system_skill_group {
        Some("System skill group cannot be deleted".to_string())
    } else if user_count > 0 || machine_count > 0 || plan_task_count > 0 {
        Some(format!(
            "Skill group is used by {} user(s), {} machine(s), and {} plan task(s)",
            user_count, machine_count, plan_task_count
        ))
    } else {
        None
    };

    Ok(Json(SkillGroupDependencyInfo {
        skill_group_id,
        group_name: skill_group.group_name,
        can_delete,
        blocking_reason,
        affected_users,
        affected_machines,
        affected_plan_tasks,
        user_count,
        machine_count,
        plan_task_count,
    }))
}

// 删除技能组
pub async fn delete_skill_group(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(skill_group_id): Path<i32>,
    Json(request): Json<DeleteSkillGroupRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // 检查权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete skill groups".to_string(),
            }),
        ));
    }

    // 先检查依赖
    let dependency_check = check_skill_group_dependencies(
        State(pool.clone()),
        Extension(auth_user.clone()),
        Path(skill_group_id),
    ).await;

    let dependency_info = match dependency_check {
        Ok(Json(info)) => info,
        Err(error) => return Err(error),
    };

    // 如果不能删除且没有提供替换技能组，返回错误
    if !dependency_info.can_delete && request.replacement_skill_group_id.is_none() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "replacement_required".to_string(),
                message: "Replacement skill group ID is required for skill groups with dependencies".to_string(),
            }),
        ));
    }

    // 开始事务
    let mut tx = pool.begin().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "transaction_error".to_string(),
                message: format!("Failed to start transaction: {}", e),
            }),
        )
    })?;

    // 如果有替换技能组，更新相关数据
    if let Some(replacement_skill_group_id) = request.replacement_skill_group_id {
        // 更新用户技能
        sqlx::query!(
            "UPDATE user_skills SET skill_group_id = $1 WHERE skill_group_id = $2",
            replacement_skill_group_id,
            skill_group_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "user_skills_migration_failed".to_string(),
                    message: format!("Failed to migrate user skills: {}", e),
                }),
            )
        })?;

        // 更新设备
        sqlx::query!(
            "UPDATE machines SET skill_group_id = $1 WHERE skill_group_id = $2",
            replacement_skill_group_id,
            skill_group_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "machines_migration_failed".to_string(),
                    message: format!("Failed to migrate machines: {}", e),
                }),
            )
        })?;

        // 更新生产计划任务
        sqlx::query!(
            "UPDATE plan_tasks SET skill_group_id = $1 WHERE skill_group_id = $2",
            replacement_skill_group_id,
            skill_group_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "plan_tasks_migration_failed".to_string(),
                    message: format!("Failed to migrate plan tasks: {}", e),
                }),
            )
        })?;
    }

    // 删除技能组
    sqlx::query!("DELETE FROM skill_groups WHERE id = $1", skill_group_id)
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "deletion_failed".to_string(),
                    message: format!("Failed to delete skill group: {}", e),
                }),
            )
        })?;

    // 提交事务
    tx.commit().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "commit_failed".to_string(),
                message: format!("Failed to commit transaction: {}", e),
            }),
        )
    })?;

    Ok(Json(serde_json::json!({
        "message": "Skill group deleted successfully"
    })))
}
