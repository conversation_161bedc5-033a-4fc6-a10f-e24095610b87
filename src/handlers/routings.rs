use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;
use serde::{Deserialize, Serialize};

use crate::{
    middleware::auth::AuthUser,
    models::routing::{CreateRoutingRequest, UpdateRoutingRequest, RoutingQuery},
    services::routing_service::RoutingService,
    utils::validation::ErrorResponse,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct ReorderStepsRequest {
    pub step_orders: Vec<StepOrder>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StepOrder {
    pub routing_id: i32,
    pub new_step_number: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CopyRoutingRequest {
    pub target_part_id: i32,
}

pub async fn create_routing(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateRoutingRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can create routings
    if !auth_user.roles.contains(&"admin".to_string()) 
        && !auth_user.roles.contains(&"process_engineer".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can create routings".to_string(),
            }),
        ));
    }

    let routing_service = RoutingService::new(pool);
    
    match routing_service.create_routing(request).await {
        Ok(routing) => Ok(Json(serde_json::json!({
            "message": "Routing step created successfully",
            "routing": routing
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_routings(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<RoutingQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let routing_service = RoutingService::new(pool);
    
    match routing_service.get_all_routings(query).await {
        Ok(routings) => Ok(Json(serde_json::json!({ "routings": routings }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_routing_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(routing_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let routing_service = RoutingService::new(pool);
    
    match routing_service.get_routing_by_id(routing_id).await {
        Ok(Some(routing)) => Ok(Json(serde_json::json!({ "routing": routing }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "routing_not_found".to_string(),
                message: "Routing not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_part_routing(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let routing_service = RoutingService::new(pool);
    
    match routing_service.get_part_routing(part_id).await {
        Ok(Some(part_routing)) => Ok(Json(serde_json::json!({ "part_routing": part_routing }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "part_not_found".to_string(),
                message: "Part not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_routing(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(routing_id): Path<i32>,
    Json(request): Json<UpdateRoutingRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can update routings
    if !auth_user.roles.contains(&"admin".to_string()) 
        && !auth_user.roles.contains(&"process_engineer".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update routings".to_string(),
            }),
        ));
    }

    let routing_service = RoutingService::new(pool);
    
    match routing_service.update_routing(routing_id, request).await {
        Ok(Some(routing)) => Ok(Json(serde_json::json!({
            "message": "Routing updated successfully",
            "routing": routing
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "routing_not_found".to_string(),
                message: "Routing not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_routing(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(routing_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can delete routings
    if !auth_user.roles.contains(&"admin".to_string()) 
        && !auth_user.roles.contains(&"process_engineer".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can delete routings".to_string(),
            }),
        ));
    }

    let routing_service = RoutingService::new(pool);
    
    match routing_service.delete_routing(routing_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Routing deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "routing_not_found".to_string(),
                message: "Routing not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn reorder_routing_steps(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(part_id): Path<i32>,
    Json(request): Json<ReorderStepsRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can reorder routing steps
    if !auth_user.roles.contains(&"admin".to_string()) 
        && !auth_user.roles.contains(&"process_engineer".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can reorder routing steps".to_string(),
            }),
        ));
    }

    let routing_service = RoutingService::new(pool);
    let step_orders: Vec<(i32, i32)> = request.step_orders
        .into_iter()
        .map(|so| (so.routing_id, so.new_step_number))
        .collect();
    
    match routing_service.reorder_routing_steps(part_id, step_orders).await {
        Ok(()) => Ok(Json(serde_json::json!({
            "message": "Routing steps reordered successfully"
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "reorder_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn copy_routing(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(source_part_id): Path<i32>,
    Json(request): Json<CopyRoutingRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can copy routings
    if !auth_user.roles.contains(&"admin".to_string()) 
        && !auth_user.roles.contains(&"process_engineer".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can copy routings".to_string(),
            }),
        ));
    }

    let routing_service = RoutingService::new(pool);
    
    match routing_service.copy_routing(source_part_id, request.target_part_id).await {
        Ok(copied_steps) => Ok(Json(serde_json::json!({
            "message": "Routing copied successfully",
            "copied_steps": copied_steps
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "copy_failed".to_string(),
                message: error,
            }),
        )),
    }
}
