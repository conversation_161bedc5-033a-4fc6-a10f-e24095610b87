use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
// use std::collections::HashMap;

use crate::{
    middleware::auth::AuthUser,
    models::machine::{CreateMachineRequest, UpdateMachineRequest},
    services::machine_service::MachineService,
    utils::validation::ErrorResponse,
};

#[derive(Debug, Deserialize)]
pub struct MachineQuery {
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateMachineStatusRequest {
    pub status: String,
}

pub async fn get_all_machines(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<MachineQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let machine_service = MachineService::new(pool);

    let machines = if let Some(skill_group_id) = query.skill_group_id {
        machine_service
            .get_machines_by_skill_group(skill_group_id)
            .await
    } else {
        machine_service.get_all_machines().await
    };

    match machines {
        Ok(machines) => Ok(Json(serde_json::json!({ "data": { "machines": machines } }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_machine_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(machine_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let machine_service = MachineService::new(pool);

    match machine_service.get_machine_by_id(machine_id).await {
        Ok(Some(machine)) => Ok(Json(serde_json::json!({ "machine": machine }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "machine_not_found".to_string(),
                message: "Machine not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn create_machine(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateMachineRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can create machines
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can create machines"
                    .to_string(),
            }),
        ));
    }

    let machine_service = MachineService::new(pool);

    match machine_service.create_machine(request).await {
        Ok(machine) => Ok(Json(serde_json::json!({
            "message": "Machine created successfully",
            "machine": machine
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_machine(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(machine_id): Path<i32>,
    Json(request): Json<UpdateMachineRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins and process engineers can update machines
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators and process engineers can update machines"
                    .to_string(),
            }),
        ));
    }

    let machine_service = MachineService::new(pool);

    match machine_service.update_machine(machine_id, request).await {
        Ok(Some(machine)) => Ok(Json(serde_json::json!({
            "message": "Machine updated successfully",
            "machine": machine
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "machine_not_found".to_string(),
                message: "Machine not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_machine(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(machine_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete machines
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete machines".to_string(),
            }),
        ));
    }

    let machine_service = MachineService::new(pool);

    match machine_service.delete_machine(machine_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Machine deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "machine_not_found".to_string(),
                message: "Machine not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_machine_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(machine_id): Path<i32>,
    Json(request): Json<UpdateMachineStatusRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Operators and above can update machine status
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Insufficient permissions to update machine status".to_string(),
            }),
        ));
    }

    let machine_service = MachineService::new(pool);

    match machine_service
        .update_machine_status(machine_id, request.status)
        .await
    {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Machine status updated successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "machine_not_found".to_string(),
                message: "Machine not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}
