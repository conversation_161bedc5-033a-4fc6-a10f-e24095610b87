use axum::{
    extract::{Extension, Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::project::{
        CreateProjectBomRequest, CreateProjectRequest, UpdateProjectBomRequest,
        UpdateProjectRequest,
    },
    services::project_service::ProjectService,
    utils::validation::ErrorResponse,
};

pub async fn create_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateProjectRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create projects
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            <PERSON><PERSON>(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can create projects"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.create_project(request).await {
        Ok(project) => Ok(Json(serde_json::json!({
            "message": "Project created successfully",
            "project": project
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_projects(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_all_projects().await {
        Ok(projects) => Ok(Json(serde_json::json!({ "projects": projects }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_by_id(project_id).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({ "project": project }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_with_bom(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_with_bom(project_id).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({ "project": project }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    Json(request): Json<UpdateProjectRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can update projects
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can update projects"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.update_project(project_id, request).await {
        Ok(Some(project)) => Ok(Json(serde_json::json!({
            "message": "Project updated successfully",
            "project": project
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_project(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete projects
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete projects".to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.delete_project(project_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Project deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "project_not_found".to_string(),
                message: "Project not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

// BOM Management
pub async fn add_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
    Json(request): Json<CreateProjectBomRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.add_bom_item(project_id, request).await {
        Ok(bom_item) => Ok(Json(serde_json::json!({
            "message": "BOM item added successfully",
            "bom_item": bom_item
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(bom_id): Path<i32>,
    Json(request): Json<UpdateProjectBomRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.update_bom_item(bom_id, request).await {
        Ok(Some(bom_item)) => Ok(Json(serde_json::json!({
            "message": "BOM item updated successfully",
            "bom_item": bom_item
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "bom_item_not_found".to_string(),
                message: "BOM item not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn remove_bom_item(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(bom_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can manage BOMs
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can manage BOMs"
                    .to_string(),
            }),
        ));
    }

    let project_service = ProjectService::new(pool);

    match project_service.remove_bom_item(bom_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "BOM item removed successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "bom_item_not_found".to_string(),
                message: "BOM item not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "delete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_project_bom(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(project_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let project_service = ProjectService::new(pool);

    match project_service.get_project_bom(project_id).await {
        Ok(bom_items) => Ok(Json(serde_json::json!({ "bom_items": bom_items }))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
