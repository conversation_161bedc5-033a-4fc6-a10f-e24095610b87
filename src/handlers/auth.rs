use axum::{
    extract::{Extension, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use sqlx::PgPool;
use validator::Validate;

use crate::{
    middleware::auth::AuthUser,
    services::auth_service::AuthService,
    utils::validation::{CreateUserRequest, ErrorResponse, LoginRequest, LoginResponse, UserInfo},
};

pub async fn login(
    State(pool): State<PgPool>,
    <PERSON><PERSON>(request): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate request
    if let Err(errors) = request.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    let auth_service = AuthService::new(pool);

    match auth_service.login(request).await {
        Ok(response) => Ok(Json(response)),
        Err(error) => Err((
            StatusCode::UNAUTHORIZED,
            <PERSON><PERSON>(ErrorResponse {
                error: "authentication_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn create_user(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateUserRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Check if user has admin role
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can create users".to_string(),
            }),
        ));
    }

    // Validate request
    if let Err(errors) = request.validate() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: format!("Validation failed: {:?}", errors),
            }),
        ));
    }

    let auth_service = AuthService::new(pool);

    match auth_service.create_user(request).await {
        Ok(user) => Ok(Json(serde_json::json!({
            "message": "User created successfully",
            "user": user
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "user_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_current_user(
    Extension(auth_user): Extension<AuthUser>,
    State(pool): State<PgPool>,
) -> Result<Json<UserInfo>, (StatusCode, Json<ErrorResponse>)> {
    let auth_service = AuthService::new(pool);

    match auth_service.get_user_info(auth_user.id).await {
        Ok(user_info) => Ok(Json(user_info)),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "user_fetch_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_roles(
    State(pool): State<PgPool>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let roles = sqlx::query!("SELECT id, role_name FROM roles ORDER BY role_name")
        .fetch_all(&pool)
        .await;

    match roles {
        Ok(roles) => {
            let role_list: Vec<serde_json::Value> = roles
                .into_iter()
                .map(|role| {
                    serde_json::json!({
                        "id": role.id,
                        "role_name": role.role_name
                    })
                })
                .collect();
            Ok(Json(serde_json::json!({ "roles": role_list })))
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch roles: {}", error),
            }),
        )),
    }
}

pub async fn get_skill_groups(
    State(pool): State<PgPool>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let skill_groups = sqlx::query!("SELECT id, group_name FROM skill_groups ORDER BY group_name")
        .fetch_all(&pool)
        .await;

    match skill_groups {
        Ok(skill_groups) => {
            let skill_group_list: Vec<serde_json::Value> = skill_groups
                .into_iter()
                .map(|sg| {
                    serde_json::json!({
                        "id": sg.id,
                        "group_name": sg.group_name
                    })
                })
                .collect();
            Ok(Json(
                serde_json::json!({ "skill_groups": skill_group_list }),
            ))
        }
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to fetch skill groups: {}", error),
            }),
        )),
    }
}
