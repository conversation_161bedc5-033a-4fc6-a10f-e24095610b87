use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::{DateTime, Utc};
use serde::Deserialize;
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::plan_task::{
        CreatePlanTaskRequest, CreatePlanTasksFromWorkOrderRequest, PlanTaskQuery,
        PlanTaskStatusUpdate, ReschedulePlanTaskRequest, UpdatePlanTaskRequest,
    },
    services::plan_task_service::PlanTaskService,
    utils::validation::ErrorResponse,
};

pub async fn create_plan_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePlanTaskRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create plan tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can create plan tasks"
                        .to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.create_plan_task(request).await {
        Ok(plan_task) => Ok(Json(serde_json::json!({
            "message": "Plan task created successfully",
            "plan_task": plan_task
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "plan_task_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_all_plan_tasks(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<PlanTaskQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.get_all_plan_tasks(query).await {
        Ok(result) => Ok(Json(serde_json::json!(result))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

// Temporary public version for testing
pub async fn get_all_plan_tasks_public(
    State(pool): State<PgPool>,
    Query(query): Query<PlanTaskQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.get_all_plan_tasks(query).await {
        Ok(result) => Ok(Json(serde_json::json!(result))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

// Temporary public version for testing
pub async fn create_plan_task_public(
    State(pool): State<PgPool>,
    Json(request): Json<CreatePlanTaskRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.create_plan_task(request).await {
        Ok(plan_task) => Ok(Json(serde_json::json!({
            "message": "Plan task created successfully",
            "plan_task": plan_task
        }))),
        Err(error) => Err((
            StatusCode::UNPROCESSABLE_ENTITY,
            Json(ErrorResponse {
                error: "validation_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_plan_task_by_id(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Path(plan_task_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.get_plan_task_by_id(plan_task_id).await {
        Ok(Some(plan_task)) => Ok(Json(serde_json::json!({ "plan_task": plan_task }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "plan_task_not_found".to_string(),
                message: "Plan task not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_plan_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(plan_task_id): Path<i32>,
    Json(request): Json<UpdatePlanTaskRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can update plan tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can update plan tasks"
                        .to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service
        .update_plan_task(plan_task_id, request)
        .await
    {
        Ok(Some(plan_task)) => Ok(Json(serde_json::json!({
            "message": "Plan task updated successfully",
            "plan_task": plan_task
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "plan_task_not_found".to_string(),
                message: "Plan task not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "plan_task_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn delete_plan_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(plan_task_id): Path<i32>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins can delete plan tasks
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators can delete plan tasks".to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service.delete_plan_task(plan_task_id).await {
        Ok(true) => Ok(Json(serde_json::json!({
            "message": "Plan task deleted successfully"
        }))),
        Ok(false) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "plan_task_not_found".to_string(),
                message: "Plan task not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn create_plan_tasks_from_work_order(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePlanTasksFromWorkOrderRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can create plan tasks from work orders
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only administrators, process engineers, and planners can create plan tasks from work orders".to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service
        .create_plan_tasks_from_work_order(request)
        .await
    {
        Ok(plan_tasks) => Ok(Json(serde_json::json!({
            "message": format!("Created {} plan tasks successfully", plan_tasks.len()),
            "plan_tasks": plan_tasks
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "plan_tasks_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn update_plan_task_status(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(plan_task_id): Path<i32>,
    Json(request): Json<PlanTaskStatusUpdate>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Operators and above can update plan task status
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Insufficient permissions to update plan task status".to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);
    let update_request = UpdatePlanTaskRequest {
        skill_group_id: None,
        planned_start: None,
        planned_end: None,
        status: Some(request.status),
    };

    match plan_task_service
        .update_plan_task(plan_task_id, update_request)
        .await
    {
        Ok(Some(plan_task)) => Ok(Json(serde_json::json!({
            "message": "Plan task status updated successfully",
            "plan_task": plan_task
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "plan_task_not_found".to_string(),
                message: "Plan task not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "status_update_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn reschedule_plan_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(plan_task_id): Path<i32>,
    Json(request): Json<ReschedulePlanTaskRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only admins, process engineers, and planners can reschedule plan tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message:
                    "Only administrators, process engineers, and planners can reschedule plan tasks"
                        .to_string(),
            }),
        ));
    }

    let plan_task_service = PlanTaskService::new(pool);
    let update_request = UpdatePlanTaskRequest {
        skill_group_id: request.skill_group_id,
        planned_start: Some(request.planned_start),
        planned_end: Some(request.planned_end),
        status: None,
    };

    match plan_task_service
        .update_plan_task(plan_task_id, update_request)
        .await
    {
        Ok(Some(plan_task)) => Ok(Json(serde_json::json!({
            "message": "Plan task rescheduled successfully",
            "plan_task": plan_task
        }))),
        Ok(None) => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "plan_task_not_found".to_string(),
                message: "Plan task not found".to_string(),
            }),
        )),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "reschedule_failed".to_string(),
                message: error,
            }),
        )),
    }
}

#[derive(Debug, Deserialize)]
pub struct GanttChartQuery {
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
}

pub async fn get_gantt_chart_data(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<GanttChartQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let plan_task_service = PlanTaskService::new(pool);

    match plan_task_service
        .get_gantt_chart_data(query.start_date, query.end_date)
        .await
    {
        Ok(gantt_data) => Ok(Json(serde_json::json!(gantt_data))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}
