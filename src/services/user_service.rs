use crate::models::user::{User, UserWithRolesAndSkills};
use sqlx::PgPool;

pub struct UserService {
    pool: PgPool,
}

impl UserService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_all_users(&self) -> Result<Vec<UserWithRolesAndSkills>, String> {
        let users = sqlx::query_as!(
            User,
            "SELECT id, username, password_hash, full_name, is_active, created_at 
             FROM users ORDER BY username"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut result = Vec::new();
        for user in users {
            let roles = sqlx::query_scalar!(
                "SELECT r.role_name FROM roles r 
                 JOIN user_roles ur ON r.id = ur.role_id 
                 WHERE ur.user_id = $1",
                user.id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let skills = sqlx::query_scalar!(
                "SELECT sg.group_name FROM skill_groups sg 
                 JOIN user_skills us ON sg.id = us.skill_group_id 
                 WHERE us.user_id = $1",
                user.id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            result.push(UserWithRolesAndSkills {
                id: user.id,
                username: user.username,
                full_name: user.full_name,
                is_active: user.is_active,
                created_at: user.created_at,
                roles,
                skills,
            });
        }

        Ok(result)
    }

    pub async fn get_user_by_id(
        &self,
        user_id: i32,
    ) -> Result<Option<UserWithRolesAndSkills>, String> {
        let user = sqlx::query_as!(
            User,
            "SELECT id, username, password_hash, full_name, is_active, created_at 
             FROM users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(user) = user {
            let roles = sqlx::query_scalar!(
                "SELECT r.role_name FROM roles r 
                 JOIN user_roles ur ON r.id = ur.role_id 
                 WHERE ur.user_id = $1",
                user.id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let skills = sqlx::query_scalar!(
                "SELECT sg.group_name FROM skill_groups sg 
                 JOIN user_skills us ON sg.id = us.skill_group_id 
                 WHERE us.user_id = $1",
                user.id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            Ok(Some(UserWithRolesAndSkills {
                id: user.id,
                username: user.username,
                full_name: user.full_name,
                is_active: user.is_active,
                created_at: user.created_at,
                roles,
                skills,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_user_status(&self, user_id: i32, is_active: bool) -> Result<(), String> {
        sqlx::query!(
            "UPDATE users SET is_active = $1 WHERE id = $2",
            is_active,
            user_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    pub async fn delete_user(&self, user_id: i32) -> Result<(), String> {
        // Start transaction
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Delete user roles
        sqlx::query!("DELETE FROM user_roles WHERE user_id = $1", user_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Delete user skills
        sqlx::query!("DELETE FROM user_skills WHERE user_id = $1", user_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Delete user
        sqlx::query!("DELETE FROM users WHERE id = $1", user_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Commit transaction
        tx.commit()
            .await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        Ok(())
    }

    pub async fn update_user_roles(&self, user_id: i32, role_ids: Vec<i32>) -> Result<(), String> {
        // Start transaction
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Delete existing roles
        sqlx::query!("DELETE FROM user_roles WHERE user_id = $1", user_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Add new roles
        for role_id in role_ids {
            sqlx::query!(
                "INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)",
                user_id,
                role_id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // Commit transaction
        tx.commit()
            .await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        Ok(())
    }

    pub async fn update_user_skills(
        &self,
        user_id: i32,
        skill_group_ids: Vec<i32>,
    ) -> Result<(), String> {
        // Start transaction
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Delete existing skills
        sqlx::query!("DELETE FROM user_skills WHERE user_id = $1", user_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        // Add new skills
        for skill_group_id in skill_group_ids {
            sqlx::query!(
                "INSERT INTO user_skills (user_id, skill_group_id) VALUES ($1, $2)",
                user_id,
                skill_group_id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // Commit transaction
        tx.commit()
            .await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        Ok(())
    }
}
