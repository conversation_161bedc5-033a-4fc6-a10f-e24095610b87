use crate::models::work_order::{
    CreateWorkOrderRequest, CreateWorkOrdersFromProjectRequest, UpdateWorkOrderRequest, WorkOrder,
    WorkOrderQuery, WorkOrderSearchResult, WorkOrderWithDetails, get_valid_work_order_statuses,
};
use sqlx::PgPool;

pub struct WorkOrderService {
    pool: PgPool,
}

impl WorkOrderService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_work_order(
        &self,
        request: CreateWorkOrderRequest,
    ) -> Result<WorkOrder, String> {
        // Validate that the project_bom_id exists
        let bom_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM project_boms WHERE id = $1)",
            request.project_bom_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !bom_exists.unwrap_or(false) {
            return Err("Project BOM not found".to_string());
        }

        let work_order = sqlx::query_as!(
            WorkOrder,
            "INSERT INTO work_orders (project_bom_id, quantity, due_date)
             VALUES ($1, $2, $3)
             RETURNING id, project_bom_id, quantity, status, due_date, created_at",
            request.project_bom_id,
            request.quantity,
            request.due_date
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(work_order)
    }

    pub async fn get_work_order_by_id(
        &self,
        work_order_id: i32,
    ) -> Result<Option<WorkOrderWithDetails>, String> {
        let work_order = sqlx::query_as!(
            WorkOrderWithDetails,
            "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                    pb.project_id, p.project_name, p.customer_name,
                    pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                    pb.quantity as bom_quantity
             FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts pt ON pb.part_id = pt.id
             WHERE wo.id = $1",
            work_order_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(work_order)
    }

    pub async fn get_all_work_orders(
        &self,
        query: WorkOrderQuery,
    ) -> Result<WorkOrderSearchResult, String> {
        let limit = query.limit.unwrap_or(50).min(100);
        let offset = query.offset.unwrap_or(0);

        // Build the WHERE clause dynamically
        let mut where_conditions = Vec::new();
        let mut param_count = 0;

        if query.project_id.is_some() {
            param_count += 1;
            where_conditions.push(format!("pb.project_id = ${}", param_count));
        }

        if query.status.is_some() {
            param_count += 1;
            where_conditions.push(format!("wo.status = ${}", param_count));
        }

        if query.part_id.is_some() {
            param_count += 1;
            where_conditions.push(format!("pb.part_id = ${}", param_count));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // Get total count
        let count_query = format!(
            "SELECT COUNT(*) FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts pt ON pb.part_id = pt.id
             {}",
            where_clause
        );

        let total_count =
            if query.project_id.is_some() || query.status.is_some() || query.part_id.is_some() {
                // Use dynamic query for count with parameters
                self.execute_count_query_with_params(&count_query, &query)
                    .await?
            } else {
                sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders")
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?
                    .unwrap_or(0)
            };

        // Get work orders
        let data_query = format!(
            "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                    pb.project_id, p.project_name, p.customer_name,
                    pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                    pb.quantity as bom_quantity
             FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts pt ON pb.part_id = pt.id
             {}
             ORDER BY wo.created_at DESC
             LIMIT ${} OFFSET ${}",
            where_clause,
            param_count + 1,
            param_count + 2
        );

        let work_orders = if query.project_id.is_some()
            || query.status.is_some()
            || query.part_id.is_some()
        {
            self.execute_data_query_with_params(&data_query, &query, limit, offset)
                .await?
        } else {
            sqlx::query_as!(
                WorkOrderWithDetails,
                "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                        pb.project_id, p.project_name, p.customer_name,
                        pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                        pb.quantity as bom_quantity
                 FROM work_orders wo
                 JOIN project_boms pb ON wo.project_bom_id = pb.id
                 JOIN projects p ON pb.project_id = p.id
                 JOIN parts pt ON pb.part_id = pt.id
                 ORDER BY wo.created_at DESC
                 LIMIT $1 OFFSET $2",
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        };

        Ok(WorkOrderSearchResult {
            work_orders,
            total_count,
            limit,
            offset,
        })
    }

    async fn execute_count_query_with_params(
        &self,
        _query: &str,
        params: &WorkOrderQuery,
    ) -> Result<i64, String> {
        // This is a simplified implementation. In a real application, you'd want to use a query builder
        // or a more sophisticated approach to handle dynamic queries safely.
        if let Some(project_id) = params.project_id {
            if let Some(status) = &params.status {
                if let Some(part_id) = params.part_id {
                    sqlx::query_scalar!(
                        "SELECT COUNT(*) FROM work_orders wo
                         JOIN project_boms pb ON wo.project_bom_id = pb.id
                         WHERE pb.project_id = $1 AND wo.status = $2 AND pb.part_id = $3",
                        project_id,
                        status,
                        part_id
                    )
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))
                    .map(|count| count.unwrap_or(0))
                } else {
                    sqlx::query_scalar!(
                        "SELECT COUNT(*) FROM work_orders wo
                         JOIN project_boms pb ON wo.project_bom_id = pb.id
                         WHERE pb.project_id = $1 AND wo.status = $2",
                        project_id,
                        status
                    )
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))
                    .map(|count| count.unwrap_or(0))
                }
            } else if let Some(part_id) = params.part_id {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM work_orders wo
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     WHERE pb.project_id = $1 AND pb.part_id = $2",
                    project_id,
                    part_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            } else {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM work_orders wo
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     WHERE pb.project_id = $1",
                    project_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            }
        } else if let Some(status) = &params.status {
            if let Some(part_id) = params.part_id {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM work_orders wo
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     WHERE wo.status = $1 AND pb.part_id = $2",
                    status,
                    part_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            } else {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM work_orders wo WHERE wo.status = $1",
                    status
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            }
        } else if let Some(part_id) = params.part_id {
            sqlx::query_scalar!(
                "SELECT COUNT(*) FROM work_orders wo
                 JOIN project_boms pb ON wo.project_bom_id = pb.id
                 WHERE pb.part_id = $1",
                part_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))
            .map(|count| count.unwrap_or(0))
        } else {
            Ok(0)
        }
    }

    async fn execute_data_query_with_params(
        &self,
        _query: &str,
        params: &WorkOrderQuery,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<WorkOrderWithDetails>, String> {
        // Similar to count query, this is a simplified implementation
        if let Some(project_id) = params.project_id {
            if let Some(status) = &params.status {
                if let Some(part_id) = params.part_id {
                    sqlx::query_as!(
                        WorkOrderWithDetails,
                        "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                                pb.project_id, p.project_name, p.customer_name,
                                pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                                pb.quantity as bom_quantity
                         FROM work_orders wo
                         JOIN project_boms pb ON wo.project_bom_id = pb.id
                         JOIN projects p ON pb.project_id = p.id
                         JOIN parts pt ON pb.part_id = pt.id
                         WHERE pb.project_id = $1 AND wo.status = $2 AND pb.part_id = $3
                         ORDER BY wo.created_at DESC
                         LIMIT $4 OFFSET $5",
                        project_id, status, part_id, limit, offset
                    )
                    .fetch_all(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))
                } else {
                    sqlx::query_as!(
                        WorkOrderWithDetails,
                        "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                                pb.project_id, p.project_name, p.customer_name,
                                pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                                pb.quantity as bom_quantity
                         FROM work_orders wo
                         JOIN project_boms pb ON wo.project_bom_id = pb.id
                         JOIN projects p ON pb.project_id = p.id
                         JOIN parts pt ON pb.part_id = pt.id
                         WHERE pb.project_id = $1 AND wo.status = $2
                         ORDER BY wo.created_at DESC
                         LIMIT $3 OFFSET $4",
                        project_id, status, limit, offset
                    )
                    .fetch_all(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))
                }
            } else if let Some(part_id) = params.part_id {
                sqlx::query_as!(
                    WorkOrderWithDetails,
                    "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                            pb.project_id, p.project_name, p.customer_name,
                            pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                            pb.quantity as bom_quantity
                     FROM work_orders wo
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     JOIN projects p ON pb.project_id = p.id
                     JOIN parts pt ON pb.part_id = pt.id
                     WHERE pb.project_id = $1 AND pb.part_id = $2
                     ORDER BY wo.created_at DESC
                     LIMIT $3 OFFSET $4",
                    project_id, part_id, limit, offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
            } else {
                sqlx::query_as!(
                    WorkOrderWithDetails,
                    "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                            pb.project_id, p.project_name, p.customer_name,
                            pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                            pb.quantity as bom_quantity
                     FROM work_orders wo
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     JOIN projects p ON pb.project_id = p.id
                     JOIN parts pt ON pb.part_id = pt.id
                     WHERE pb.project_id = $1
                     ORDER BY wo.created_at DESC
                     LIMIT $2 OFFSET $3",
                    project_id, limit, offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
            }
        } else {
            // Handle other parameter combinations...
            Ok(Vec::new())
        }
    }

    pub async fn update_work_order(
        &self,
        work_order_id: i32,
        request: UpdateWorkOrderRequest,
    ) -> Result<Option<WorkOrder>, String> {
        // Validate status if provided
        if let Some(ref status) = request.status {
            let valid_statuses = get_valid_work_order_statuses();
            if !valid_statuses.contains(&status.as_str()) {
                return Err(format!(
                    "Invalid status. Valid statuses are: {:?}",
                    valid_statuses
                ));
            }
        }

        // Check if work order exists
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM work_orders WHERE id = $1)",
            work_order_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !exists.unwrap_or(false) {
            return Ok(None);
        }

        // Build update query dynamically
        let mut set_clauses = Vec::new();
        let mut param_count = 0;

        if request.quantity.is_some() {
            param_count += 1;
            set_clauses.push(format!("quantity = ${}", param_count));
        }

        if request.status.is_some() {
            param_count += 1;
            set_clauses.push(format!("status = ${}", param_count));
        }

        if request.due_date.is_some() {
            param_count += 1;
            set_clauses.push(format!("due_date = ${}", param_count));
        }

        if set_clauses.is_empty() {
            // No updates requested, return current work order
            return self.get_work_order_basic(work_order_id).await;
        }

        // Execute update based on what fields are being updated
        let work_order = if let Some(quantity) = request.quantity {
            if let Some(ref status) = request.status {
                if let Some(due_date) = request.due_date {
                    sqlx::query_as!(
                        WorkOrder,
                        "UPDATE work_orders SET quantity = $1, status = $2, due_date = $3
                         WHERE id = $4
                         RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                        quantity,
                        status,
                        due_date,
                        work_order_id
                    )
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?
                } else {
                    sqlx::query_as!(
                        WorkOrder,
                        "UPDATE work_orders SET quantity = $1, status = $2
                         WHERE id = $3
                         RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                        quantity,
                        status,
                        work_order_id
                    )
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?
                }
            } else if let Some(due_date) = request.due_date {
                sqlx::query_as!(
                    WorkOrder,
                    "UPDATE work_orders SET quantity = $1, due_date = $2
                     WHERE id = $3
                     RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                    quantity,
                    due_date,
                    work_order_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
            } else {
                sqlx::query_as!(
                    WorkOrder,
                    "UPDATE work_orders SET quantity = $1
                     WHERE id = $2
                     RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                    quantity,
                    work_order_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
            }
        } else if let Some(ref status) = request.status {
            if let Some(due_date) = request.due_date {
                sqlx::query_as!(
                    WorkOrder,
                    "UPDATE work_orders SET status = $1, due_date = $2
                     WHERE id = $3
                     RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                    status,
                    due_date,
                    work_order_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
            } else {
                sqlx::query_as!(
                    WorkOrder,
                    "UPDATE work_orders SET status = $1
                     WHERE id = $2
                     RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                    status,
                    work_order_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
            }
        } else if let Some(due_date) = request.due_date {
            sqlx::query_as!(
                WorkOrder,
                "UPDATE work_orders SET due_date = $1
                 WHERE id = $2
                 RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                due_date,
                work_order_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        } else {
            return self.get_work_order_basic(work_order_id).await;
        };

        Ok(Some(work_order))
    }

    async fn get_work_order_basic(&self, work_order_id: i32) -> Result<Option<WorkOrder>, String> {
        let work_order = sqlx::query_as!(
            WorkOrder,
            "SELECT id, project_bom_id, quantity, status, due_date, created_at
             FROM work_orders WHERE id = $1",
            work_order_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(work_order)
    }

    pub async fn delete_work_order(&self, work_order_id: i32) -> Result<bool, String> {
        let result = sqlx::query!("DELETE FROM work_orders WHERE id = $1", work_order_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn create_work_orders_from_project(
        &self,
        request: CreateWorkOrdersFromProjectRequest,
    ) -> Result<Vec<WorkOrder>, String> {
        let multiplier = request.multiplier.unwrap_or(1.0);

        // Get all BOM items for the project
        let bom_items = sqlx::query!(
            "SELECT id, part_id, quantity FROM project_boms WHERE project_id = $1",
            request.project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if bom_items.is_empty() {
            return Err("No BOM items found for the project".to_string());
        }

        let mut work_orders = Vec::new();

        for bom_item in bom_items {
            let quantity = ((bom_item.quantity as f64) * multiplier).round() as i32;

            let work_order = sqlx::query_as!(
                WorkOrder,
                "INSERT INTO work_orders (project_bom_id, quantity, due_date)
                 VALUES ($1, $2, $3)
                 RETURNING id, project_bom_id, quantity, status, due_date, created_at",
                bom_item.id,
                quantity,
                request.due_date
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            work_orders.push(work_order);
        }

        Ok(work_orders)
    }

    pub async fn get_work_orders_by_project(
        &self,
        project_id: i32,
    ) -> Result<Vec<WorkOrderWithDetails>, String> {
        let work_orders = sqlx::query_as!(
            WorkOrderWithDetails,
            "SELECT wo.id, wo.project_bom_id, wo.quantity, wo.status, wo.due_date, wo.created_at,
                    pb.project_id, p.project_name, p.customer_name,
                    pb.part_id, pt.part_number, pt.part_name, pt.version, pt.specifications,
                    pb.quantity as bom_quantity
             FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts pt ON pb.part_id = pt.id
             WHERE pb.project_id = $1
             ORDER BY wo.created_at DESC",
            project_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(work_orders)
    }
}
