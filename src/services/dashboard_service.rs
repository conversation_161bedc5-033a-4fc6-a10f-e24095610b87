use crate::models::dashboard::{
    DashboardOverview, DashboardQuery, KPIMetrics, MachineStatusSummary, MachineUtilization,
    ProductionMetrics, ProductionReport, ProductionSummary, QualityMetrics, RecentActivity,
    ReportPeriod, ReportRequest, SkillGroupPerformance, TimeSeriesPoint, TrendData,
    WorkOrderPerformance, WorkOrderStatusSummary,
};
use chrono::{Duration, Utc};
use sqlx::PgPool;

pub struct DashboardService {
    pool: PgPool,
}

impl DashboardService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_dashboard_overview(
        &self,
        query: DashboardQuery,
    ) -> Result<DashboardOverview, String> {
        let production_summary = self.get_production_summary(&query).await?;
        let machine_status = self.get_machine_status_summary(&query).await?;
        let work_order_status = self.get_work_order_status_summary(&query).await?;
        let quality_metrics = self.get_quality_metrics(&query).await?;
        let recent_activities = self.get_recent_activities(&query).await?;

        Ok(DashboardOverview {
            production_summary,
            machine_status,
            work_order_status,
            quality_metrics,
            recent_activities,
        })
    }

    async fn get_production_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<ProductionSummary, String> {
        // TODO: Implement query filtering
        let today = Utc::now().date_naive();
        let start_of_day = today.and_hms_opt(0, 0, 0).unwrap().and_utc();

        // Tasks completed today
        let tasks_completed_today = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM execution_logs 
             WHERE event_type = 'task_complete' 
             AND event_time >= $1",
            start_of_day
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Tasks in progress
        let tasks_in_progress =
            sqlx::query_scalar!("SELECT COUNT(*) FROM plan_tasks WHERE status = 'in_progress'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        // Tasks pending
        let tasks_pending = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM plan_tasks WHERE status IN ('planned', 'scheduled')"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Total work orders
        let total_work_orders = sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

        // Calculate on-time delivery rate (simplified)
        let completed_on_time = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM work_orders wo
             JOIN plan_tasks pt ON wo.id = pt.work_order_id
             WHERE wo.status = 'completed' 
             AND wo.due_date IS NOT NULL
             AND pt.planned_end <= wo.due_date::timestamptz"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        let total_completed =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'completed'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let on_time_delivery_rate = if total_completed > 0 {
            (completed_on_time as f64 / total_completed as f64) * 100.0
        } else {
            0.0
        };

        // Overall efficiency (simplified calculation)
        let overall_efficiency = 85.0; // Placeholder - would calculate based on actual vs planned times

        Ok(ProductionSummary {
            tasks_completed_today,
            tasks_in_progress,
            tasks_pending,
            total_work_orders,
            on_time_delivery_rate,
            overall_efficiency,
        })
    }

    async fn get_machine_status_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<MachineStatusSummary, String> {
        let total_machines = sqlx::query_scalar!("SELECT COUNT(*) FROM machines")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

        let available_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'available'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let in_use_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'in_use'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let maintenance_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'maintenance'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let offline_machines =
            sqlx::query_scalar!("SELECT COUNT(*) FROM machines WHERE status = 'offline'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let utilization_rate = if total_machines > 0 {
            (in_use_machines as f64 / total_machines as f64) * 100.0
        } else {
            0.0
        };

        Ok(MachineStatusSummary {
            total_machines,
            available_machines,
            in_use_machines,
            maintenance_machines,
            offline_machines,
            utilization_rate,
        })
    }

    async fn get_work_order_status_summary(
        &self,
        _query: &DashboardQuery,
    ) -> Result<WorkOrderStatusSummary, String> {
        let pending_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'pending'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let planned_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'planned'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let in_progress_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'in_progress'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let completed_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'completed'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let cancelled_orders =
            sqlx::query_scalar!("SELECT COUNT(*) FROM work_orders WHERE status = 'cancelled'")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

        let overdue_orders = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM work_orders 
             WHERE due_date < CURRENT_DATE 
             AND status NOT IN ('completed', 'cancelled')"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        Ok(WorkOrderStatusSummary {
            pending_orders,
            planned_orders,
            in_progress_orders,
            completed_orders,
            cancelled_orders,
            overdue_orders,
        })
    }

    async fn get_quality_metrics(&self, _query: &DashboardQuery) -> Result<QualityMetrics, String> {
        // Placeholder implementation - in a real system, this would query quality inspection data
        Ok(QualityMetrics {
            total_inspections: 100,
            passed_inspections: 95,
            failed_inspections: 5,
            quality_rate: 95.0,
            defect_rate: 5.0,
        })
    }

    async fn get_recent_activities(
        &self,
        _query: &DashboardQuery,
    ) -> Result<Vec<RecentActivity>, String> {
        let activities = sqlx::query!(
            "SELECT el.id, el.event_type, el.event_time, el.notes,
                    u.username, u.full_name,
                    'plan_task' as entity_type, el.plan_task_id as entity_id
             FROM execution_logs el
             JOIN users u ON el.user_id = u.id
             ORDER BY el.event_time DESC
             LIMIT 10"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let recent_activities = activities
            .into_iter()
            .map(|activity| {
                let description = match activity.event_type.as_str() {
                    "task_start" => "Started task execution".to_string(),
                    "task_complete" => "Completed task".to_string(),
                    "task_pause" => "Paused task".to_string(),
                    "task_resume" => "Resumed task".to_string(),
                    _ => format!("Performed {}", activity.event_type),
                };

                RecentActivity {
                    id: activity.id,
                    activity_type: activity.event_type,
                    description,
                    user_name: activity.full_name.unwrap_or(activity.username),
                    timestamp: activity.event_time,
                    entity_type: "plan_task".to_string(),
                    entity_id: activity.entity_id,
                }
            })
            .collect();

        Ok(recent_activities)
    }

    pub async fn get_production_report(
        &self,
        request: ReportRequest,
    ) -> Result<ProductionReport, String> {
        let production_metrics = self.calculate_production_metrics(&request.period).await?;
        let machine_utilization = self.calculate_machine_utilization(&request.period).await?;
        let work_order_performance = self
            .calculate_work_order_performance(&request.period)
            .await?;
        let skill_group_performance = self
            .calculate_skill_group_performance(&request.period)
            .await?;

        Ok(ProductionReport {
            report_period: request.period,
            production_metrics,
            machine_utilization,
            work_order_performance,
            skill_group_performance,
        })
    }

    async fn calculate_production_metrics(
        &self,
        period: &ReportPeriod,
    ) -> Result<ProductionMetrics, String> {
        let start_time = period.start_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
        let end_time = period.end_date.and_hms_opt(23, 59, 59).unwrap().and_utc();

        let total_tasks_completed = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM execution_logs 
             WHERE event_type = 'task_complete' 
             AND event_time BETWEEN $1 AND $2",
            start_time,
            end_time
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Simplified calculations - in a real system, these would be more sophisticated
        let total_production_time = total_tasks_completed * 60; // Assume 60 minutes per task
        let average_task_duration = if total_tasks_completed > 0 {
            total_production_time as f64 / total_tasks_completed as f64
        } else {
            0.0
        };

        Ok(ProductionMetrics {
            total_tasks_completed,
            total_production_time,
            average_task_duration,
            on_time_completion_rate: 90.0, // Placeholder
            efficiency_rate: 85.0,         // Placeholder
            throughput: if total_production_time > 0 {
                (total_tasks_completed as f64 * 60.0) / (total_production_time as f64 / 60.0)
            } else {
                0.0
            },
        })
    }

    async fn calculate_machine_utilization(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<MachineUtilization>, String> {
        let machines = sqlx::query!(
            "SELECT m.id, m.machine_name, sg.group_name as skill_group_name
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let utilization_data = machines
            .into_iter()
            .map(|machine| MachineUtilization {
                machine_id: machine.id,
                machine_name: machine.machine_name,
                skill_group_name: machine.skill_group_name,
                total_available_time: 480, // 8 hours in minutes
                total_used_time: 360,      // Placeholder
                utilization_rate: 75.0,    // Placeholder
                downtime: 60,              // Placeholder
                maintenance_time: 60,      // Placeholder
            })
            .collect();

        Ok(utilization_data)
    }

    async fn calculate_work_order_performance(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<WorkOrderPerformance>, String> {
        // Simplified implementation
        Ok(Vec::new())
    }

    async fn calculate_skill_group_performance(
        &self,
        _period: &ReportPeriod,
    ) -> Result<Vec<SkillGroupPerformance>, String> {
        let skill_groups = sqlx::query!(
            "SELECT sg.id, sg.group_name,
                    COUNT(pt.id) as total_tasks,
                    COUNT(CASE WHEN pt.status = 'completed' THEN 1 END) as completed_tasks
             FROM skill_groups sg
             LEFT JOIN plan_tasks pt ON sg.id = pt.skill_group_id
             GROUP BY sg.id, sg.group_name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let performance_data = skill_groups
            .into_iter()
            .map(|sg| {
                let total_tasks = sg.total_tasks.unwrap_or(0);
                let completed_tasks = sg.completed_tasks.unwrap_or(0);

                let efficiency_rate = if total_tasks > 0 {
                    (completed_tasks as f64 / total_tasks as f64) * 100.0
                } else {
                    0.0
                };

                SkillGroupPerformance {
                    skill_group_id: sg.id,
                    skill_group_name: sg.group_name,
                    total_tasks,
                    completed_tasks,
                    average_task_time: 60.0, // Placeholder
                    efficiency_rate,
                    utilization_rate: 80.0, // Placeholder
                }
            })
            .collect();

        Ok(performance_data)
    }

    pub async fn get_kpi_metrics(&self) -> Result<KPIMetrics, String> {
        // Placeholder implementation - in a real system, these would be calculated from actual data
        Ok(KPIMetrics {
            overall_equipment_effectiveness: 85.0,
            first_pass_yield: 95.0,
            cycle_time_variance: 5.0,
            schedule_adherence: 90.0,
            resource_utilization: 80.0,
            cost_per_unit: 25.50,
        })
    }

    pub async fn get_trend_data(&self, metric_name: &str, days: i32) -> Result<TrendData, String> {
        let end_date = Utc::now();
        let start_date = end_date - Duration::days(days as i64);

        // Simplified implementation - generate sample trend data
        let mut time_series = Vec::new();
        for i in 0..days {
            let timestamp = start_date + Duration::days(i as i64);
            let value = 80.0 + (i as f64 * 0.5) + (i as f64 % 7.0) * 2.0; // Sample trending data
            time_series.push(TimeSeriesPoint { timestamp, value });
        }

        let first_value = time_series.first().map(|p| p.value).unwrap_or(0.0);
        let last_value = time_series.last().map(|p| p.value).unwrap_or(0.0);
        let percentage_change = if first_value > 0.0 {
            ((last_value - first_value) / first_value) * 100.0
        } else {
            0.0
        };

        let trend_direction = if percentage_change > 1.0 {
            "up"
        } else if percentage_change < -1.0 {
            "down"
        } else {
            "stable"
        };

        Ok(TrendData {
            metric_name: metric_name.to_string(),
            time_series,
            trend_direction: trend_direction.to_string(),
            percentage_change,
        })
    }
}
