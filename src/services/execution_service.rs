use crate::models::execution_log::{
    ActiveTaskInfo, ActiveTasksResponse, BarcodeValidationRequest, BarcodeValidationResponse,
    CreateExecutionLogRequest, EVENT_TYPE_TASK_CANCEL, EVENT_TYPE_TASK_COMPLETE,
    EVENT_TYPE_TASK_PAUSE, EVENT_TYPE_TASK_RESUME, EVENT_TYPE_TASK_START, ExecutionLog,
    ExecutionLogQuery, ExecutionLogSearchResult, ExecutionLogWithDetails, MachineStatusInfo,
    PendingTaskInfo, ShopFloorDashboard, TaskExecutionRequest, TaskExecutionResponse,
    get_valid_event_types,
};
use crate::models::plan_task::{PLAN_TASK_STATUS_COMPLETED, PLAN_TASK_STATUS_IN_PROGRESS};
use chrono::Utc;
use sqlx::PgPool;

pub struct ExecutionService {
    pool: PgPool,
}

impl ExecutionService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_execution_log(
        &self,
        user_id: i32,
        request: CreateExecutionLogRequest,
    ) -> Result<ExecutionLog, String> {
        // Validate event type
        let valid_event_types = get_valid_event_types();
        if !valid_event_types.contains(&request.event_type.as_str()) {
            return Err(format!(
                "Invalid event type. Valid types are: {:?}",
                valid_event_types
            ));
        }

        // Validate plan task exists
        let plan_task_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM plan_tasks WHERE id = $1)",
            request.plan_task_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !plan_task_exists.unwrap_or(false) {
            return Err("Plan task not found".to_string());
        }

        // Validate machine if provided
        if let Some(machine_id) = request.machine_id {
            let machine_exists = sqlx::query_scalar!(
                "SELECT EXISTS(SELECT 1 FROM machines WHERE id = $1)",
                machine_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            if !machine_exists.unwrap_or(false) {
                return Err("Machine not found".to_string());
            }
        }

        let execution_log = sqlx::query_as!(
            ExecutionLog,
            "INSERT INTO execution_logs (plan_task_id, machine_id, user_id, event_type, notes)
             VALUES ($1, $2, $3, $4, $5)
             RETURNING id, plan_task_id, machine_id, user_id, event_type, event_time, notes",
            request.plan_task_id,
            request.machine_id,
            user_id,
            request.event_type,
            request.notes
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(execution_log)
    }

    pub async fn start_task(
        &self,
        user_id: i32,
        request: TaskExecutionRequest,
    ) -> Result<TaskExecutionResponse, String> {
        // Check if task is already in progress
        let current_status = sqlx::query_scalar!(
            "SELECT status FROM plan_tasks WHERE id = $1",
            request.plan_task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let current_status = current_status.ok_or("Plan task not found")?;

        if current_status == PLAN_TASK_STATUS_IN_PROGRESS {
            return Ok(TaskExecutionResponse {
                success: false,
                message: "Task is already in progress".to_string(),
                execution_log: None,
                updated_task_status: None,
            });
        }

        if current_status == PLAN_TASK_STATUS_COMPLETED {
            return Ok(TaskExecutionResponse {
                success: false,
                message: "Task is already completed".to_string(),
                execution_log: None,
                updated_task_status: None,
            });
        }

        // Create execution log
        let log_request = CreateExecutionLogRequest {
            plan_task_id: request.plan_task_id,
            machine_id: request.machine_id,
            event_type: EVENT_TYPE_TASK_START.to_string(),
            notes: request.notes,
        };

        let execution_log = self.create_execution_log(user_id, log_request).await?;

        // Update plan task status
        sqlx::query!(
            "UPDATE plan_tasks SET status = $1 WHERE id = $2",
            PLAN_TASK_STATUS_IN_PROGRESS,
            request.plan_task_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Update machine status if provided
        if let Some(machine_id) = request.machine_id {
            sqlx::query!(
                "UPDATE machines SET status = 'in_use' WHERE id = $1",
                machine_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        Ok(TaskExecutionResponse {
            success: true,
            message: "Task started successfully".to_string(),
            execution_log: Some(execution_log),
            updated_task_status: Some(PLAN_TASK_STATUS_IN_PROGRESS.to_string()),
        })
    }

    pub async fn complete_task(
        &self,
        user_id: i32,
        request: TaskExecutionRequest,
    ) -> Result<TaskExecutionResponse, String> {
        // Check if task is in progress
        let current_status = sqlx::query_scalar!(
            "SELECT status FROM plan_tasks WHERE id = $1",
            request.plan_task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let current_status = current_status.ok_or("Plan task not found")?;

        if current_status != PLAN_TASK_STATUS_IN_PROGRESS {
            return Ok(TaskExecutionResponse {
                success: false,
                message: "Task must be in progress to complete".to_string(),
                execution_log: None,
                updated_task_status: None,
            });
        }

        // Create execution log
        let log_request = CreateExecutionLogRequest {
            plan_task_id: request.plan_task_id,
            machine_id: request.machine_id,
            event_type: EVENT_TYPE_TASK_COMPLETE.to_string(),
            notes: request.notes,
        };

        let execution_log = self.create_execution_log(user_id, log_request).await?;

        // Update plan task status
        sqlx::query!(
            "UPDATE plan_tasks SET status = $1 WHERE id = $2",
            PLAN_TASK_STATUS_COMPLETED,
            request.plan_task_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Update machine status if provided
        if let Some(machine_id) = request.machine_id {
            sqlx::query!(
                "UPDATE machines SET status = 'available' WHERE id = $1",
                machine_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        Ok(TaskExecutionResponse {
            success: true,
            message: "Task completed successfully".to_string(),
            execution_log: Some(execution_log),
            updated_task_status: Some(PLAN_TASK_STATUS_COMPLETED.to_string()),
        })
    }

    pub async fn pause_task(
        &self,
        user_id: i32,
        request: TaskExecutionRequest,
    ) -> Result<TaskExecutionResponse, String> {
        // Check if task is in progress
        let current_status = sqlx::query_scalar!(
            "SELECT status FROM plan_tasks WHERE id = $1",
            request.plan_task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let current_status = current_status.ok_or("Plan task not found")?;

        if current_status != PLAN_TASK_STATUS_IN_PROGRESS {
            return Ok(TaskExecutionResponse {
                success: false,
                message: "Task must be in progress to pause".to_string(),
                execution_log: None,
                updated_task_status: None,
            });
        }

        // Create execution log
        let log_request = CreateExecutionLogRequest {
            plan_task_id: request.plan_task_id,
            machine_id: request.machine_id,
            event_type: EVENT_TYPE_TASK_PAUSE.to_string(),
            notes: request.notes,
        };

        let execution_log = self.create_execution_log(user_id, log_request).await?;

        // Update machine status if provided
        if let Some(machine_id) = request.machine_id {
            sqlx::query!(
                "UPDATE machines SET status = 'available' WHERE id = $1",
                machine_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        Ok(TaskExecutionResponse {
            success: true,
            message: "Task paused successfully".to_string(),
            execution_log: Some(execution_log),
            updated_task_status: Some(current_status),
        })
    }

    pub async fn resume_task(
        &self,
        user_id: i32,
        request: TaskExecutionRequest,
    ) -> Result<TaskExecutionResponse, String> {
        // Check if task was paused (has a pause event but no resume after it)
        let last_event = sqlx::query_scalar!(
            "SELECT event_type FROM execution_logs 
             WHERE plan_task_id = $1 
             ORDER BY event_time DESC 
             LIMIT 1",
            request.plan_task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if last_event != Some(EVENT_TYPE_TASK_PAUSE.to_string()) {
            return Ok(TaskExecutionResponse {
                success: false,
                message: "Task must be paused to resume".to_string(),
                execution_log: None,
                updated_task_status: None,
            });
        }

        // Create execution log
        let log_request = CreateExecutionLogRequest {
            plan_task_id: request.plan_task_id,
            machine_id: request.machine_id,
            event_type: EVENT_TYPE_TASK_RESUME.to_string(),
            notes: request.notes,
        };

        let execution_log = self.create_execution_log(user_id, log_request).await?;

        // Update machine status if provided
        if let Some(machine_id) = request.machine_id {
            sqlx::query!(
                "UPDATE machines SET status = 'in_use' WHERE id = $1",
                machine_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        Ok(TaskExecutionResponse {
            success: true,
            message: "Task resumed successfully".to_string(),
            execution_log: Some(execution_log),
            updated_task_status: Some(PLAN_TASK_STATUS_IN_PROGRESS.to_string()),
        })
    }

    pub async fn validate_barcode(
        &self,
        request: BarcodeValidationRequest,
    ) -> Result<BarcodeValidationResponse, String> {
        // Simple barcode validation simulation
        // In a real system, this would integrate with actual barcode scanning hardware/software

        // For simulation, assume barcode format: "WO{work_order_id}-{part_number}"
        if let Some(captures) = regex::Regex::new(r"^WO(\d+)-(.+)$")
            .unwrap()
            .captures(&request.barcode)
        {
            let work_order_id: i32 = captures[1]
                .parse()
                .map_err(|_| "Invalid work order ID in barcode")?;
            let _part_number = captures[2].to_string(); // TODO: Validate part number

            // Validate work order exists
            let work_order_info = sqlx::query!(
                "SELECT wo.id, pb.part_id, p.part_number 
                 FROM work_orders wo
                 JOIN project_boms pb ON wo.project_bom_id = pb.id
                 JOIN parts p ON pb.part_id = p.id
                 WHERE wo.id = $1",
                work_order_id
            )
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            if let Some(info) = work_order_info {
                let valid = if let Some(expected_part) = &request.expected_part_number {
                    info.part_number == *expected_part
                } else if let Some(expected_wo) = request.expected_work_order_id {
                    info.id == expected_wo
                } else {
                    true
                };

                // Find associated plan task
                let plan_task_id = sqlx::query_scalar!(
                    "SELECT id FROM plan_tasks WHERE work_order_id = $1 LIMIT 1",
                    work_order_id
                )
                .fetch_optional(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                Ok(BarcodeValidationResponse {
                    valid,
                    message: if valid {
                        "Barcode validated successfully".to_string()
                    } else {
                        "Barcode does not match expected values".to_string()
                    },
                    part_number: Some(info.part_number),
                    work_order_id: Some(info.id),
                    plan_task_id,
                })
            } else {
                Ok(BarcodeValidationResponse {
                    valid: false,
                    message: "Work order not found".to_string(),
                    part_number: None,
                    work_order_id: None,
                    plan_task_id: None,
                })
            }
        } else {
            Ok(BarcodeValidationResponse {
                valid: false,
                message: "Invalid barcode format. Expected: WO{work_order_id}-{part_number}"
                    .to_string(),
                part_number: None,
                work_order_id: None,
                plan_task_id: None,
            })
        }
    }

    pub async fn get_active_tasks(&self, user_id: i32) -> Result<ActiveTasksResponse, String> {
        // Get user info
        let user = sqlx::query!(
            "SELECT username, full_name FROM users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let user = user.ok_or("User not found")?;

        // Get active tasks for the user (tasks that have been started but not completed)
        let active_tasks = sqlx::query!(
            "SELECT DISTINCT pt.id as plan_task_id, pt.work_order_id, pt.planned_end,
                    parts.part_number, r.process_name, m.machine_name, pt.status,
                    el.event_time as started_at
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             JOIN routings r ON pt.routing_step_id = r.id
             LEFT JOIN machines m ON pt.skill_group_id = m.skill_group_id
             JOIN execution_logs el ON pt.id = el.plan_task_id
             WHERE pt.status = $1
             AND el.event_type = $2
             AND el.user_id = $3
             AND NOT EXISTS (
                 SELECT 1 FROM execution_logs el2
                 WHERE el2.plan_task_id = pt.id
                 AND el2.event_type IN ($4, $5)
                 AND el2.event_time > el.event_time
             )
             ORDER BY el.event_time ASC",
            PLAN_TASK_STATUS_IN_PROGRESS,
            EVENT_TYPE_TASK_START,
            user_id,
            EVENT_TYPE_TASK_COMPLETE,
            EVENT_TYPE_TASK_CANCEL
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let active_task_infos: Vec<ActiveTaskInfo> = active_tasks
            .into_iter()
            .map(|task| ActiveTaskInfo {
                plan_task_id: task.plan_task_id,
                work_order_id: task.work_order_id,
                part_number: task.part_number,
                process_name: task.process_name,
                machine_name: Some(task.machine_name),
                started_at: task.started_at,
                planned_end: task.planned_end,
                status: task.status,
            })
            .collect();

        Ok(ActiveTasksResponse {
            active_tasks: active_task_infos,
            user_id,
            username: user.username,
        })
    }

    pub async fn get_shop_floor_dashboard(
        &self,
        skill_group_id: Option<i32>,
    ) -> Result<ShopFloorDashboard, String> {
        // Get active tasks - simplified query to avoid type conflicts
        let active_tasks_query = sqlx::query!(
            "SELECT DISTINCT pt.id as plan_task_id, pt.work_order_id, pt.planned_end,
                    parts.part_number, r.process_name, el.event_time as started_at, pt.status
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             JOIN routings r ON pt.routing_step_id = r.id
             JOIN execution_logs el ON pt.id = el.plan_task_id
             WHERE pt.status = $1
             AND el.event_type = $2
             AND ($3::int IS NULL OR pt.skill_group_id = $3)
             AND NOT EXISTS (
                 SELECT 1 FROM execution_logs el2
                 WHERE el2.plan_task_id = pt.id
                 AND el2.event_type IN ($4, $5)
                 AND el2.event_time > el.event_time
             )
             ORDER BY el.event_time ASC",
            PLAN_TASK_STATUS_IN_PROGRESS,
            EVENT_TYPE_TASK_START,
            skill_group_id,
            EVENT_TYPE_TASK_COMPLETE,
            EVENT_TYPE_TASK_CANCEL
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let active_tasks: Vec<ActiveTaskInfo> = active_tasks_query
            .into_iter()
            .map(|task| ActiveTaskInfo {
                plan_task_id: task.plan_task_id,
                work_order_id: task.work_order_id,
                part_number: task.part_number,
                process_name: task.process_name,
                machine_name: None, // Simplified for now
                started_at: task.started_at,
                planned_end: task.planned_end,
                status: task.status,
            })
            .collect();

        // Get pending tasks - simplified query
        let pending_tasks_query = sqlx::query!(
            "SELECT pt.id as plan_task_id, pt.work_order_id, pt.planned_start,
                    parts.part_number, r.process_name
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             JOIN routings r ON pt.routing_step_id = r.id
             WHERE pt.status IN ('planned', 'scheduled')
             AND ($1::int IS NULL OR pt.skill_group_id = $1)
             ORDER BY pt.planned_start ASC
             LIMIT 10",
            skill_group_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let pending_tasks: Vec<PendingTaskInfo> = pending_tasks_query
            .into_iter()
            .map(|task| PendingTaskInfo {
                plan_task_id: task.plan_task_id,
                work_order_id: task.work_order_id,
                part_number: task.part_number,
                process_name: task.process_name,
                planned_start: task.planned_start,
                priority: "medium".to_string(), // Simplified for now
            })
            .collect();

        // Get completed tasks today
        let completed_today = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM execution_logs
             WHERE event_type = $1
             AND DATE(event_time) = CURRENT_DATE",
            EVENT_TYPE_TASK_COMPLETE
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(0);

        // Get machine status - simplified query
        let machine_status_query = sqlx::query!(
            "SELECT m.id, m.machine_name, m.status, sg.group_name as skill_group_name
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE ($1::int IS NULL OR m.skill_group_id = $1)
             ORDER BY sg.group_name, m.machine_name",
            skill_group_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let machine_status: Vec<MachineStatusInfo> = machine_status_query
            .into_iter()
            .map(|machine| MachineStatusInfo {
                machine_id: machine.id,
                machine_name: machine.machine_name,
                status: machine.status,
                current_task: None, // TODO: Get current task if machine is in use
                skill_group_name: machine.skill_group_name,
            })
            .collect();

        Ok(ShopFloorDashboard {
            active_tasks,
            pending_tasks,
            completed_today,
            machine_status,
        })
    }

    pub async fn get_execution_logs(
        &self,
        query: ExecutionLogQuery,
    ) -> Result<ExecutionLogSearchResult, String> {
        let limit = query.limit.unwrap_or(50).min(100);
        let offset = query.offset.unwrap_or(0);

        // Simple implementation - in production you'd want more sophisticated filtering
        let logs = if let Some(plan_task_id) = query.plan_task_id {
            sqlx::query_as!(
                ExecutionLog,
                "SELECT id, plan_task_id, machine_id, user_id, event_type, event_time, notes
                 FROM execution_logs
                 WHERE plan_task_id = $1
                 ORDER BY event_time DESC
                 LIMIT $2 OFFSET $3",
                plan_task_id,
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        } else {
            sqlx::query_as!(
                ExecutionLog,
                "SELECT id, plan_task_id, machine_id, user_id, event_type, event_time, notes
                 FROM execution_logs
                 ORDER BY event_time DESC
                 LIMIT $1 OFFSET $2",
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        };

        let total_count = if query.plan_task_id.is_some() {
            sqlx::query_scalar!(
                "SELECT COUNT(*) FROM execution_logs WHERE plan_task_id = $1",
                query.plan_task_id.unwrap()
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0)
        } else {
            sqlx::query_scalar!("SELECT COUNT(*) FROM execution_logs")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0)
        };

        // Convert to detailed logs (simplified for now)
        let execution_logs: Vec<ExecutionLogWithDetails> = logs
            .into_iter()
            .map(|log| ExecutionLogWithDetails {
                id: log.id,
                plan_task_id: log.plan_task_id,
                machine_id: log.machine_id,
                user_id: log.user_id,
                event_type: log.event_type,
                event_time: log.event_time,
                notes: log.notes,
                // Simplified - in production you'd join with other tables
                work_order_id: 0,
                routing_step_id: 0,
                skill_group_id: 0,
                planned_start: Utc::now(),
                planned_end: Utc::now(),
                task_status: "unknown".to_string(),
                work_order_quantity: 0,
                work_order_status: "unknown".to_string(),
                project_id: 0,
                project_name: "Unknown".to_string(),
                customer_name: None,
                part_id: 0,
                part_number: "Unknown".to_string(),
                part_name: None,
                version: "Unknown".to_string(),
                step_number: 0,
                process_name: "Unknown".to_string(),
                work_instructions: None,
                username: "Unknown".to_string(),
                user_full_name: None,
                machine_name: None,
                skill_group_name: "Unknown".to_string(),
            })
            .collect();

        Ok(ExecutionLogSearchResult {
            execution_logs,
            total_count,
            limit,
            offset,
        })
    }
}
