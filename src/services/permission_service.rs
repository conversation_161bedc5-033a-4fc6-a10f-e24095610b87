use sqlx::PgPool;
use std::collections::HashMap;

#[derive(Debu<PERSON>, Clone)]
pub struct PermissionService {
    pool: PgPool,
}

impl PermissionService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 检查用户是否有指定权限
    pub async fn has_permission(&self, user_id: i32, permission_code: &str) -> Result<bool, String> {
        let result = sqlx::query!(
            "SELECT COUNT(*) as count
             FROM user_roles ur
             JOIN role_permissions rp ON ur.role_id = rp.role_id
             JOIN permissions p ON rp.permission_id = p.id
             WHERE ur.user_id = $1 
             AND p.permission_code = $2 
             AND rp.granted = true 
             AND p.is_active = true",
            user_id,
            permission_code
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.count.unwrap_or(0) > 0)
    }

    /// 检查用户是否有任一权限
    pub async fn has_any_permission(&self, user_id: i32, permission_codes: &[&str]) -> Result<bool, String> {
        if permission_codes.is_empty() {
            return Ok(false);
        }

        let permission_codes_vec: Vec<String> = permission_codes.iter().map(|s| s.to_string()).collect();
        let result = sqlx::query!(
            "SELECT COUNT(*) as count
             FROM user_roles ur
             JOIN role_permissions rp ON ur.role_id = rp.role_id
             JOIN permissions p ON rp.permission_id = p.id
             WHERE ur.user_id = $1
             AND p.permission_code = ANY($2)
             AND rp.granted = true
             AND p.is_active = true",
            user_id,
            &permission_codes_vec
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.count.unwrap_or(0) > 0)
    }

    /// 获取用户的所有权限
    pub async fn get_user_permissions(&self, user_id: i32) -> Result<Vec<String>, String> {
        let permissions = sqlx::query!(
            "SELECT DISTINCT p.permission_code
             FROM user_roles ur
             JOIN role_permissions rp ON ur.role_id = rp.role_id
             JOIN permissions p ON rp.permission_id = p.id
             WHERE ur.user_id = $1 
             AND rp.granted = true 
             AND p.is_active = true
             ORDER BY p.permission_code",
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(permissions.into_iter().map(|p| p.permission_code).collect())
    }

    /// 获取角色的所有权限
    pub async fn get_role_permissions(&self, role_id: i32) -> Result<Vec<String>, String> {
        let permissions = sqlx::query!(
            "SELECT p.permission_code
             FROM role_permissions rp
             JOIN permissions p ON rp.permission_id = p.id
             WHERE rp.role_id = $1 
             AND rp.granted = true 
             AND p.is_active = true
             ORDER BY p.permission_code",
            role_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(permissions.into_iter().map(|p| p.permission_code).collect())
    }

    /// 批量检查用户权限
    pub async fn check_user_permissions(&self, user_id: i32, permission_codes: &[&str]) -> Result<HashMap<String, bool>, String> {
        if permission_codes.is_empty() {
            return Ok(HashMap::new());
        }

        let permission_codes_vec: Vec<String> = permission_codes.iter().map(|s| s.to_string()).collect();
        let granted_permissions = sqlx::query!(
            "SELECT DISTINCT p.permission_code
             FROM user_roles ur
             JOIN role_permissions rp ON ur.role_id = rp.role_id
             JOIN permissions p ON rp.permission_id = p.id
             WHERE ur.user_id = $1
             AND p.permission_code = ANY($2)
             AND rp.granted = true
             AND p.is_active = true",
            user_id,
            &permission_codes_vec
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let granted_set: std::collections::HashSet<String> = granted_permissions
            .into_iter()
            .map(|p| p.permission_code)
            .collect();

        let mut result = HashMap::new();
        for &permission_code in permission_codes {
            result.insert(permission_code.to_string(), granted_set.contains(permission_code));
        }

        Ok(result)
    }

    /// 检查角色是否有指定权限
    pub async fn role_has_permission(&self, role_id: i32, permission_code: &str) -> Result<bool, String> {
        let result = sqlx::query!(
            "SELECT COUNT(*) as count
             FROM role_permissions rp
             JOIN permissions p ON rp.permission_id = p.id
             WHERE rp.role_id = $1 
             AND p.permission_code = $2 
             AND rp.granted = true 
             AND p.is_active = true",
            role_id,
            permission_code
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.count.unwrap_or(0) > 0)
    }

    /// 获取用户的角色ID列表
    pub async fn get_user_role_ids(&self, user_id: i32) -> Result<Vec<i32>, String> {
        let roles = sqlx::query!(
            "SELECT role_id FROM user_roles WHERE user_id = $1",
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(roles.into_iter().map(|r| r.role_id).collect())
    }

    /// 检查权限是否存在
    pub async fn permission_exists(&self, permission_code: &str) -> Result<bool, String> {
        let result = sqlx::query!(
            "SELECT COUNT(*) as count FROM permissions WHERE permission_code = $1 AND is_active = true",
            permission_code
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.count.unwrap_or(0) > 0)
    }

    /// 为角色授予权限
    pub async fn grant_permission_to_role(&self, role_id: i32, permission_id: i32, granted_by: i32) -> Result<(), String> {
        sqlx::query!(
            "INSERT INTO role_permissions (role_id, permission_id, granted, granted_by) 
             VALUES ($1, $2, true, $3)
             ON CONFLICT (role_id, permission_id) 
             DO UPDATE SET granted = true, granted_by = $3, granted_at = NOW()",
            role_id,
            permission_id,
            granted_by
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    /// 撤销角色权限
    pub async fn revoke_permission_from_role(&self, role_id: i32, permission_id: i32) -> Result<(), String> {
        sqlx::query!(
            "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
            role_id,
            permission_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(())
    }

    /// 获取权限的详细信息
    pub async fn get_permission_info(&self, permission_code: &str) -> Result<Option<crate::models::user::Permission>, String> {
        let permission = sqlx::query_as!(
            crate::models::user::Permission,
            "SELECT id, permission_code, permission_name, description, category, is_active, created_at
             FROM permissions 
             WHERE permission_code = $1",
            permission_code
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(permission)
    }
}
