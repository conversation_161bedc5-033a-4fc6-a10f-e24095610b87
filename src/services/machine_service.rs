use crate::models::machine::{
    CreateMachineRequest, Machine, MachineWithSkillGroup, UpdateMachineRequest,
};
use sqlx::PgPool;

pub struct MachineService {
    pool: PgPool,
}

impl MachineService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_all_machines(&self) -> Result<Vec<MachineWithSkillGroup>, String> {
        let machines = sqlx::query_as!(
            MachineWithSkillGroup,
            "SELECT m.id, m.machine_name, m.skill_group_id, sg.group_name as skill_group_name, m.status
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             ORDER BY m.machine_name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(machines)
    }

    pub async fn get_machine_by_id(
        &self,
        machine_id: i32,
    ) -> Result<Option<MachineWithSkillGroup>, String> {
        let machine = sqlx::query_as!(
            MachineWithSkillGroup,
            "SELECT m.id, m.machine_name, m.skill_group_id, sg.group_name as skill_group_name, m.status
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE m.id = $1",
            machine_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(machine)
    }

    pub async fn create_machine(&self, request: CreateMachineRequest) -> Result<Machine, String> {
        // Verify skill group exists
        let skill_group_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM skill_groups WHERE id = $1)",
            request.skill_group_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !skill_group_exists {
            return Err("Skill group not found".to_string());
        }

        let machine = sqlx::query_as!(
            Machine,
            "INSERT INTO machines (machine_name, skill_group_id, status)
             VALUES ($1, $2, $3)
             RETURNING id, machine_name, skill_group_id, status",
            request.machine_name,
            request.skill_group_id,
            request.status.unwrap_or_else(|| "available".to_string())
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(machine)
    }

    pub async fn update_machine(
        &self,
        machine_id: i32,
        request: UpdateMachineRequest,
    ) -> Result<Option<Machine>, String> {
        // Check if machine exists
        let machine_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM machines WHERE id = $1)",
            machine_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !machine_exists {
            return Ok(None);
        }

        // Verify skill group exists if provided
        if let Some(skill_group_id) = request.skill_group_id {
            let skill_group_exists = sqlx::query_scalar!(
                "SELECT EXISTS(SELECT 1 FROM skill_groups WHERE id = $1)",
                skill_group_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(false);

            if !skill_group_exists {
                return Err("Skill group not found".to_string());
            }
        }

        // Build dynamic update query
        let mut query = "UPDATE machines SET ".to_string();
        let mut params = Vec::new();
        let mut param_count = 1;

        if let Some(machine_name) = &request.machine_name {
            query.push_str(&format!("machine_name = ${}, ", param_count));
            params.push(machine_name.clone());
            param_count += 1;
        }

        if let Some(skill_group_id) = request.skill_group_id {
            query.push_str(&format!("skill_group_id = ${}, ", param_count));
            params.push(skill_group_id.to_string());
            param_count += 1;
        }

        if let Some(status) = &request.status {
            query.push_str(&format!("status = ${}, ", param_count));
            params.push(status.clone());
            param_count += 1;
        }

        // Remove trailing comma and space
        query.truncate(query.len() - 2);
        query.push_str(&format!(
            " WHERE id = ${} RETURNING id, machine_name, skill_group_id, status",
            param_count
        ));

        // Execute update using raw SQL since we have dynamic parameters
        let machine = if request.machine_name.is_some()
            && request.skill_group_id.is_some()
            && request.status.is_some()
        {
            sqlx::query_as!(
                Machine,
                "UPDATE machines SET machine_name = $1, skill_group_id = $2, status = $3 
                 WHERE id = $4 RETURNING id, machine_name, skill_group_id, status",
                request.machine_name.unwrap(),
                request.skill_group_id.unwrap(),
                request.status.unwrap(),
                machine_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        } else {
            // For partial updates, get current values first
            let current = sqlx::query_as!(
                Machine,
                "SELECT id, machine_name, skill_group_id, status FROM machines WHERE id = $1",
                machine_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            sqlx::query_as!(
                Machine,
                "UPDATE machines SET machine_name = $1, skill_group_id = $2, status = $3 
                 WHERE id = $4 RETURNING id, machine_name, skill_group_id, status",
                request.machine_name.unwrap_or(current.machine_name),
                request.skill_group_id.unwrap_or(current.skill_group_id),
                request.status.unwrap_or(current.status),
                machine_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
        };

        Ok(Some(machine))
    }

    pub async fn delete_machine(&self, machine_id: i32) -> Result<bool, String> {
        let result = sqlx::query!("DELETE FROM machines WHERE id = $1", machine_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_machines_by_skill_group(
        &self,
        skill_group_id: i32,
    ) -> Result<Vec<MachineWithSkillGroup>, String> {
        let machines = sqlx::query_as!(
            MachineWithSkillGroup,
            "SELECT m.id, m.machine_name, m.skill_group_id, sg.group_name as skill_group_name, m.status
             FROM machines m
             JOIN skill_groups sg ON m.skill_group_id = sg.id
             WHERE m.skill_group_id = $1
             ORDER BY m.machine_name",
            skill_group_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(machines)
    }

    pub async fn update_machine_status(
        &self,
        machine_id: i32,
        status: String,
    ) -> Result<bool, String> {
        let result = sqlx::query!(
            "UPDATE machines SET status = $1 WHERE id = $2",
            status,
            machine_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }
}
