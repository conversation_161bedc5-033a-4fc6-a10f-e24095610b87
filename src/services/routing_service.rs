use sqlx::{PgPool, types::BigDecimal};
use crate::models::routing::{
    Routing, RoutingWithPartInfo, CreateRoutingRequest, UpdateRoutingRequest,
    PartRoutingSteps, RoutingStep, RoutingQuery
};
use std::str::FromStr;

pub struct RoutingService {
    pool: PgPool,
}

impl RoutingService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_routing(&self, request: CreateRoutingRequest) -> Result<Routing, String> {
        // Verify part exists
        let part_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM parts WHERE id = $1)",
            request.part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !part_exists {
            return Err("Part not found".to_string());
        }

        // Check if step number already exists for this part
        let step_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM routings WHERE part_id = $1 AND step_number = $2)",
            request.part_id,
            request.step_number
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if step_exists {
            return Err("Step number already exists for this part".to_string());
        }

        // Convert f64 to BigDecimal for database storage
        let standard_hours_decimal = request.standard_hours
            .map(|h| BigDecimal::from_str(&h.to_string()).unwrap_or_else(|_| BigDecimal::from(0)));

        let result = sqlx::query!(
            "INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours)
             VALUES ($1, $2, $3, $4, $5)
             RETURNING id, part_id, step_number, process_name, work_instructions, standard_hours",
            request.part_id,
            request.step_number,
            request.process_name,
            request.work_instructions,
            standard_hours_decimal
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let routing = Routing {
            id: result.id,
            part_id: result.part_id,
            step_number: result.step_number,
            process_name: result.process_name,
            work_instructions: result.work_instructions,
            standard_hours: result.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
        };

        Ok(routing)
    }

    pub async fn get_all_routings(&self, query: RoutingQuery) -> Result<Vec<RoutingWithPartInfo>, String> {
        let routings = match (query.part_id, &query.process_name) {
            (Some(part_id), None) => {
                let rows = sqlx::query!(
                    "SELECT r.id, r.part_id, p.part_number, p.part_name, p.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours
                     FROM routings r
                     JOIN parts p ON r.part_id = p.id
                     WHERE r.part_id = $1
                     ORDER BY r.step_number",
                    part_id
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                rows.into_iter().map(|row| RoutingWithPartInfo {
                    id: row.id,
                    part_id: row.part_id,
                    part_number: row.part_number,
                    part_name: row.part_name,
                    version: row.version,
                    step_number: row.step_number,
                    process_name: row.process_name,
                    work_instructions: row.work_instructions,
                    standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
                }).collect()
            },
            (None, Some(process_name)) => {
                let pattern = format!("%{}%", process_name);
                let rows = sqlx::query!(
                    "SELECT r.id, r.part_id, p.part_number, p.part_name, p.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours
                     FROM routings r
                     JOIN parts p ON r.part_id = p.id
                     WHERE r.process_name ILIKE $1
                     ORDER BY p.part_number, r.step_number",
                    pattern
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                rows.into_iter().map(|row| RoutingWithPartInfo {
                    id: row.id,
                    part_id: row.part_id,
                    part_number: row.part_number,
                    part_name: row.part_name,
                    version: row.version,
                    step_number: row.step_number,
                    process_name: row.process_name,
                    work_instructions: row.work_instructions,
                    standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
                }).collect()
            },
            (Some(part_id), Some(process_name)) => {
                let pattern = format!("%{}%", process_name);
                let rows = sqlx::query!(
                    "SELECT r.id, r.part_id, p.part_number, p.part_name, p.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours
                     FROM routings r
                     JOIN parts p ON r.part_id = p.id
                     WHERE r.part_id = $1 AND r.process_name ILIKE $2
                     ORDER BY r.step_number",
                    part_id, pattern
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                rows.into_iter().map(|row| RoutingWithPartInfo {
                    id: row.id,
                    part_id: row.part_id,
                    part_number: row.part_number,
                    part_name: row.part_name,
                    version: row.version,
                    step_number: row.step_number,
                    process_name: row.process_name,
                    work_instructions: row.work_instructions,
                    standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
                }).collect()
            },
            (None, None) => {
                let rows = sqlx::query!(
                    "SELECT r.id, r.part_id, p.part_number, p.part_name, p.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours
                     FROM routings r
                     JOIN parts p ON r.part_id = p.id
                     ORDER BY p.part_number, r.step_number"
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                rows.into_iter().map(|row| RoutingWithPartInfo {
                    id: row.id,
                    part_id: row.part_id,
                    part_number: row.part_number,
                    part_name: row.part_name,
                    version: row.version,
                    step_number: row.step_number,
                    process_name: row.process_name,
                    work_instructions: row.work_instructions,
                    standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
                }).collect()
            }
        };

        Ok(routings)
    }

    pub async fn get_routing_by_id(&self, routing_id: i32) -> Result<Option<RoutingWithPartInfo>, String> {
        let row = sqlx::query!(
            "SELECT r.id, r.part_id, p.part_number, p.part_name, p.version,
                    r.step_number, r.process_name, r.work_instructions, r.standard_hours
             FROM routings r
             JOIN parts p ON r.part_id = p.id
             WHERE r.id = $1",
            routing_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let routing = row.map(|row| RoutingWithPartInfo {
            id: row.id,
            part_id: row.part_id,
            part_number: row.part_number,
            part_name: row.part_name,
            version: row.version,
            step_number: row.step_number,
            process_name: row.process_name,
            work_instructions: row.work_instructions,
            standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
        });

        Ok(routing)
    }

    pub async fn get_part_routing(&self, part_id: i32) -> Result<Option<PartRoutingSteps>, String> {
        // First get part info
        let part = sqlx::query!(
            "SELECT part_number, part_name, version FROM parts WHERE id = $1",
            part_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(part) = part {
            // Get routing steps
            let rows = sqlx::query!(
                "SELECT id, step_number, process_name, work_instructions, standard_hours
                 FROM routings
                 WHERE part_id = $1
                 ORDER BY step_number",
                part_id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let steps: Vec<RoutingStep> = rows.into_iter().map(|row| RoutingStep {
                id: row.id,
                step_number: row.step_number,
                process_name: row.process_name,
                work_instructions: row.work_instructions,
                standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
            }).collect();

            Ok(Some(PartRoutingSteps {
                part_id,
                part_number: part.part_number,
                part_name: part.part_name,
                version: part.version,
                routing_steps: steps,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_routing(&self, routing_id: i32, request: UpdateRoutingRequest) -> Result<Option<Routing>, String> {
        // Check if routing exists
        let routing_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM routings WHERE id = $1)",
            routing_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !routing_exists {
            return Ok(None);
        }

        // Get current values
        let current_row = sqlx::query!(
            "SELECT id, part_id, step_number, process_name, work_instructions, standard_hours
             FROM routings WHERE id = $1",
            routing_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let current = Routing {
            id: current_row.id,
            part_id: current_row.part_id,
            step_number: current_row.step_number,
            process_name: current_row.process_name,
            work_instructions: current_row.work_instructions,
            standard_hours: current_row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
        };

        let standard_hours = request.standard_hours.or(current.standard_hours);
        // Convert f64 to BigDecimal for database storage
        let standard_hours_decimal = standard_hours
            .map(|h| BigDecimal::from_str(&h.to_string()).unwrap_or_else(|_| BigDecimal::from(0)));

        let result = sqlx::query!(
            "UPDATE routings SET process_name = $1, work_instructions = $2, standard_hours = $3
             WHERE id = $4
             RETURNING id, part_id, step_number, process_name, work_instructions, standard_hours",
            request.process_name.unwrap_or(current.process_name),
            request.work_instructions.or(current.work_instructions),
            standard_hours_decimal,
            routing_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let routing = Routing {
            id: result.id,
            part_id: result.part_id,
            step_number: result.step_number,
            process_name: result.process_name,
            work_instructions: result.work_instructions,
            standard_hours: result.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
        };

        Ok(Some(routing))
    }

    pub async fn delete_routing(&self, routing_id: i32) -> Result<bool, String> {
        let result = sqlx::query!("DELETE FROM routings WHERE id = $1", routing_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn reorder_routing_steps(&self, part_id: i32, step_orders: Vec<(i32, i32)>) -> Result<(), String> {
        // step_orders contains (routing_id, new_step_number) pairs
        
        // Start transaction
        let mut tx = self.pool.begin().await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Verify all routing IDs belong to the specified part
        for (routing_id, _) in &step_orders {
            let belongs_to_part = sqlx::query_scalar!(
                "SELECT EXISTS(SELECT 1 FROM routings WHERE id = $1 AND part_id = $2)",
                routing_id,
                part_id
            )
            .fetch_one(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(false);

            if !belongs_to_part {
                return Err(format!("Routing {} does not belong to part {}", routing_id, part_id));
            }
        }

        // Update step numbers
        for (routing_id, new_step_number) in step_orders {
            sqlx::query!(
                "UPDATE routings SET step_number = $1 WHERE id = $2",
                new_step_number,
                routing_id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // Commit transaction
        tx.commit().await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        Ok(())
    }

    pub async fn copy_routing(&self, source_part_id: i32, target_part_id: i32) -> Result<Vec<Routing>, String> {
        // Verify both parts exist
        let source_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM parts WHERE id = $1)",
            source_part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        let target_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM parts WHERE id = $1)",
            target_part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if !source_exists {
            return Err("Source part not found".to_string());
        }

        if !target_exists {
            return Err("Target part not found".to_string());
        }

        // Check if target part already has routing
        let target_has_routing = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM routings WHERE part_id = $1)",
            target_part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if target_has_routing {
            return Err("Target part already has routing steps".to_string());
        }

        // Get source routing steps
        let source_rows = sqlx::query!(
            "SELECT id, part_id, step_number, process_name, work_instructions, standard_hours
             FROM routings WHERE part_id = $1 ORDER BY step_number",
            source_part_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let source_steps: Vec<Routing> = source_rows.into_iter().map(|row| Routing {
            id: row.id,
            part_id: row.part_id,
            step_number: row.step_number,
            process_name: row.process_name,
            work_instructions: row.work_instructions,
            standard_hours: row.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
        }).collect();

        if source_steps.is_empty() {
            return Err("Source part has no routing steps to copy".to_string());
        }

        // Copy routing steps to target part
        let mut copied_steps = Vec::new();
        for step in source_steps {
            // Convert f64 to BigDecimal for database storage
            let standard_hours_decimal = step.standard_hours
                .map(|h| BigDecimal::from_str(&h.to_string()).unwrap_or_else(|_| BigDecimal::from(0)));

            let result = sqlx::query!(
                "INSERT INTO routings (part_id, step_number, process_name, work_instructions, standard_hours)
                 VALUES ($1, $2, $3, $4, $5)
                 RETURNING id, part_id, step_number, process_name, work_instructions, standard_hours",
                target_part_id,
                step.step_number,
                step.process_name,
                step.work_instructions,
                standard_hours_decimal
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let new_step = Routing {
                id: result.id,
                part_id: result.part_id,
                step_number: result.step_number,
                process_name: result.process_name,
                work_instructions: result.work_instructions,
                standard_hours: result.standard_hours.and_then(|bd| bd.to_string().parse().ok()),
            };

            copied_steps.push(new_step);
        }

        Ok(copied_steps)
    }
}
