use crate::{
    models::user::{User, UserWithRolesAndSkills},
    utils::{
        jwt, password,
        validation::{CreateUserRequest, LoginRequest, LoginResponse, UserInfo},
    },
};
use sqlx::PgPool;

pub struct AuthService {
    pool: PgPool,
}

impl AuthService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn login(&self, request: LoginRequest) -> Result<LoginResponse, String> {
        // Find user by username
        let user = sqlx::query_as!(
            User,
            "SELECT id, username, password_hash, full_name, is_active, created_at FROM users WHERE username = $1",
            request.username
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let user = match user {
            Some(user) => user,
            None => return Err("Invalid username or password".to_string()),
        };

        if !user.is_active {
            return Err("Account is disabled".to_string());
        }

        // Verify password
        if !password::verify_password(&request.password, &user.password_hash)
            .map_err(|e| format!("Password verification error: {}", e))?
        {
            return Err("Invalid username or password".to_string());
        }

        // Get user roles
        let roles = sqlx::query_scalar!(
            "SELECT r.role_name FROM roles r 
             JOIN user_roles ur ON r.id = ur.role_id 
             WHERE ur.user_id = $1",
            user.id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Get user skills
        let skills = sqlx::query_scalar!(
            "SELECT sg.group_name FROM skill_groups sg 
             JOIN user_skills us ON sg.id = us.skill_group_id 
             WHERE us.user_id = $1",
            user.id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Create JWT token
        let claims = jwt::Claims::new(
            user.id,
            user.username.clone(),
            roles.clone(),
            skills.clone(),
        );
        let token = jwt::create_jwt(&claims).map_err(|e| format!("Token creation error: {}", e))?;

        let user_info = UserInfo {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            roles,
            skills,
            is_active: user.is_active,
        };

        Ok(LoginResponse {
            token,
            user: user_info,
        })
    }

    pub async fn create_user(
        &self,
        request: CreateUserRequest,
    ) -> Result<UserWithRolesAndSkills, String> {
        // Check if username already exists
        let existing_user = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)",
            request.username
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if existing_user {
            return Err("Username already exists".to_string());
        }

        // Hash password
        let password_hash = password::hash_password(&request.password)
            .map_err(|e| format!("Password hashing error: {}", e))?;

        // Start transaction
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| format!("Transaction error: {}", e))?;

        // Create user
        let user = sqlx::query_as!(
            User,
            "INSERT INTO users (username, password_hash, full_name, is_active) 
             VALUES ($1, $2, $3, true) 
             RETURNING id, username, password_hash, full_name, is_active, created_at",
            request.username,
            password_hash,
            request.full_name
        )
        .fetch_one(&mut *tx)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Assign roles
        for role_id in &request.role_ids {
            sqlx::query!(
                "INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)",
                user.id,
                role_id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // Assign skills
        for skill_id in &request.skill_group_ids {
            sqlx::query!(
                "INSERT INTO user_skills (user_id, skill_group_id) VALUES ($1, $2)",
                user.id,
                skill_id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| format!("Database error: {}", e))?;
        }

        // Commit transaction
        tx.commit()
            .await
            .map_err(|e| format!("Transaction commit error: {}", e))?;

        // Get user with roles and skills
        self.get_user_with_roles_and_skills(user.id).await
    }

    pub async fn get_user_info(&self, user_id: i32) -> Result<UserInfo, String> {
        // Get user basic info
        let user = sqlx::query_as!(
            User,
            "SELECT id, username, password_hash, full_name, is_active, created_at FROM users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let user = match user {
            Some(user) => user,
            None => return Err("User not found".to_string()),
        };

        // Get user roles
        let roles = sqlx::query_scalar!(
            "SELECT r.role_name FROM roles r
             JOIN user_roles ur ON r.id = ur.role_id
             WHERE ur.user_id = $1",
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        // Get user skills
        let skills = sqlx::query_scalar!(
            "SELECT sg.group_name FROM skill_groups sg
             JOIN user_skills us ON sg.id = us.skill_group_id
             WHERE us.user_id = $1",
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(UserInfo {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            roles,
            skills,
            is_active: user.is_active,
        })
    }

    pub async fn get_user_with_roles_and_skills(
        &self,
        user_id: i32,
    ) -> Result<UserWithRolesAndSkills, String> {
        let user = sqlx::query_as!(
            User,
            "SELECT id, username, password_hash, full_name, is_active, created_at FROM users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let user = match user {
            Some(user) => user,
            None => return Err("User not found".to_string()),
        };

        let roles = sqlx::query_scalar!(
            "SELECT r.role_name FROM roles r 
             JOIN user_roles ur ON r.id = ur.role_id 
             WHERE ur.user_id = $1",
            user.id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let skills = sqlx::query_scalar!(
            "SELECT sg.group_name FROM skill_groups sg 
             JOIN user_skills us ON sg.id = us.skill_group_id 
             WHERE us.user_id = $1",
            user.id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(UserWithRolesAndSkills {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            is_active: user.is_active,
            created_at: user.created_at,
            roles,
            skills,
        })
    }
}
