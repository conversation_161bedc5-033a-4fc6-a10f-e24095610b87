# MES 前端开发指南

## 📋 概述

基于 MES 系统的 78 个 API 端点，我们开发了一个现代化的 React 前端应用，提供完整的制造执行系统用户界面。

## 🏗️ 技术架构

### 核心技术栈
- **React 18** + **TypeScript** - 现代化前端框架
- **Vite** - 快速构建工具
- **Ant Design 5** - 企业级 UI 组件库
- **React Router 6** - 客户端路由
- **Zustand** - 轻量级状态管理
- **React Query** - 数据获取和缓存
- **Axios** - HTTP 客户端
- **Recharts** - 数据可视化
- **Tailwind CSS** - 工具优先的 CSS 框架

### 项目结构
```
frontend/
├── src/
│   ├── components/      # 通用组件
│   ├── lib/            # 工具库和 API 客户端
│   ├── pages/          # 页面组件
│   ├── store/          # 状态管理
│   ├── types/          # TypeScript 类型定义
│   ├── App.tsx         # 主应用组件
│   └── main.tsx        # 应用入口
├── public/             # 静态资源
├── package.json        # 依赖配置
├── vite.config.ts      # Vite 配置
├── tailwind.config.js  # Tailwind 配置
└── tsconfig.json       # TypeScript 配置
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 检查 Node.js 版本 (需要 >= 18)
node --version

# 进入前端目录
cd frontend

# 安装依赖
npm install
# 或
yarn install
```

### 2. 启动开发服务器
```bash
# 使用提供的启动脚本（推荐）
../start_frontend.sh

# 或手动启动
npm run dev
```

### 3. 访问应用
- 前端地址: http://localhost:3000
- 默认账号: admin / admin123

## 📱 功能模块

### 🔐 认证系统
- **登录页面** (`/login`) - JWT 认证
- **自动令牌管理** - 令牌存储和刷新
- **路由保护** - 基于认证状态的访问控制

### 📊 仪表板 (`/dashboard`)
- **生产概览** - 项目、工单、任务统计
- **实时图表** - 生产趋势和状态分布
- **KPI 指标** - 设备利用率、质量合格率

### 🏭 核心业务模块

#### 项目管理 (`/projects`)
- 项目列表展示
- 新建/编辑/删除项目
- 客户信息管理

#### 零件管理 (`/parts`)
- 零件主数据维护
- 版本控制
- 规格说明管理

#### 设备管理 (`/machines`)
- 设备状态监控
- 技能组分配
- 设备操作控制

#### 工单管理 (`/work-orders`)
- 工单状态跟踪
- 截止日期管理
- 工单详情查看

#### 生产计划 (`/plan-tasks`)
- 任务调度
- 甘特图展示
- 计划时间管理

#### 执行跟踪 (`/execution`)
- 实时任务执行
- 条码扫描支持
- 执行日志记录

#### 质量管理 (`/quality`)
- 质量检验流程
- 检验结果记录
- 质量指标统计

#### 用户管理 (`/users`)
- 用户权限管理
- 角色分配
- 技能组管理

## 🔧 开发指南

### API 集成

#### 1. 类型定义
所有 API 相关的 TypeScript 类型都定义在 `src/types/api.ts` 中：

```typescript
export interface Project {
  id: number;
  project_name: string;
  customer_name?: string;
  created_at: string;
}

export interface CreateProjectRequest {
  project_name: string;
  customer_name?: string;
}
```

#### 2. API 客户端
`src/lib/api.ts` 提供了完整的 API 客户端：

```typescript
import { apiClient } from '@/lib/api';

// 获取项目列表
const projects = await apiClient.getProjects();

// 创建新项目
const newProject = await apiClient.createProject({
  project_name: '新项目',
  customer_name: '客户名称'
});
```

#### 3. 数据获取
使用 React Query 进行数据管理：

```typescript
import { useQuery, useMutation, useQueryClient } from 'react-query';

const { data: projects, isLoading } = useQuery(
  'projects',
  () => apiClient.getProjects()
);

const createMutation = useMutation(
  (data: CreateProjectRequest) => apiClient.createProject(data),
  {
    onSuccess: () => {
      queryClient.invalidateQueries('projects');
    },
  }
);
```

### 状态管理

#### 认证状态
```typescript
import { useAuthStore } from '@/store/auth';

const { user, isAuthenticated, login, logout } = useAuthStore();
```

### 组件开发

#### 页面组件结构
```typescript
import React from 'react';
import { Table, Button, Card, Typography } from 'antd';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';

const { Title } = Typography;

const MyPage: React.FC = () => {
  const { data, isLoading } = useQuery('my-data', apiClient.getMyData);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>页面标题</Title>
      </div>
      
      <Card>
        <Table
          dataSource={data}
          loading={isLoading}
          // ... 其他配置
        />
      </Card>
    </div>
  );
};

export default MyPage;
```

#### 样式规范
- 使用 Ant Design 组件样式
- 使用 Tailwind CSS 工具类
- 自定义样式写在 `App.css` 中

```css
.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 6px;
}

.page-content {
  background: #fff;
  padding: 24px;
  border-radius: 6px;
}
```

## 🎨 UI/UX 设计

### 设计原则
- **一致性** - 统一的视觉语言和交互模式
- **效率** - 减少用户操作步骤
- **清晰** - 信息层次分明，状态明确
- **响应式** - 适配不同屏幕尺寸

### 颜色系统
- **主色** - #1890ff (蓝色)
- **成功** - #52c41a (绿色)
- **警告** - #faad14 (橙色)
- **错误** - #ff4d4f (红色)

### 状态标识
```typescript
const getStatusTag = (status: string) => {
  const statusMap = {
    active: { color: 'green', text: '活跃' },
    inactive: { color: 'red', text: '禁用' },
    pending: { color: 'orange', text: '待处理' },
  };
  return <Tag color={statusMap[status]?.color}>{statusMap[status]?.text}</Tag>;
};
```

## 📱 响应式设计

### 断点设置
- **xs**: < 576px (手机)
- **sm**: ≥ 576px (大手机)
- **md**: ≥ 768px (平板)
- **lg**: ≥ 992px (桌面)
- **xl**: ≥ 1200px (大桌面)

### 布局适配
```typescript
<Row gutter={[16, 16]}>
  <Col xs={24} sm={12} lg={8}>
    <Card>内容</Card>
  </Col>
</Row>
```

## 🔒 安全考虑

### 认证和授权
- JWT 令牌自动管理
- 路由级别的访问控制
- API 请求自动注入认证头

### 数据验证
- 前端表单验证
- TypeScript 类型检查
- API 响应数据验证

## 🚀 部署指南

### 构建生产版本
```bash
npm run build
```

### 环境变量
```env
VITE_API_BASE_URL=http://your-api-server.com/api
```

### Docker 部署
```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🧪 测试

### 单元测试
```bash
npm run test
```

### E2E 测试
```bash
npm run test:e2e
```

## 📈 性能优化

### 代码分割
- 路由级别的懒加载
- 组件按需导入

### 缓存策略
- React Query 数据缓存
- 浏览器缓存优化

### 打包优化
- Vite 自动代码分割
- 资源压缩和优化

## 🔧 开发工具

### 推荐 VS Code 插件
- TypeScript Importer
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- Auto Rename Tag

### 代码规范
- ESLint 代码检查
- Prettier 代码格式化
- TypeScript 严格模式

## 📚 学习资源

### 官方文档
- [React 官方文档](https://react.dev/)
- [Ant Design 组件库](https://ant.design/)
- [Vite 构建工具](https://vitejs.dev/)
- [React Query 数据管理](https://tanstack.com/query/latest)

### 最佳实践
- React Hooks 使用指南
- TypeScript 最佳实践
- 前端性能优化
- 用户体验设计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 📞 技术支持

如有问题，请查看：
1. 项目 README 文档
2. API 文档
3. 开发者指南
4. 提交 Issue

---

**开发愉快！** 🎉
