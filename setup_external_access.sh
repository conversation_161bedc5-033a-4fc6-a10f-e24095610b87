#!/bin/bash

# MES System External Access Setup Script
echo "🌐 MES System External Access Setup"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Get server IP address
get_server_ip() {
    # Try to get public IP
    PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || curl -s icanhazip.com 2>/dev/null)
    
    # Get local IP
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || ip route get 1 | awk '{print $7}' 2>/dev/null)
    
    echo "🔍 Detected IP Addresses:"
    if [ -n "$PUBLIC_IP" ]; then
        echo "   Public IP: $PUBLIC_IP"
    else
        echo "   Public IP: Unable to detect"
    fi
    
    if [ -n "$LOCAL_IP" ]; then
        echo "   Local IP: $LOCAL_IP"
    else
        echo "   Local IP: Unable to detect"
    fi
}

# Check if ports are available
check_ports() {
    print_step "Checking port availability"
    
    # Check backend port 8080
    if netstat -tuln 2>/dev/null | grep -q ":8080 "; then
        print_warning "Port 8080 is already in use"
    else
        print_success "Port 8080 is available"
    fi
    
    # Check frontend port 3000
    if netstat -tuln 2>/dev/null | grep -q ":3000 "; then
        print_warning "Port 3000 is already in use"
    else
        print_success "Port 3000 is available"
    fi
}

# Configure firewall (if needed)
configure_firewall() {
    print_step "Checking firewall configuration"
    
    # Check if ufw is available
    if command -v ufw >/dev/null 2>&1; then
        echo "UFW firewall detected"
        echo "To allow external access, run these commands:"
        echo "  sudo ufw allow 8080/tcp  # Backend API"
        echo "  sudo ufw allow 3000/tcp  # Frontend"
        echo ""
        
        read -p "Do you want to configure UFW firewall now? (y/N): " configure_ufw
        if [[ $configure_ufw =~ ^[Yy]$ ]]; then
            sudo ufw allow 8080/tcp
            sudo ufw allow 3000/tcp
            print_success "Firewall configured for external access"
        fi
    elif command -v firewall-cmd >/dev/null 2>&1; then
        echo "Firewalld detected"
        echo "To allow external access, run these commands:"
        echo "  sudo firewall-cmd --permanent --add-port=8080/tcp"
        echo "  sudo firewall-cmd --permanent --add-port=3000/tcp"
        echo "  sudo firewall-cmd --reload"
        echo ""
        
        read -p "Do you want to configure firewalld now? (y/N): " configure_firewalld
        if [[ $configure_firewalld =~ ^[Yy]$ ]]; then
            sudo firewall-cmd --permanent --add-port=8080/tcp
            sudo firewall-cmd --permanent --add-port=3000/tcp
            sudo firewall-cmd --reload
            print_success "Firewall configured for external access"
        fi
    else
        print_warning "No common firewall detected. You may need to manually configure firewall rules."
    fi
}

# Update CORS configuration
update_cors_config() {
    print_step "Updating CORS configuration"
    
    if [ -n "$PUBLIC_IP" ] && [ -n "$LOCAL_IP" ]; then
        # Create .env file if it doesn't exist
        if [ ! -f ".env" ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
        fi
        
        # Update CORS origins
        CORS_ORIGINS="http://localhost:3000,http://localhost:8080,http://$LOCAL_IP:3000,http://$LOCAL_IP:8080"
        if [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
            CORS_ORIGINS="$CORS_ORIGINS,http://$PUBLIC_IP:3000,http://$PUBLIC_IP:8080"
        fi
        
        # Update .env file
        if grep -q "CORS_ALLOWED_ORIGINS" .env; then
            sed -i "s|CORS_ALLOWED_ORIGINS=.*|CORS_ALLOWED_ORIGINS=$CORS_ORIGINS|" .env
        else
            echo "CORS_ALLOWED_ORIGINS=$CORS_ORIGINS" >> .env
        fi
        
        print_success "Updated CORS configuration in .env file"
        echo "   Allowed origins: $CORS_ORIGINS"
    else
        print_warning "Could not detect IP addresses. Please manually update CORS_ALLOWED_ORIGINS in .env file"
    fi
}

# Update frontend API configuration
update_frontend_config() {
    print_step "Checking frontend API configuration"
    
    if [ -f "frontend/src/lib/api.ts" ]; then
        # Check if API base URL is configured for external access
        if grep -q "baseURL.*'/api'" frontend/src/lib/api.ts; then
            print_success "Frontend API configuration looks good (relative URLs)"
        else
            print_warning "Frontend API configuration may need adjustment for external access"
            echo "   Consider using relative URLs ('/api') for better compatibility"
        fi
    else
        print_warning "Frontend API configuration file not found"
    fi
}

# Create startup script for external access
create_startup_script() {
    print_step "Creating startup script for external access"
    
    cat > start_mes_external.sh << 'EOF'
#!/bin/bash

# MES System Startup Script for External Access
echo "🚀 Starting MES System with External Access"
echo "============================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get IP addresses
PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "unknown")
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")

echo -e "${BLUE}📡 Server Access Information:${NC}"
echo "   Local Access:"
echo "     Backend:  http://localhost:8080"
echo "     Frontend: http://localhost:3000"
echo ""
echo "   Network Access:"
if [ "$LOCAL_IP" != "unknown" ]; then
    echo "     Backend:  http://$LOCAL_IP:8080"
    echo "     Frontend: http://$LOCAL_IP:3000"
fi
if [ "$PUBLIC_IP" != "unknown" ] && [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
    echo "     Backend:  http://$PUBLIC_IP:8080"
    echo "     Frontend: http://$PUBLIC_IP:3000"
fi
echo ""

# Start backend
echo -e "${GREEN}🔧 Starting Backend Server...${NC}"
cargo run &
BACKEND_PID=$!

# Wait for backend to start
sleep 5

# Start frontend
echo -e "${GREEN}🎨 Starting Frontend Server...${NC}"
cd frontend
npm run dev -- --host 0.0.0.0 &
FRONTEND_PID=$!

echo ""
echo -e "${GREEN}✅ MES System Started Successfully!${NC}"
echo ""
echo "🔗 Access URLs:"
echo "   Local: http://localhost:3000"
if [ "$LOCAL_IP" != "unknown" ]; then
    echo "   Network: http://$LOCAL_IP:3000"
fi
if [ "$PUBLIC_IP" != "unknown" ] && [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
    echo "   Public: http://$PUBLIC_IP:3000"
fi
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for interrupt
trap 'echo ""; echo "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF

    chmod +x start_mes_external.sh
    print_success "Created start_mes_external.sh script"
}

# Main execution
main() {
    echo "This script will configure MES System for external network access."
    echo ""
    
    get_server_ip
    echo ""
    
    check_ports
    echo ""
    
    configure_firewall
    echo ""
    
    update_cors_config
    echo ""
    
    update_frontend_config
    echo ""
    
    create_startup_script
    echo ""
    
    print_step "Setup Summary"
    echo "=============="
    echo "✅ Environment configuration updated"
    echo "✅ CORS settings configured for external access"
    echo "✅ Startup script created (start_mes_external.sh)"
    echo ""
    
    print_step "Next Steps"
    echo "1. Review and adjust .env file if needed"
    echo "2. Ensure firewall allows ports 3000 and 8080"
    echo "3. Run: ./start_mes_external.sh to start with external access"
    echo ""
    
    if [ -n "$LOCAL_IP" ]; then
        echo "🌐 Your MES System will be accessible at:"
        echo "   http://$LOCAL_IP:3000 (from local network)"
        if [ -n "$PUBLIC_IP" ] && [ "$PUBLIC_IP" != "$LOCAL_IP" ]; then
            echo "   http://$PUBLIC_IP:3000 (from internet, if firewall allows)"
        fi
    fi
    
    echo ""
    print_warning "Security Note: External access exposes your system to the network."
    print_warning "Ensure you have proper authentication and firewall rules in place."
}

# Run main function
main
