<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .user-table th, .user-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .user-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .step.completed .step-number {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 用户管理功能测试</h1>
        <div class="highlight">
            <strong>新增功能：</strong>
            <ul>
                <li>✅ 完整的用户CRUD操作</li>
                <li>✅ 用户状态切换（启用/禁用）</li>
                <li>✅ 角色和技能组管理</li>
                <li>✅ 三个管理标签页：用户、角色、技能组</li>
                <li>✅ 权限控制和安全验证</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>测试流程</h3>
            <div class="step" id="step-1">
                <div class="step-number">1</div>
                <div>管理员登录</div>
            </div>
            <div class="step" id="step-2">
                <div class="step-number">2</div>
                <div>获取角色和技能组数据</div>
            </div>
            <div class="step" id="step-3">
                <div class="step-number">3</div>
                <div>获取用户列表</div>
            </div>
            <div class="step" id="step-4">
                <div class="step-number">4</div>
                <div>创建新用户</div>
            </div>
            <div class="step" id="step-5">
                <div class="step-number">5</div>
                <div>更新用户状态</div>
            </div>
            <div class="step" id="step-6">
                <div class="step-number">6</div>
                <div>更新用户角色和技能</div>
            </div>
        </div>

        <div class="test-section">
            <h3>1. 管理员登录</h3>
            <button onclick="testLogin()">登录</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取基础数据</h3>
            <button onclick="testGetRoles()">获取角色列表</button>
            <button onclick="testGetSkillGroups()">获取技能组列表</button>
            <div id="roles-result"></div>
            <div id="skills-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 获取用户列表</h3>
            <button onclick="testGetUsers()">获取用户列表</button>
            <div id="users-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 创建新用户</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="new-username" />
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="new-password" value="test123456" />
            </div>
            <div class="form-group">
                <label>姓名:</label>
                <input type="text" id="new-fullname" value="测试用户" />
            </div>
            <div class="form-group">
                <label>角色ID (逗号分隔):</label>
                <input type="text" id="new-roles" value="4" placeholder="例如: 4,5" />
            </div>
            <div class="form-group">
                <label>技能组ID (逗号分隔):</label>
                <input type="text" id="new-skills" value="1,2" placeholder="例如: 1,2,3" />
            </div>
            <button onclick="testCreateUser()">创建用户</button>
            <div id="create-user-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 用户状态管理</h3>
            <div class="form-group">
                <label>用户ID:</label>
                <input type="number" id="status-user-id" placeholder="输入要修改状态的用户ID" />
            </div>
            <button onclick="testToggleUserStatus(true)">启用用户</button>
            <button onclick="testToggleUserStatus(false)" class="danger">禁用用户</button>
            <div id="status-result"></div>
        </div>

        <div class="test-section">
            <h3>6. 更新用户角色和技能</h3>
            <div class="form-group">
                <label>用户ID:</label>
                <input type="number" id="update-user-id" placeholder="输入要更新的用户ID" />
            </div>
            <div class="form-group">
                <label>新角色ID (逗号分隔):</label>
                <input type="text" id="update-roles" value="2,4" placeholder="例如: 2,4" />
            </div>
            <div class="form-group">
                <label>新技能组ID (逗号分隔):</label>
                <input type="text" id="update-skills" value="1,3,5" placeholder="例如: 1,3,5" />
            </div>
            <button onclick="testUpdateUserRoles()">更新角色</button>
            <button onclick="testUpdateUserSkills()">更新技能</button>
            <div id="update-result"></div>
        </div>

        <div class="test-section">
            <h3>7. 自动化测试</h3>
            <button onclick="runFullTest()" class="success">运行完整测试</button>
            <div id="full-test-result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let roles = [];
        let skillGroups = [];
        let users = [];
        let testUserId = null;
        const API_BASE = 'http://localhost:8080/api';

        // 生成唯一的用户名
        function generateUsername() {
            const timestamp = Date.now();
            return `testuser_${timestamp}`;
        }

        // 设置用户名
        document.getElementById('new-username').value = generateUsername();

        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.success;
            
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <div><strong>状态:</strong> ${isSuccess ? '✅ 成功' : '❌ 失败'}</div>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        function markStepCompleted(stepId) {
            const step = document.getElementById(stepId);
            if (step) {
                step.classList.add('completed');
            }
        }

        async function testLogin() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
                markStepCompleted('step-1');
            }

            displayResult('login-result', result);
        }

        async function testGetRoles() {
            if (!authToken) {
                displayResult('roles-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/auth/roles');
            
            if (result.success && result.data.roles) {
                roles = result.data.roles;
                markStepCompleted('step-2');
            }
            
            displayResult('roles-result', result);
        }

        async function testGetSkillGroups() {
            if (!authToken) {
                displayResult('skills-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/auth/skill-groups');
            
            if (result.success && result.data.skill_groups) {
                skillGroups = result.data.skill_groups;
            }
            
            displayResult('skills-result', result);
        }

        async function testGetUsers() {
            if (!authToken) {
                displayResult('users-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/users');
            
            if (result.success && result.data.users) {
                users = result.data.users;
                markStepCompleted('step-3');
            }
            
            displayResult('users-result', result);
        }

        async function testCreateUser() {
            if (!authToken) {
                displayResult('create-user-result', { success: false, error: '请先登录' });
                return;
            }

            const username = document.getElementById('new-username').value;
            const password = document.getElementById('new-password').value;
            const fullName = document.getElementById('new-fullname').value;
            const roleIds = document.getElementById('new-roles').value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            const skillIds = document.getElementById('new-skills').value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

            const userData = {
                username: username,
                password: password,
                full_name: fullName,
                role_ids: roleIds,
                skill_group_ids: skillIds
            };

            const result = await apiCall('/auth/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });

            if (result.success && result.data.user) {
                testUserId = result.data.user.id;
                markStepCompleted('step-4');
                
                // 自动填充用户ID到其他测试表单
                document.getElementById('status-user-id').value = testUserId;
                document.getElementById('update-user-id').value = testUserId;
            }

            displayResult('create-user-result', result);
        }

        async function testToggleUserStatus(isActive) {
            if (!authToken) {
                displayResult('status-result', { success: false, error: '请先登录' });
                return;
            }

            const userId = document.getElementById('status-user-id').value;
            if (!userId) {
                displayResult('status-result', { success: false, error: '请输入用户ID' });
                return;
            }

            const result = await apiCall(`/users/${userId}/status`, {
                method: 'POST',
                body: JSON.stringify({ is_active: isActive })
            });

            if (result.success) {
                markStepCompleted('step-5');
            }

            displayResult('status-result', result);
        }

        async function testUpdateUserRoles() {
            if (!authToken) {
                displayResult('update-result', { success: false, error: '请先登录' });
                return;
            }

            const userId = document.getElementById('update-user-id').value;
            const roleIds = document.getElementById('update-roles').value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

            if (!userId) {
                displayResult('update-result', { success: false, error: '请输入用户ID' });
                return;
            }

            const result = await apiCall(`/users/${userId}/roles`, {
                method: 'POST',
                body: JSON.stringify({ role_ids: roleIds })
            });

            if (result.success) {
                markStepCompleted('step-6');
            }

            displayResult('update-result', result);
        }

        async function testUpdateUserSkills() {
            if (!authToken) {
                displayResult('update-result', { success: false, error: '请先登录' });
                return;
            }

            const userId = document.getElementById('update-user-id').value;
            const skillIds = document.getElementById('update-skills').value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

            if (!userId) {
                displayResult('update-result', { success: false, error: '请输入用户ID' });
                return;
            }

            const result = await apiCall(`/users/${userId}/skills`, {
                method: 'POST',
                body: JSON.stringify({ skill_group_ids: skillIds })
            });

            displayResult('update-result', result);
        }

        async function runFullTest() {
            document.getElementById('full-test-result').innerHTML = '<div class="info">正在运行完整测试...</div>';
            
            try {
                await testLogin();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testGetRoles();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testGetSkillGroups();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testGetUsers();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testCreateUser();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (testUserId) {
                    await testToggleUserStatus(false);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    await testToggleUserStatus(true);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    await testUpdateUserRoles();
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    await testUpdateUserSkills();
                }
                
                document.getElementById('full-test-result').innerHTML = '<div class="success">✅ 完整测试完成！请查看各步骤结果。</div>';
            } catch (error) {
                document.getElementById('full-test-result').innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('用户管理功能测试页面已加载');
            console.log('可以手动执行各步骤，或点击"运行完整测试"自动执行所有步骤');
        };
    </script>
</body>
</html>
