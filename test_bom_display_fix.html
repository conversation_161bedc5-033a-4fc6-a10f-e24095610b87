<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM显示修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .bom-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .bom-table th, .bom-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .bom-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .step.completed .step-number {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 BOM显示修复验证</h1>
        <div class="highlight">
            <strong>修复内容：</strong>
            <ul>
                <li>✅ 修复前端API客户端缺少getProjectBom方法</li>
                <li>✅ 修复BOM页面使用错误的API调用</li>
                <li>✅ 更新ProjectBom类型定义</li>
                <li>✅ 验证零件创建后在BOM中正确显示</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>测试流程</h3>
            <div class="step" id="step-1">
                <div class="step-number">1</div>
                <div>登录系统</div>
            </div>
            <div class="step" id="step-2">
                <div class="step-number">2</div>
                <div>获取项目列表</div>
            </div>
            <div class="step" id="step-3">
                <div class="step-number">3</div>
                <div>查看现有BOM</div>
            </div>
            <div class="step" id="step-4">
                <div class="step-number">4</div>
                <div>创建新零件并添加到BOM</div>
            </div>
            <div class="step" id="step-5">
                <div class="step-number">5</div>
                <div>验证BOM更新</div>
            </div>
        </div>

        <div class="test-section">
            <h3>1. 系统登录</h3>
            <button onclick="testLogin()">登录</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取项目列表</h3>
            <button onclick="testGetProjects()">获取项目列表</button>
            <div id="projects-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 查看现有BOM</h3>
            <div class="form-group">
                <label>选择项目:</label>
                <select id="project-select">
                    <option value="">请先获取项目列表</option>
                </select>
            </div>
            <button onclick="testGetBOM()">查看BOM</button>
            <div id="bom-before-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 创建零件并添加到BOM</h3>
            <div class="form-group">
                <label>零件编号:</label>
                <input type="text" id="new-part-number" />
            </div>
            <div class="form-group">
                <label>零件名称:</label>
                <input type="text" id="new-part-name" value="BOM测试零件" />
            </div>
            <div class="form-group">
                <label>数量:</label>
                <input type="number" id="new-part-quantity" value="3" min="1" />
            </div>
            <button onclick="testCreatePartAndAddToBOM()">创建零件并添加到BOM</button>
            <div id="create-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 验证BOM更新</h3>
            <button onclick="testVerifyBOMUpdate()">重新查看BOM</button>
            <div id="bom-after-result"></div>
        </div>

        <div class="test-section">
            <h3>6. 自动化测试</h3>
            <button onclick="runFullTest()" class="success">运行完整测试</button>
            <div id="full-test-result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let projects = [];
        let selectedProjectId = null;
        let bomBefore = [];
        let bomAfter = [];
        const API_BASE = 'http://localhost:8080/api';

        // 生成唯一的零件编号
        function generatePartNumber() {
            const timestamp = Date.now();
            return `BOM-TEST-${timestamp}`;
        }

        // 设置零件编号
        document.getElementById('new-part-number').value = generatePartNumber();

        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.success;
            
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <div><strong>状态:</strong> ${isSuccess ? '✅ 成功' : '❌ 失败'}</div>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        function markStepCompleted(stepId) {
            const step = document.getElementById(stepId);
            if (step) {
                step.classList.add('completed');
            }
        }

        async function testLogin() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
                markStepCompleted('step-1');
            }

            displayResult('login-result', result);
        }

        async function testGetProjects() {
            if (!authToken) {
                displayResult('projects-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/projects');
            
            if (result.success && result.data.projects) {
                projects = result.data.projects;
                markStepCompleted('step-2');
                
                // 更新项目选择下拉框
                const select = document.getElementById('project-select');
                select.innerHTML = '<option value="">请选择项目</option>';
                
                projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = `${project.project_name}${project.customer_name ? ` (${project.customer_name})` : ''}`;
                    select.appendChild(option);
                });

                // 自动选择第一个项目
                if (projects.length > 0) {
                    select.value = projects[0].id;
                    selectedProjectId = projects[0].id;
                }
            }
            
            displayResult('projects-result', result);
        }

        async function testGetBOM() {
            if (!authToken) {
                displayResult('bom-before-result', { success: false, error: '请先登录' });
                return;
            }

            const projectId = document.getElementById('project-select').value;
            if (!projectId) {
                displayResult('bom-before-result', { success: false, error: '请选择项目' });
                return;
            }

            selectedProjectId = projectId;
            const result = await apiCall(`/projects/${projectId}/bom`);
            
            if (result.success && result.data.bom_items) {
                bomBefore = result.data.bom_items;
                markStepCompleted('step-3');
                
                const formattedResult = {
                    ...result,
                    summary: `当前BOM包含 ${bomBefore.length} 个零件`,
                    bom_table: bomBefore
                };
                displayResult('bom-before-result', formattedResult);
            } else {
                displayResult('bom-before-result', result);
            }
        }

        async function testCreatePartAndAddToBOM() {
            if (!authToken || !selectedProjectId) {
                displayResult('create-result', { success: false, error: '请先登录并选择项目' });
                return;
            }

            const partNumber = document.getElementById('new-part-number').value;
            const partName = document.getElementById('new-part-name').value;
            const quantity = parseInt(document.getElementById('new-part-quantity').value);

            // 1. 创建零件
            const partData = {
                part_number: partNumber,
                part_name: partName,
                version: 'v1.0',
                specifications: 'BOM测试零件规格说明'
            };

            const partResult = await apiCall('/parts', {
                method: 'POST',
                body: JSON.stringify(partData)
            });

            if (!partResult.success) {
                displayResult('create-result', partResult);
                return;
            }

            // 2. 添加到BOM
            const bomData = {
                part_id: partResult.data.part.id,
                quantity: quantity
            };

            const bomResult = await apiCall(`/projects/${selectedProjectId}/bom`, {
                method: 'POST',
                body: JSON.stringify(bomData)
            });

            const combinedResult = {
                success: partResult.success && bomResult.success,
                part_creation: partResult,
                bom_addition: bomResult,
                summary: bomResult.success ? '零件创建并成功添加到BOM' : '零件创建成功但BOM添加失败'
            };

            if (combinedResult.success) {
                markStepCompleted('step-4');
            }

            displayResult('create-result', combinedResult);
        }

        async function testVerifyBOMUpdate() {
            if (!authToken || !selectedProjectId) {
                displayResult('bom-after-result', { success: false, error: '请先完成前面的步骤' });
                return;
            }

            const result = await apiCall(`/projects/${selectedProjectId}/bom`);
            
            if (result.success && result.data.bom_items) {
                bomAfter = result.data.bom_items;
                markStepCompleted('step-5');
                
                const comparison = {
                    ...result,
                    summary: `BOM更新验证: 之前${bomBefore.length}个零件，现在${bomAfter.length}个零件`,
                    new_items: bomAfter.filter(after => 
                        !bomBefore.some(before => before.part_id === after.part_id)
                    ),
                    bom_table: bomAfter
                };
                
                displayResult('bom-after-result', comparison);
            } else {
                displayResult('bom-after-result', result);
            }
        }

        async function runFullTest() {
            document.getElementById('full-test-result').innerHTML = '<div class="info">正在运行完整测试...</div>';
            
            try {
                await testLogin();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testGetProjects();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testGetBOM();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testCreatePartAndAddToBOM();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testVerifyBOMUpdate();
                
                document.getElementById('full-test-result').innerHTML = '<div class="success">✅ 完整测试完成！请查看各步骤结果。</div>';
            } catch (error) {
                document.getElementById('full-test-result').innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('BOM显示修复验证页面已加载');
            console.log('可以手动执行各步骤，或点击"运行完整测试"自动执行所有步骤');
        };
    </script>
</body>
</html>
