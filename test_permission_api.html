<!DOCTYPE html>
<html>
<head>
    <title>权限API测试</title>
</head>
<body>
    <h1>权限API测试</h1>
    <div id="results"></div>

    <script>
        const token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGVzIjpbImFkbWluIl0sInNraWxscyI6WyJDTkMgTWFjaGluaW5nIiwiTWlsbGluZyIsIlR1cm5pbmciXSwiZXhwIjoxNzUxOTg3Mjc1LCJpYXQiOjE3NTE5MDA4NzV9.RPySNGrR-jW9UQNgnI4Wb3iRYGYTgDhhSxEzTnESS_A";
        
        async function testAPIs() {
            const results = document.getElementById('results');
            
            try {
                // 测试角色API
                console.log('测试角色API...');
                const rolesResponse = await fetch('http://localhost:8080/api/auth/roles', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                const rolesData = await rolesResponse.json();
                console.log('角色数据:', rolesData);
                
                results.innerHTML += `<h2>角色API测试</h2>`;
                results.innerHTML += `<p>状态: ${rolesResponse.ok ? '成功' : '失败'}</p>`;
                results.innerHTML += `<p>数据类型: ${typeof rolesData}</p>`;
                results.innerHTML += `<p>是否有roles属性: ${rolesData.hasOwnProperty('roles')}</p>`;
                results.innerHTML += `<p>roles是否为数组: ${Array.isArray(rolesData.roles)}</p>`;
                results.innerHTML += `<p>角色数量: ${rolesData.roles ? rolesData.roles.length : 0}</p>`;
                results.innerHTML += `<pre>${JSON.stringify(rolesData, null, 2)}</pre>`;
                
                // 测试权限API
                console.log('测试权限API...');
                const permissionsResponse = await fetch('http://localhost:8080/api/permissions', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                const permissionsData = await permissionsResponse.json();
                console.log('权限数据:', permissionsData);
                
                results.innerHTML += `<h2>权限API测试</h2>`;
                results.innerHTML += `<p>状态: ${permissionsResponse.ok ? '成功' : '失败'}</p>`;
                results.innerHTML += `<p>数据类型: ${typeof permissionsData}</p>`;
                results.innerHTML += `<p>是否为数组: ${Array.isArray(permissionsData)}</p>`;
                results.innerHTML += `<p>权限数量: ${permissionsData.length || 0}</p>`;
                
                // 测试角色权限API
                if (rolesData.roles && rolesData.roles.length > 0) {
                    const firstRoleId = rolesData.roles[0].id;
                    console.log('测试角色权限API...');
                    const rolePermissionsResponse = await fetch(`http://localhost:8080/api/roles/${firstRoleId}/permissions`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                        },
                    });
                    const rolePermissionsData = await rolePermissionsResponse.json();
                    console.log('角色权限数据:', rolePermissionsData);
                    
                    results.innerHTML += `<h2>角色权限API测试</h2>`;
                    results.innerHTML += `<p>状态: ${rolePermissionsResponse.ok ? '成功' : '失败'}</p>`;
                    results.innerHTML += `<p>角色名称: ${rolePermissionsData.role_name}</p>`;
                    results.innerHTML += `<p>权限数量: ${rolePermissionsData.permissions ? rolePermissionsData.permissions.length : 0}</p>`;
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                results.innerHTML += `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }
        
        // 页面加载后执行测试
        window.onload = testAPIs;
    </script>
</body>
</html>
