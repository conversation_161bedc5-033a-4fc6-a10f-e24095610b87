# MES System API Summary

## Quick Stats
- **Total Endpoints**: 78 active endpoints
- **Base URL**: `http://localhost:8080/api`
- **Authentication**: JWT Bearer tokens
- **Framework**: Rust + Axum

## API Categories Overview

| Category | Endpoints | Description |
|----------|-----------|-------------|
| Authentication | 5 | Login, user creation, roles |
| User Management | 5 | User CRUD, status, roles, skills |
| Project Management | 8 | Projects, BOMs, work order creation |
| Parts Management | 6 | Part definitions and routing |
| Machine Management | 6 | Equipment management and status |
| Work Orders | 6 | Production orders and status |
| Production Planning | 9 | Task planning and scheduling |
| Execution Tracking | 9 | Shop floor execution and logging |
| Dashboard & Reporting | 11 | Analytics and KPI metrics |
| Quality Management | 8 | Inspections and quality control |
| Audit Logging | 7 | System audit and compliance |

## HTTP Methods Distribution

| Method | Count | Percentage |
|--------|-------|------------|
| GET | 46 | 59.0% |
| POST | 25 | 32.1% |
| PUT | 4 | 5.1% |
| DELETE | 3 | 3.8% |

## Key Endpoints

### Authentication
- `POST /auth/login` - User authentication
- `GET /auth/me` - Current user info
- `GET /auth/roles` - Available roles
- `GET /auth/skill-groups` - Skill groups

### Core Operations
- `GET /projects` - List all projects
- `POST /projects` - Create new project
- `GET /work-orders` - List work orders
- `POST /work-orders` - Create work order
- `GET /plan-tasks` - List production tasks
- `GET /execution/dashboard` - Shop floor dashboard

### Monitoring & Analytics
- `GET /dashboard/overview` - System overview
- `GET /dashboard/production-summary` - Production metrics
- `GET /dashboard/machine-utilization` - Equipment status
- `GET /quality/metrics` - Quality metrics

## Access Control

### Public Endpoints (5)
- Root and health check
- Login endpoint
- Roles and skill groups lookup

### Protected Endpoints (73)
- All operational endpoints require JWT authentication
- Role-based access control enforced

### Admin-Only Features
- User creation and management
- Audit log access and management
- System configuration

## Response Format
```json
{
  "data": {},
  "message": "Success message",
  "timestamp": "2025-06-28T10:45:00Z"
}
```

## Error Handling
- Standard HTTP status codes
- Structured error responses
- Validation error details

## Development Status
- ✅ **Active**: 78 endpoints fully functional
- ⚠️ **Disabled**: 9 routing endpoints (BigDecimal compatibility issues)
- 🔄 **Planned**: WebSocket support for real-time updates

## Testing
Use the provided `test_api.sh` script for endpoint testing.

## Documentation
See `API_DOCUMENTATION.md` for complete endpoint details and examples.
