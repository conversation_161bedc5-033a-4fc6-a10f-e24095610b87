<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零件管理改进测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.secondary {
            background-color: #6c757d;
        }
        button.secondary:hover {
            background-color: #545b62;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 零件管理改进测试</h1>
        <div class="highlight">
            <strong>改进内容：</strong>
            <ul>
                <li>✅ 零件创建时可选择关联项目</li>
                <li>✅ 自动添加到项目BOM</li>
                <li>✅ 改善用户体验</li>
                <li>✅ 保持数据库设计灵活性</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>1. 登录系统</h3>
            <button onclick="testLogin()">登录</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取项目列表</h3>
            <button onclick="testGetProjects()">获取项目列表</button>
            <div id="projects-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 创建零件（仅零件）</h3>
            <div class="form-group">
                <label>零件编号:</label>
                <input type="text" id="part-number-1" value="PART-001" />
            </div>
            <div class="form-group">
                <label>零件名称:</label>
                <input type="text" id="part-name-1" value="测试零件" />
            </div>
            <div class="form-group">
                <label>版本:</label>
                <input type="text" id="part-version-1" value="v1.0" />
            </div>
            <button onclick="testCreatePartOnly()">创建零件（不关联项目）</button>
            <div id="create-part-only-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 创建零件并添加到项目BOM</h3>
            <div class="form-group">
                <label>零件编号:</label>
                <input type="text" id="part-number-2" value="PART-002" />
            </div>
            <div class="form-group">
                <label>零件名称:</label>
                <input type="text" id="part-name-2" value="项目关联零件" />
            </div>
            <div class="form-group">
                <label>版本:</label>
                <input type="text" id="part-version-2" value="v1.0" />
            </div>
            <div class="form-group">
                <label>选择项目:</label>
                <select id="project-select">
                    <option value="">请先获取项目列表</option>
                </select>
            </div>
            <div class="form-group">
                <label>BOM数量:</label>
                <input type="number" id="bom-quantity" value="5" min="1" />
            </div>
            <button onclick="testCreatePartWithBOM()">创建零件并添加到BOM</button>
            <div id="create-part-bom-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 验证BOM创建</h3>
            <button onclick="testGetBOM()">查看项目BOM</button>
            <div id="bom-result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let projects = [];
        const API_BASE = 'http://localhost:8080/api';

        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.success;
            
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <div><strong>状态:</strong> ${isSuccess ? '✅ 成功' : '❌ 失败'}</div>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        async function testLogin() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function testGetProjects() {
            if (!authToken) {
                displayResult('projects-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/projects');
            
            if (result.success && result.data.projects) {
                projects = result.data.projects;
                
                // 更新项目选择下拉框
                const select = document.getElementById('project-select');
                select.innerHTML = '<option value="">请选择项目</option>';
                
                projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = `${project.project_name}${project.customer_name ? ` (${project.customer_name})` : ''}`;
                    select.appendChild(option);
                });
            }
            
            displayResult('projects-result', result);
        }

        async function testCreatePartOnly() {
            if (!authToken) {
                displayResult('create-part-only-result', { success: false, error: '请先登录' });
                return;
            }

            const partData = {
                part_number: document.getElementById('part-number-1').value,
                part_name: document.getElementById('part-name-1').value,
                version: document.getElementById('part-version-1').value,
                specifications: '测试零件规格说明'
            };

            const result = await apiCall('/parts', {
                method: 'POST',
                body: JSON.stringify(partData)
            });

            displayResult('create-part-only-result', result);
        }

        async function testCreatePartWithBOM() {
            if (!authToken) {
                displayResult('create-part-bom-result', { success: false, error: '请先登录' });
                return;
            }

            const projectId = document.getElementById('project-select').value;
            const quantity = parseInt(document.getElementById('bom-quantity').value);

            if (!projectId) {
                displayResult('create-part-bom-result', { success: false, error: '请选择项目' });
                return;
            }

            // 1. 先创建零件
            const partData = {
                part_number: document.getElementById('part-number-2').value,
                part_name: document.getElementById('part-name-2').value,
                version: document.getElementById('part-version-2').value,
                specifications: '项目关联零件规格说明'
            };

            const partResult = await apiCall('/parts', {
                method: 'POST',
                body: JSON.stringify(partData)
            });

            if (!partResult.success) {
                displayResult('create-part-bom-result', partResult);
                return;
            }

            // 2. 然后添加到项目BOM
            const bomData = {
                part_id: partResult.data.id,
                quantity: quantity
            };

            const bomResult = await apiCall(`/projects/${projectId}/bom`, {
                method: 'POST',
                body: JSON.stringify(bomData)
            });

            const combinedResult = {
                success: partResult.success && bomResult.success,
                partCreation: partResult,
                bomCreation: bomResult
            };

            displayResult('create-part-bom-result', combinedResult);
        }

        async function testGetBOM() {
            if (!authToken) {
                displayResult('bom-result', { success: false, error: '请先登录' });
                return;
            }

            const projectId = document.getElementById('project-select').value;
            if (!projectId) {
                displayResult('bom-result', { success: false, error: '请选择项目' });
                return;
            }

            const result = await apiCall(`/projects/${projectId}/bom`);

            // 格式化BOM显示
            if (result.success && result.data.bom_items) {
                const bomItems = result.data.bom_items;
                const formattedResult = {
                    ...result,
                    summary: `找到 ${bomItems.length} 个BOM项目`,
                    items: bomItems.map(item => ({
                        零件编号: item.part_number,
                        零件名称: item.part_name || '未命名',
                        版本: item.version,
                        数量: item.quantity,
                        规格: item.specifications || '无'
                    }))
                };
                displayResult('bom-result', formattedResult);
            } else {
                displayResult('bom-result', result);
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('零件管理改进测试页面已加载');
            console.log('测试流程：1. 登录 -> 2. 获取项目 -> 3. 创建零件 -> 4. 验证BOM');
        };
    </script>
</body>
</html>
