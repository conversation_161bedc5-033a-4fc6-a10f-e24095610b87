# MES System Environment Configuration Example
# Copy this file to .env and modify the values as needed

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/mes_db

# Server Configuration - 0.0.0.0 allows external access
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_HOURS=24

# CORS Configuration - Add your domain for external access
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://0.0.0.0:3000,http://0.0.0.0:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Logging Configuration
RUST_LOG=info
LOG_LEVEL=info

# Application Configuration
APP_NAME=MES System
APP_VERSION=0.1.0
APP_ENVIRONMENT=development

# Security Configuration
BCRYPT_COST=12
SESSION_TIMEOUT_MINUTES=30

# File Upload Configuration
MAX_FILE_SIZE_MB=10
UPLOAD_DIR=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM_EMAIL=<EMAIL>

# Redis Configuration (for caching, if used)
REDIS_URL=redis://localhost:6379

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL_SECONDS=30

# External Access Configuration
# Replace with your actual domain/IP for production
# CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://your-ip-address:3000
# For development with external access, you can use:
# CORS_ALLOWED_ORIGINS=http://your-ip-address:3000,http://your-ip-address:8080

# Production Settings
# Set these in production environment
# DATABASE_URL=***************************************************/mes_production
# JWT_SECRET=very-long-random-string-for-production
# APP_ENVIRONMENT=production
# RUST_LOG=warn
# SERVER_HOST=0.0.0.0  # Keep this for external access
# CORS_ALLOWED_ORIGINS=https://yourdomain.com

# Development Settings
# Uncomment for development
# RUST_BACKTRACE=1
# SQLX_LOGGING=true
