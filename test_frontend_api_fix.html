<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端API修复测试</h1>
        <p>测试修复后的前端API客户端是否能正确处理后端响应格式</p>

        <div class="test-section">
            <h3>1. 登录测试</h3>
            <button onclick="testLogin()">测试登录</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取项目列表</h3>
            <button onclick="testGetProjects()">获取项目列表</button>
            <div id="projects-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 创建项目</h3>
            <button onclick="testCreateProject()">创建测试项目</button>
            <div id="create-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 获取用户列表</h3>
            <button onclick="testGetUsers()">获取用户列表</button>
            <div id="users-result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        const API_BASE = 'http://localhost:8080/api';

        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.success;
            
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <div><strong>状态:</strong> ${isSuccess ? '✅ 成功' : '❌ 失败'}</div>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        async function testLogin() {
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
            }

            displayResult('login-result', result);
        }

        async function testGetProjects() {
            if (!authToken) {
                displayResult('projects-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/projects');
            displayResult('projects-result', result);
        }

        async function testCreateProject() {
            if (!authToken) {
                displayResult('create-result', { success: false, error: '请先登录' });
                return;
            }

            const projectName = `测试项目_${Date.now()}`;
            const result = await apiCall('/projects', {
                method: 'POST',
                body: JSON.stringify({
                    project_name: projectName,
                    customer_name: '前端测试客户'
                })
            });

            displayResult('create-result', result);
        }

        async function testGetUsers() {
            if (!authToken) {
                displayResult('users-result', { success: false, error: '请先登录' });
                return;
            }

            const result = await apiCall('/users');
            displayResult('users-result', result);
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('前端API修复测试页面已加载');
            console.log('请按顺序执行测试：1. 登录 -> 2. 获取项目列表 -> 3. 创建项目 -> 4. 获取用户列表');
        };
    </script>
</body>
</html>
