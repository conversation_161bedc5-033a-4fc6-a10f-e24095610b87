#!/usr/bin/env python3
"""
API Statistics Generator for MES System
Analyzes the Rust source code to count and categorize API endpoints
"""

import re
import os
from collections import defaultdict

def extract_routes_from_main():
    """Extract route definitions from main.rs"""
    routes = []
    
    try:
        with open('src/main.rs', 'r') as f:
            content = f.read()
            
        # Find route definitions
        route_pattern = r'\.route\("([^"]+)",\s*(\w+)\(([^)]+)\)\)'
        matches = re.findall(route_pattern, content)
        
        for path, method, handler in matches:
            # Map Axum methods to HTTP methods
            method_map = {
                'get': 'GET',
                'post': 'POST', 
                'put': 'PUT',
                'delete': 'DELETE',
                'axum::routing::put': 'PUT',
                'axum::routing::delete': 'DELETE'
            }
            
            http_method = method_map.get(method, method.upper())
            routes.append((http_method, path, handler))
            
    except FileNotFoundError:
        print("Error: src/main.rs not found")
        return []
        
    return routes

def categorize_routes(routes):
    """Categorize routes by functionality"""
    categories = defaultdict(list)
    
    for method, path, handler in routes:
        if '/auth/' in path:
            categories['Authentication'].append((method, path))
        elif '/users' in path:
            categories['User Management'].append((method, path))
        elif '/projects' in path or '/bom' in path:
            categories['Project Management'].append((method, path))
        elif '/parts' in path:
            categories['Parts Management'].append((method, path))
        elif '/machines' in path:
            categories['Machine Management'].append((method, path))
        elif '/work-orders' in path:
            categories['Work Orders'].append((method, path))
        elif '/plan-tasks' in path or '/planning' in path:
            categories['Production Planning'].append((method, path))
        elif '/execution' in path:
            categories['Execution Tracking'].append((method, path))
        elif '/dashboard' in path or '/reports' in path:
            categories['Dashboard and Reporting'].append((method, path))
        elif '/quality' in path:
            categories['Quality Management'].append((method, path))
        elif '/audit' in path:
            categories['Audit Logging'].append((method, path))
        elif '/skill-groups' in path:
            categories['Skill Groups'].append((method, path))
        else:
            categories['Other'].append((method, path))
            
    return categories

def count_by_method(routes):
    """Count routes by HTTP method"""
    method_counts = defaultdict(int)
    
    for method, path, handler in routes:
        method_counts[method] += 1
        
    return method_counts

def analyze_access_control(routes):
    """Analyze access control patterns"""
    public_routes = []
    protected_routes = []
    
    # These are typically public routes
    public_patterns = ['/health', '/', '/api/auth/login', '/api/auth/roles', '/api/auth/skill-groups']
    
    for method, path, handler in routes:
        if any(pattern in path for pattern in public_patterns):
            public_routes.append((method, path))
        else:
            protected_routes.append((method, path))
            
    return public_routes, protected_routes

def main():
    print("🔍 Analyzing MES System API Endpoints...")
    print("=" * 50)
    
    routes = extract_routes_from_main()
    
    if not routes:
        print("No routes found!")
        return
        
    print(f"📊 Total API Endpoints Found: {len(routes)}")
    print()
    
    # Categorize routes
    categories = categorize_routes(routes)
    print("📂 Breakdown by Category:")
    total_categorized = 0
    for category, route_list in sorted(categories.items()):
        print(f"  • {category}: {len(route_list)} endpoints")
        total_categorized += len(route_list)
        
    print()
    
    # Count by HTTP method
    method_counts = count_by_method(routes)
    print("🔧 HTTP Methods Distribution:")
    total_methods = sum(method_counts.values())
    for method, count in sorted(method_counts.items()):
        percentage = (count / total_methods) * 100
        print(f"  • {method}: {count} endpoints ({percentage:.1f}%)")
        
    print()
    
    # Access control analysis
    public_routes, protected_routes = analyze_access_control(routes)
    print("🔒 Access Control:")
    print(f"  • Public Endpoints: {len(public_routes)}")
    print(f"  • Protected Endpoints: {len(protected_routes)}")
    
    print()
    print("📋 Detailed Route List:")
    print("-" * 50)
    
    for category, route_list in sorted(categories.items()):
        if route_list:
            print(f"\n{category}:")
            for method, path in sorted(route_list):
                print(f"  {method:6} {path}")
    
    print()
    print("✅ Analysis complete!")

if __name__ == "__main__":
    main()
