<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺管理 - 仅技能组测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .routing-item {
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .step-number {
            font-weight: bold;
            color: #007bff;
            font-size: 16px;
        }
        .process-name {
            font-weight: bold;
            color: #333;
            margin: 4px 0;
        }
        .work-instructions {
            color: #666;
            font-size: 14px;
            margin-top: 4px;
        }
        .standard-hours {
            color: #28a745;
            font-weight: bold;
            margin-top: 4px;
        }
        .skill-group-tag {
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }
        .data-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 工艺管理 - 仅技能组测试</h1>
        <p>此页面用于测试工艺管理功能是否已成功移除设备选择，只保留技能组选项。</p>
        
        <div class="form-group">
            <label for="partSelect">选择零件：</label>
            <select id="partSelect">
                <option value="">请选择零件</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="processName">工艺名称：</label>
            <select id="processName">
                <option value="">请选择技能组或手动输入</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="stepNumber">工序编号：</label>
            <input type="number" id="stepNumber" min="1" placeholder="输入工序编号">
        </div>
        
        <div class="form-group">
            <label for="workInstructions">工作指导：</label>
            <textarea id="workInstructions" rows="3" placeholder="输入工作指导书内容"></textarea>
        </div>
        
        <div class="form-group">
            <label for="standardHours">标准工时（小时）：</label>
            <input type="number" id="standardHours" step="0.1" min="0" placeholder="输入标准工时">
        </div>
        
        <button onclick="createRouting()">创建工艺路由</button>
        <button onclick="loadRoutings()">刷新工艺列表</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📊 数据概览</h2>
        <div class="data-section">
            <div>
                <h3>技能组列表</h3>
                <div id="skillGroupsList"></div>
            </div>
            <div>
                <h3>零件列表</h3>
                <div id="partsList"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 现有工艺路由</h2>
        <div id="routingsList"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = '';
        let parts = [];
        let skillGroups = [];
        let routings = [];

        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                authToken = data.token;
                return true;
            } catch (error) {
                console.error('登录失败:', error);
                return false;
            }
        }

        // 获取零件数据
        async function fetchParts() {
            try {
                const response = await fetch(`${API_BASE}/parts`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                parts = data.data.parts;
                return parts;
            } catch (error) {
                console.error('获取零件失败:', error);
                return [];
            }
        }

        // 获取技能组数据
        async function fetchSkillGroups() {
            try {
                const response = await fetch(`${API_BASE}/skill-groups`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                skillGroups = data;
                return skillGroups;
            } catch (error) {
                console.error('获取技能组失败:', error);
                return [];
            }
        }

        // 获取工艺路由数据
        async function fetchRoutings() {
            try {
                const response = await fetch(`${API_BASE}/routings`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                routings = data.routings;
                return routings;
            } catch (error) {
                console.error('获取工艺路由失败:', error);
                return [];
            }
        }

        // 创建工艺路由
        async function createRouting() {
            const partId = document.getElementById('partSelect').value;
            const processName = document.getElementById('processName').value;
            const stepNumber = document.getElementById('stepNumber').value;
            const workInstructions = document.getElementById('workInstructions').value;
            const standardHours = document.getElementById('standardHours').value;

            if (!partId || !processName || !stepNumber) {
                showResult('请填写必填字段：零件、工艺名称、工序编号', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/routings`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        part_id: parseInt(partId),
                        step_number: parseInt(stepNumber),
                        process_name: processName,
                        work_instructions: workInstructions || null,
                        standard_hours: standardHours ? parseFloat(standardHours) : null
                    })
                });

                if (response.ok) {
                    showResult('✅ 工艺路由创建成功！只使用了技能组，没有指定具体设备。', 'success');
                    clearForm();
                    loadRoutings();
                } else {
                    const error = await response.json();
                    showResult(`❌ 创建失败: ${error.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 创建失败: ${error.message}`, 'error');
            }
        }

        // 填充选择器
        function populateSelectors() {
            // 填充零件选择器
            const partSelect = document.getElementById('partSelect');
            partSelect.innerHTML = '<option value="">请选择零件</option>';
            parts.forEach(part => {
                const option = document.createElement('option');
                option.value = part.id;
                option.textContent = `${part.part_number} - ${part.part_name || '未命名'} (${part.version})`;
                partSelect.appendChild(option);
            });

            // 填充工艺名称选择器（只有技能组）
            const processSelect = document.getElementById('processName');
            processSelect.innerHTML = '<option value="">请选择技能组或手动输入</option>';
            skillGroups.forEach(skillGroup => {
                const option = document.createElement('option');
                option.value = skillGroup.group_name;
                option.textContent = skillGroup.group_name;
                processSelect.appendChild(option);
            });
        }

        // 显示数据概览
        function showDataOverview() {
            // 显示技能组
            const skillGroupsDiv = document.getElementById('skillGroupsList');
            skillGroupsDiv.innerHTML = skillGroups.map(sg => 
                `<div class="skill-group-tag">${sg.group_name}</div>`
            ).join('');

            // 显示零件
            const partsDiv = document.getElementById('partsList');
            partsDiv.innerHTML = parts.map(part => 
                `<div style="margin: 4px 0; padding: 4px; background: #f0f0f0; border-radius: 4px;">
                    <strong>${part.part_number}</strong> - ${part.part_name || '未命名'} (${part.version})
                </div>`
            ).join('');
        }

        // 加载并显示工艺路由
        async function loadRoutings() {
            await fetchRoutings();
            const routingsDiv = document.getElementById('routingsList');
            
            if (routings.length === 0) {
                routingsDiv.innerHTML = '<p>暂无工艺路由数据</p>';
                return;
            }

            // 按零件分组
            const routingsByPart = {};
            routings.forEach(routing => {
                const partKey = `${routing.part_number} (${routing.version})`;
                if (!routingsByPart[partKey]) {
                    routingsByPart[partKey] = [];
                }
                routingsByPart[partKey].push(routing);
            });

            let html = '';
            Object.entries(routingsByPart).forEach(([partKey, partRoutings]) => {
                html += `<h4>${partKey}</h4>`;
                partRoutings.sort((a, b) => a.step_number - b.step_number).forEach(routing => {
                    html += `
                        <div class="routing-item">
                            <div class="step-number">步骤 ${routing.step_number}</div>
                            <div class="process-name">${routing.process_name}</div>
                            ${routing.work_instructions ? `<div class="work-instructions">工作指导: ${routing.work_instructions}</div>` : ''}
                            ${routing.standard_hours ? `<div class="standard-hours">标准工时: ${routing.standard_hours}h</div>` : ''}
                        </div>
                    `;
                });
            });

            routingsDiv.innerHTML = html;
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        // 清空表单
        function clearForm() {
            document.getElementById('processName').value = '';
            document.getElementById('stepNumber').value = '';
            document.getElementById('workInstructions').value = '';
            document.getElementById('standardHours').value = '';
        }

        // 初始化
        async function init() {
            const loginSuccess = await login();
            if (!loginSuccess) {
                showResult('登录失败，请检查后端服务是否正常运行。', 'error');
                return;
            }

            await Promise.all([fetchParts(), fetchSkillGroups()]);
            populateSelectors();
            showDataOverview();
            await loadRoutings();
            
            showResult('✅ 数据加载完成。工艺管理现在只显示技能组选项，已移除设备选择。', 'success');
        }

        init();
    </script>
</body>
</html>
