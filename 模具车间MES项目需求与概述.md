模具车间制造执行系统 (MES) - 项目需求与概述版本: 1.0日期: 2025年6月10日1. 项目概述与目标1.1 项目说明本项目旨在为机械加工与模具制造车间，开发一套轻量级、高效率的制造执行系统（MES）。系统将以车间内部的工艺流程为起点，实现从任务创建、工艺规划、生产调度、车间执行到质量追溯的全流程数字化管理。系统将采用现代化的技术栈（Rust + PostgreSQL），支持PC和移动端访问，并通过精细化的权限管理，确保数据安全和操作便捷。1.2 核心目标解决生产进度不透明问题： 实时追踪每一个零件、每一道工序的当前状态、位置和负责人。解决产品质量无法溯源问题： 建立从成品到工序、人员、设备、甚至原材料批次的完整追溯链。提升生产协同效率： 通过标准化的工艺流程和明确的作业指导，减少口头沟通成本和出错率。实现柔性生产调度： 支持将任务分配到“设备组”而非具体设备，提高设备利用率和应对突发状况的能力。提供数据决策支持： 通过数据看板，为管理者提供真实、及时的生产KPI，支持持续改进。1.3 主要用户角色管理员 (Admin): 系统的最高管理者，负责用户、角色、技能组等基础数据的维护。工艺员 (Process Engineer): 负责创建生产任务、维护零件库及版本、制定和下发工艺路线及作业指导。计划员 (Planner): 负责将待生产的工序任务，通过甘特图等工具，合理地安排到设备组的生产计划中。车间工人 (Operator): 生产任务的执行者，根据自身技能和生产计划，在车间终端（手机/PC）上进行扫码报工。质检员 (Quality Inspector): 负责对完成的工序或成品进行质量检验，并录入检验数据。2. 核心功能需求项目与BOM管理： 支持工艺员创建模具项目，并为其定义包含版本、数量的零件物料清单（BOM）。零件与工艺管理： 支持工艺员维护带版本的零件库，并为零件设计包含详细“作业指导”的标准工艺路线。生产计划与调度： 提供可视化的甘特图界面，支持计划员将工序任务拖拽至设备组（技能组）进行排程。车间执行与报工： 支持工人查看个人任务列表，通过扫码进行任务的开始、暂停、继续、完成等操作。权限与技能管理： 实现基于“角色+技能”的精细化权限控制，确保用户只能访问和操作其职责与能力范围内的功能。追溯与查询： 提供强大的查询功能，可根据零件码追溯其完整的生产历史。数据看板： 可视化展示订单进度、设备状态、合格率等关键KPI。审计日志： 对关键数据的创建、修改、删除操作进行记录，以备追溯。