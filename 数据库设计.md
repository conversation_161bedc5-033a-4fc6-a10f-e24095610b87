模具车间制造执行系统 (MES) - 数据库设计版本: 2.3.1 (最终版)数据库类型: PostgreSQL概述本数据库方案设计了14个核心表，以支持MES系统的全部功能，包括用户权限、项目工艺、生产计划、车间执行和审计日志。数据表结构详情表名 (Table Name)描述users用户表roles角色表user_roles用户角色关联表skill_groups技能组/设备类型表user_skills用户技能关联表machines设备表projects项目/模具表 (系统顶层实体)parts零件主数据/物料库 (含版本)project_boms项目物料清单work_orders工单表routings工艺路线表 (含作业指导)plan_tasks生产计划表 (计划员的产出)execution_logs执行记录表 (工人的产出)audit_logs审计日志表users (用户表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 用户ID || username | VARCHAR(100) | No | 用户登录名 (唯一) || password_hash | VARCHAR(255) | No | 加密后的密码 || full_name | VARCHAR(100) | Yes | 员工真实姓名 || is_active | BOOLEAN | No | 账号是否激活 || created_at | TIMESTAMPTZ | No | 创建时间 |roles (角色表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 角色ID || role_name | VARCHAR(50) | No | 角色名 (唯一) |user_roles (用户角色关联表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || user_id | INTEGER | No | 关联 users.id || role_id | INTEGER | No | 关联 roles.id |skill_groups (技能组/设备类型表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 技能组ID || group_name | VARCHAR(100) | No | 组名 (唯一) |user_skills (用户技能关联表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || user_id | INTEGER | No | 关联 users.id || skill_group_id| INTEGER | No | 关联 skill_groups.id |machines (设备表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 设备ID || machine_name | VARCHAR(255) | No | 设备名称/编号 || skill_group_id| INTEGER | No | 关联 skill_groups.id || status | VARCHAR(50) | No | 设备当前状态 |projects (项目/模具表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 项目ID || project_name | VARCHAR(255) | No | 模具编号或项目名 || customer_name | VARCHAR(255) | Yes | 客户名称 || created_at | TIMESTAMPTZ | No | 创建时间 |parts (零件主数据/物料库)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 零件ID || part_number | VARCHAR(255) | No | 零件编号 || part_name | VARCHAR(255) | Yes | 零件名称 || version | VARCHAR(50) | No | 版本号 || specifications| TEXT | Yes | 规格描述 |project_boms (项目物料清单)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | BOM行ID || project_id | INTEGER | No | 关联 projects.id || part_id | INTEGER | No | 关联 parts.id || quantity | INTEGER | No | 所需数量 |work_orders (工单表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 工单ID || project_bom_id| INTEGER | No | 关联 project_boms.id || quantity | INTEGER | No | 生产数量 || status | VARCHAR(50) | No | 工单状态 || due_date | DATE | Yes | 期望交期 || created_at | TIMESTAMPTZ | No | 创建时间 |routings (工艺路线表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 工艺路线ID || part_id | INTEGER | No | 关联parts.id (关联零件主数据) || step_number | INTEGER | No | 工序顺序号 || process_name | VARCHAR(255)| No | 工序名称 || work_instructions| TEXT| Yes | 作业指导内容 || standard_hours| DECIMAL(10,2)| Yes | 标准工时 |plan_tasks (生产计划表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 计划任务ID || work_order_id|INTEGER| No | 关联work_orders.id || routing_step_id|INTEGER| No | 关联routings.id中的特定步骤 || skill_group_id| INTEGER | No | 关联skill_groups.id || planned_start | TIMESTAMPTZ | No | 计划开始时间 || planned_end | TIMESTAMPTZ | No | 计划结束时间 || status | VARCHAR(50) | No | 计划状态 |execution_logs (执行记录表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY | No | 记录ID || plan_task_id | INTEGER | No | 关联plan_tasks.id || machine_id | INTEGER | Yes | 实际使用的设备ID || user_id | INTEGER | No | 关联users.id (操作员) || event_type | VARCHAR(50) | No | 事件类型 (START/STOP...) || event_time | TIMESTAMPTZ | No | 事件时间 || notes | TEXT | Yes | 备注 |audit_logs (审计日志表)| 字段名 | 数据类型 | 是否可为空 | 解释 || :--- | :--- | :--- | :--- || id | SERIAL PRIMARY KEY| No | 日志ID || user_id | INTEGER | No | 关联users.id (操作人) || action_type | VARCHAR(50) | No | 操作类型 (CREATE/UPDATE...) || target_entity| VARCHAR(100)| No | 操作对象 (e.g., 'parts') || target_id | INTEGER | No | 操作对象的ID || change_details| JSONB | Yes | 变更详情(旧值/新值) || action_time | TIMESTAMPTZ | No | 操作时间 |