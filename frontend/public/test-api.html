<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .result {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #52c41a; }
        .error { border-left: 4px solid #f5222d; }
        .warning { border-left: 4px solid #faad14; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #f6ffed; color: #52c41a; }
        .status.error { background: #fff2f0; color: #f5222d; }
        .status.loading { background: #e6f7ff; color: #1890ff; }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .data-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            text-align: center;
        }
        .data-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .data-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 MES系统API测试页面</h1>
        <p>这个页面可以直接测试前端API连接，无需React环境</p>
        
        <div class="test-section">
            <h3>🔗 连接状态</h3>
            <p><strong>当前地址:</strong> <span id="current-url"></span></p>
            <p><strong>API地址:</strong> <span id="api-url"></span></p>
            <p><strong>认证状态:</strong> <span id="auth-status" class="status">未认证</span></p>
            <button onclick="login()">登录</button>
            <button onclick="testAllAPIs()" id="test-all-btn" disabled>测试所有API</button>
        </div>

        <div class="test-section">
            <h3>📊 仪表板数据</h3>
            <button onclick="testDashboard()">获取仪表板数据</button>
            <div id="dashboard-data" class="data-grid" style="display: none;">
                <div class="data-card">
                    <h4>设备利用率</h4>
                    <div class="value" id="utilization-rate">-</div>
                </div>
                <div class="data-card">
                    <h4>质量合格率</h4>
                    <div class="value" id="quality-rate">-</div>
                </div>
                <div class="data-card">
                    <h4>总工单数</h4>
                    <div class="value" id="total-orders">-</div>
                </div>
                <div class="data-card">
                    <h4>进行中工单</h4>
                    <div class="value" id="progress-orders">-</div>
                </div>
            </div>
            <div id="dashboard-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 其他数据</h3>
            <button onclick="testProjects()">测试项目数据</button>
            <button onclick="testParts()">测试零件数据</button>
            <button onclick="testMachines()">测试设备数据</button>
            <button onclick="testWorkOrders()">测试工单数据</button>
            <div id="other-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 问题诊断</h3>
            <button onclick="diagnose()">运行诊断</button>
            <div id="diagnosis-result" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        
        // 更新页面信息
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('api-url').textContent = window.location.origin + '/api';

        // 登录函数
        async function login() {
            const statusEl = document.getElementById('auth-status');
            statusEl.textContent = '登录中...';
            statusEl.className = 'status loading';

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    statusEl.textContent = '已登录';
                    statusEl.className = 'status success';
                    document.getElementById('test-all-btn').disabled = false;
                    console.log('登录成功:', data);
                } else {
                    const errorText = await response.text();
                    statusEl.textContent = '登录失败';
                    statusEl.className = 'status error';
                    console.error('登录失败:', errorText);
                }
            } catch (error) {
                statusEl.textContent = '登录错误';
                statusEl.className = 'status error';
                console.error('登录错误:', error);
            }
        }

        // 测试仪表板
        async function testDashboard() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const resultEl = document.getElementById('dashboard-result');
            resultEl.textContent = '正在获取仪表板数据...';
            resultEl.className = 'result';

            try {
                const response = await fetch('/api/dashboard/overview', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新数据卡片
                    document.getElementById('utilization-rate').textContent = 
                        (data.machine_status?.utilization_rate || 0) + '%';
                    document.getElementById('quality-rate').textContent = 
                        (data.quality_metrics?.quality_rate || 0) + '%';
                    document.getElementById('total-orders').textContent = 
                        data.production_summary?.total_work_orders || 0;
                    document.getElementById('progress-orders').textContent = 
                        data.work_order_status?.in_progress_orders || 0;
                    
                    document.getElementById('dashboard-data').style.display = 'grid';
                    
                    resultEl.textContent = '✅ 仪表板数据获取成功\n' + JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultEl.textContent = `❌ 仪表板数据获取失败: ${response.status}\n${errorText}`;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = `❌ 仪表板数据获取错误: ${error.message}`;
                resultEl.className = 'result error';
            }
        }

        // 测试项目数据
        async function testProjects() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const resultEl = document.getElementById('other-result');
            resultEl.textContent = '正在获取项目数据...';

            try {
                const response = await fetch('/api/projects', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultEl.textContent = `✅ 项目数据获取成功 (${data.projects?.length || 0} 个项目)\n` + 
                                         JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultEl.textContent = `❌ 项目数据获取失败: ${response.status}\n${errorText}`;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = `❌ 项目数据获取错误: ${error.message}`;
                resultEl.className = 'result error';
            }
        }

        // 测试零件数据
        async function testParts() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const resultEl = document.getElementById('other-result');
            resultEl.textContent = '正在获取零件数据...';

            try {
                const response = await fetch('/api/parts', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultEl.textContent = `✅ 零件数据获取成功 (${data.parts?.length || 0} 个零件)\n` + 
                                         JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultEl.textContent = `❌ 零件数据获取失败: ${response.status}\n${errorText}`;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = `❌ 零件数据获取错误: ${error.message}`;
                resultEl.className = 'result error';
            }
        }

        // 测试设备数据
        async function testMachines() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const resultEl = document.getElementById('other-result');
            resultEl.textContent = '正在获取设备数据...';

            try {
                const response = await fetch('/api/machines', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultEl.textContent = `✅ 设备数据获取成功 (${data.machines?.length || 0} 台设备)\n` + 
                                         JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultEl.textContent = `❌ 设备数据获取失败: ${response.status}\n${errorText}`;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = `❌ 设备数据获取错误: ${error.message}`;
                resultEl.className = 'result error';
            }
        }

        // 测试工单数据
        async function testWorkOrders() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const resultEl = document.getElementById('other-result');
            resultEl.textContent = '正在获取工单数据...';

            try {
                const response = await fetch('/api/work-orders', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultEl.textContent = `✅ 工单数据获取成功 (${data.work_orders?.length || 0} 个工单)\n` + 
                                         JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultEl.textContent = `❌ 工单数据获取失败: ${response.status}\n${errorText}`;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = `❌ 工单数据获取错误: ${error.message}`;
                resultEl.className = 'result error';
            }
        }

        // 测试所有API
        async function testAllAPIs() {
            await testDashboard();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testProjects();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testParts();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testMachines();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testWorkOrders();
        }

        // 运行诊断
        async function diagnose() {
            const resultEl = document.getElementById('diagnosis-result');
            let diagnosis = '🔍 系统诊断报告\n\n';

            // 检查前端服务
            try {
                const frontendResponse = await fetch('/');
                diagnosis += `✅ 前端服务: 正常 (状态码: ${frontendResponse.status})\n`;
            } catch (error) {
                diagnosis += `❌ 前端服务: 异常 (${error.message})\n`;
            }

            // 检查后端健康检查
            try {
                const healthResponse = await fetch('/api/health');
                diagnosis += `✅ 后端健康检查: 正常 (状态码: ${healthResponse.status})\n`;
            } catch (error) {
                diagnosis += `❌ 后端健康检查: 异常 (${error.message})\n`;
            }

            // 检查认证状态
            if (authToken) {
                diagnosis += `✅ 认证状态: 已登录\n`;
                diagnosis += `Token: ${authToken.substring(0, 50)}...\n`;
            } else {
                diagnosis += `⚠️ 认证状态: 未登录\n`;
            }

            // 检查浏览器环境
            diagnosis += `\n📱 浏览器环境:\n`;
            diagnosis += `User Agent: ${navigator.userAgent}\n`;
            diagnosis += `当前URL: ${window.location.href}\n`;
            diagnosis += `协议: ${window.location.protocol}\n`;
            diagnosis += `主机: ${window.location.host}\n`;

            resultEl.textContent = diagnosis;
            resultEl.className = 'result';
        }

        // 页面加载时自动运行诊断
        window.onload = function() {
            diagnose();
        };
    </script>
</body>
</html>
