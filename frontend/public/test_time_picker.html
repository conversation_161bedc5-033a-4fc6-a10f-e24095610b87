<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .check {
            color: #52c41a;
            margin-right: 10px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 10px 10px 0;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #40a9ff;
        }
        .instructions {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 3px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 生产计划时间选择器优化测试</h1>
        
        <div class="instructions">
            <strong>📋 测试说明：</strong>
            <p>我们已经优化了生产计划中的时间选择功能，现在分钟选项只显示 00 和 30，大大简化了操作流程。</p>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 已完成的优化</div>
            <ul class="feature-list">
                <li><span class="check">✓</span>分钟选择限制为 00 和 30 两个选项</li>
                <li><span class="check">✓</span>移除了 1-29 和 31-59 分钟选项</li>
                <li><span class="check">✓</span>设置了 30 分钟最小时间间隔</li>
                <li><span class="check">✓</span>前端和后端双重验证</li>
                <li><span class="check">✓</span>优化了用户界面体验</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试步骤</div>
            <ol>
                <li>点击下方按钮进入 MES 系统</li>
                <li>登录系统（用户名：admin，密码：admin123）</li>
                <li>导航到"生产计划"页面</li>
                <li>点击"创建计划任务"按钮</li>
                <li>测试时间选择器：
                    <ul>
                        <li>验证分钟下拉框只显示 00 和 30</li>
                        <li>尝试设置少于 30 分钟的时间间隔</li>
                        <li>确认系统显示错误提示</li>
                    </ul>
                </li>
                <li>测试重新安排任务功能</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">⚙️ 技术实现</div>
            <div class="code">
showTime={{
  minuteStep: 30,
  hideDisabledOptions: true,
  format: 'HH:mm',
}}
            </div>
            <p>使用 Ant Design DatePicker 的 <code>minuteStep</code> 属性设置 30 分钟步长，<code>hideDisabledOptions</code> 隐藏不可选项。</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:3000" class="button">🚀 进入 MES 系统测试</a>
            <a href="http://localhost:3000/plan-tasks" class="button">📊 直接访问生产计划</a>
        </div>

        <div class="test-section">
            <div class="test-title">📈 预期效果</div>
            <ul class="feature-list">
                <li><span class="check">✓</span>减少用户操作步骤</li>
                <li><span class="check">✓</span>提高计划制定效率</li>
                <li><span class="check">✓</span>避免不合理的时间设置</li>
                <li><span class="check">✓</span>符合实际生产需求</li>
                <li><span class="check">✓</span>降低操作错误率</li>
            </ul>
        </div>
    </div>
</body>
</html>
