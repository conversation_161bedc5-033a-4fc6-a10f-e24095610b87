// 表单验证工具函数
import type { Rule } from 'antd/es/form';

// 通用验证规则
export const validationRules = {
  // 必填项
  required: (message?: string): Rule => ({
    required: true,
    message: message || '此字段为必填项',
  }),

  // 邮箱验证
  email: (message?: string): Rule => ({
    type: 'email',
    message: message || '请输入有效的邮箱地址',
  }),

  // 手机号验证
  phone: (message?: string): Rule => ({
    pattern: /^1[3-9]\d{9}$/,
    message: message || '请输入有效的手机号码',
  }),

  // 密码强度验证
  password: (message?: string): Rule => ({
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: message || '密码至少8位，包含大小写字母和数字',
  }),

  // 用户名验证
  username: (message?: string): Rule => ({
    pattern: /^[a-zA-Z0-9_]{3,20}$/,
    message: message || '用户名只能包含字母、数字和下划线，长度3-20位',
  }),

  // 数字范围验证
  numberRange: (min: number, max: number, message?: string): Rule => ({
    type: 'number',
    min,
    max,
    message: message || `请输入${min}-${max}之间的数字`,
  }),

  // 正整数验证
  positiveInteger: (message?: string): Rule => ({
    pattern: /^[1-9]\d*$/,
    message: message || '请输入正整数',
  }),

  // 非负数验证
  nonNegativeNumber: (message?: string): Rule => ({
    pattern: /^\d+(\.\d+)?$/,
    message: message || '请输入非负数',
  }),

  // 字符串长度验证
  stringLength: (min: number, max: number, message?: string): Rule => ({
    min,
    max,
    message: message || `长度应在${min}-${max}个字符之间`,
  }),

  // 零件编号验证
  partNumber: (message?: string): Rule => ({
    pattern: /^[A-Z0-9-]{3,20}$/,
    message: message || '零件编号只能包含大写字母、数字和连字符，长度3-20位',
  }),

  // 项目编号验证
  projectCode: (message?: string): Rule => ({
    pattern: /^[A-Z0-9]{4,10}$/,
    message: message || '项目编号只能包含大写字母和数字，长度4-10位',
  }),

  // 工单编号验证
  workOrderNumber: (message?: string): Rule => ({
    pattern: /^WO\d{6,10}$/,
    message: message || '工单编号格式：WO + 6-10位数字',
  }),

  // 设备编号验证
  machineCode: (message?: string): Rule => ({
    pattern: /^[A-Z]{2,3}\d{3,6}$/,
    message: message || '设备编号格式：2-3位大写字母 + 3-6位数字',
  }),

  // URL验证
  url: (message?: string): Rule => ({
    type: 'url',
    message: message || '请输入有效的URL地址',
  }),

  // 日期验证
  date: (message?: string): Rule => ({
    type: 'date',
    message: message || '请选择有效的日期',
  }),

  // 自定义验证函数
  custom: (validator: (rule: any, value: any) => Promise<void>, message?: string): Rule => ({
    validator,
    message,
  }),
};

// 业务特定验证规则
export const businessRules = {
  // 项目验证规则
  project: {
    name: [
      validationRules.required('请输入项目名称'),
      validationRules.stringLength(2, 50, '项目名称长度应在2-50个字符之间'),
    ],
    code: [
      validationRules.required('请输入项目编号'),
      validationRules.projectCode(),
    ],
    customerName: [
      validationRules.stringLength(2, 100, '客户名称长度应在2-100个字符之间'),
    ],
    description: [
      validationRules.stringLength(0, 500, '描述长度不能超过500个字符'),
    ],
  },

  // 零件验证规则
  part: {
    partNumber: [
      validationRules.required('请输入零件编号'),
      validationRules.partNumber(),
    ],
    partName: [
      validationRules.required('请输入零件名称'),
      validationRules.stringLength(2, 100, '零件名称长度应在2-100个字符之间'),
    ],
    specifications: [
      validationRules.stringLength(0, 200, '规格说明长度不能超过200个字符'),
    ],
    version: [
      validationRules.required('请输入版本号'),
      validationRules.stringLength(1, 20, '版本号长度应在1-20个字符之间'),
    ],
  },

  // 设备验证规则
  machine: {
    machineCode: [
      validationRules.required('请输入设备编号'),
      validationRules.machineCode(),
    ],
    machineName: [
      validationRules.required('请输入设备名称'),
      validationRules.stringLength(2, 100, '设备名称长度应在2-100个字符之间'),
    ],
    model: [
      validationRules.stringLength(0, 50, '型号长度不能超过50个字符'),
    ],
    manufacturer: [
      validationRules.stringLength(0, 100, '制造商长度不能超过100个字符'),
    ],
  },

  // 工单验证规则
  workOrder: {
    workOrderNumber: [
      validationRules.required('请输入工单编号'),
      validationRules.workOrderNumber(),
    ],
    quantity: [
      validationRules.required('请输入数量'),
      validationRules.positiveInteger('数量必须为正整数'),
      validationRules.numberRange(1, 999999, '数量范围：1-999999'),
    ],
    priority: [
      validationRules.required('请选择优先级'),
    ],
  },

  // 用户验证规则
  user: {
    username: [
      validationRules.required('请输入用户名'),
      validationRules.username(),
    ],
    email: [
      validationRules.required('请输入邮箱'),
      validationRules.email(),
    ],
    phone: [
      validationRules.phone(),
    ],
    password: [
      validationRules.required('请输入密码'),
      validationRules.password(),
    ],
    confirmPassword: (getFieldValue: (name: string) => any) => [
      validationRules.required('请确认密码'),
      validationRules.custom(async (_, value) => {
        if (!value || getFieldValue('password') === value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('两次输入的密码不一致'));
      }),
    ],
  },

  // BOM验证规则
  bom: {
    quantity: [
      validationRules.required('请输入数量'),
      validationRules.positiveInteger('数量必须为正整数'),
      validationRules.numberRange(1, 9999, '数量范围：1-9999'),
    ],
    partId: [
      validationRules.required('请选择零件'),
    ],
  },

  // 质量检验验证规则
  quality: {
    inspectionType: [
      validationRules.required('请选择检验类型'),
    ],
    result: [
      validationRules.required('请选择检验结果'),
    ],
    notes: [
      validationRules.stringLength(0, 1000, '备注长度不能超过1000个字符'),
    ],
  },
};

// 动态验证函数
export const dynamicValidation = {
  // 检查唯一性（需要配合API调用）
  checkUniqueness: (
    checkFn: (value: string) => Promise<boolean>,
    message: string = '该值已存在'
  ) => validationRules.custom(async (_, value) => {
    if (!value) return Promise.resolve();
    
    const isUnique = await checkFn(value);
    if (!isUnique) {
      return Promise.reject(new Error(message));
    }
    return Promise.resolve();
  }),

  // 检查依赖关系
  checkDependency: (
    dependentField: string,
    getFieldValue: (name: string) => any,
    message: string = '请先填写相关字段'
  ) => validationRules.custom(async (_, value) => {
    if (!value) return Promise.resolve();
    
    const dependentValue = getFieldValue(dependentField);
    if (!dependentValue) {
      return Promise.reject(new Error(message));
    }
    return Promise.resolve();
  }),

  // 日期范围验证
  dateRange: (
    startField: string,
    endField: string,
    getFieldValue: (name: string) => any,
    isStartDate: boolean = true
  ) => validationRules.custom(async (_, value) => {
    if (!value) return Promise.resolve();
    
    const startDate = isStartDate ? value : getFieldValue(startField);
    const endDate = isStartDate ? getFieldValue(endField) : value;
    
    if (startDate && endDate && startDate >= endDate) {
      return Promise.reject(new Error('开始日期必须早于结束日期'));
    }
    return Promise.resolve();
  }),
};

// 表单验证辅助函数
export const formHelpers = {
  // 获取字段错误信息
  getFieldError: (errors: any[], fieldName: string): string | undefined => {
    const error = errors.find(err => err.name.includes(fieldName));
    return error?.errors?.[0];
  },

  // 检查表单是否有错误
  hasErrors: (errors: any[]): boolean => {
    return errors.length > 0;
  },

  // 格式化验证错误信息
  formatErrors: (errors: any[]): Record<string, string> => {
    const formatted: Record<string, string> = {};
    errors.forEach(error => {
      const fieldName = error.name.join('.');
      formatted[fieldName] = error.errors?.[0] || '验证失败';
    });
    return formatted;
  },

  // 清理表单数据（移除空值）
  cleanFormData: (data: Record<string, any>): Record<string, any> => {
    const cleaned: Record<string, any> = {};
    Object.keys(data).forEach(key => {
      const value = data[key];
      if (value !== null && value !== undefined && value !== '') {
        cleaned[key] = value;
      }
    });
    return cleaned;
  },
};

export default {
  validationRules,
  businessRules,
  dynamicValidation,
  formHelpers,
};
