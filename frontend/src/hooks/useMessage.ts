import { useCallback } from 'react';
import { App } from 'antd';

/**
 * 自定义Hook，用于在React 18并发模式下安全地显示消息
 * 避免在渲染期间调用message导致的警告
 */
export const useMessage = () => {
  const { message } = App.useApp();

  const showMessage = useCallback((type: 'success' | 'error' | 'warning' | 'info', content: string) => {
    // 使用setTimeout将消息调用推迟到下一个事件循环
    // 这样可以避免在React渲染期间调用message
    setTimeout(() => {
      message[type](content);
    }, 0);
  }, [message]);

  const success = useCallback((content: string) => {
    showMessage('success', content);
  }, [showMessage]);

  const error = useCallback((content: string) => {
    showMessage('error', content);
  }, [showMessage]);

  const warning = useCallback((content: string) => {
    showMessage('warning', content);
  }, [showMessage]);

  const info = useCallback((content: string) => {
    showMessage('info', content);
  }, [showMessage]);

  return {
    success,
    error,
    warning,
    info,
    showMessage,
  };
};

export default useMessage;
