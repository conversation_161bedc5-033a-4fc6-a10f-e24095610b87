import React, { useState } from 'react';
import { Card, Typography, Table, Tag, Space, Button, Modal, Form, Select, Checkbox, message, Divider } from 'antd';
import { EditOutlined, EyeOutlined, SettingOutlined } from '@ant-design/icons';
import { 
  ROLES, 
  ROLE_MENU_CONFIG, 
  PAGE_PERMISSIONS, 
  FEATURE_PERMISSIONS,
  getUserMenuItems 
} from '@/utils/permissions';

const { Title, Text } = Typography;
const { Option } = Select;

interface RoleConfig {
  role: string;
  displayName: string;
  description: string;
  menus: string[];
  features: string[];
}

const RolePermissions: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 角色显示名称映射
  const roleDisplayNames = {
    [ROLES.ADMIN]: '系统管理员',
    [ROLES.PROCESS_ENGINEER]: '工艺工程师',
    [ROLES.PLANNER]: '生产计划员',
    [ROLES.OPERATOR]: '操作员',
    [ROLES.QUALITY_INSPECTOR]: '质检员',
    [ROLES.VIEWER]: '查看者'
  };

  // 角色描述
  const roleDescriptions = {
    [ROLES.ADMIN]: '拥有系统所有权限，可以管理用户、角色和系统配置',
    [ROLES.PROCESS_ENGINEER]: '负责工艺设计、路径规划和技术文档管理',
    [ROLES.PLANNER]: '负责生产计划制定、任务调度和资源分配',
    [ROLES.OPERATOR]: '负责具体生产执行，只能开始/暂停/完成分配给自己的任务',
    [ROLES.QUALITY_INSPECTOR]: '负责质量检验和质量数据管理',
    [ROLES.VIEWER]: '只能查看仪表板，无操作权限'
  };

  // 菜单显示名称映射
  const menuDisplayNames = {
    '/dashboard': '仪表板',
    '/projects': '项目管理',
    '/parts': '零件管理',
    '/machines': '设备管理',
    '/work-orders': '工单管理',
    '/plan-tasks': '生产计划',
    '/execution': '生产执行',
    '/operator-execution': '操作员执行',
    '/quality': '质量管理',
    '/bom': 'BOM管理',
    '/routings': '工艺路径',
    '/users': '用户管理',
    '/role-permissions': '角色权限',
    '/api-test': 'API测试',
    '/database': '数据库管理',
    '/api-debug': 'API调试'
  };

  // 功能显示名称映射
  const featureDisplayNames = {
    'CREATE_PROJECT': '创建项目',
    'CREATE_PART': '创建零件',
    'CREATE_MACHINE': '创建设备',
    'CREATE_WORK_ORDER': '创建工单',
    'CREATE_PLAN_TASK': '创建计划任务',
    'CREATE_ROUTING': '创建工艺路径',
    'EDIT_PROJECT': '编辑项目',
    'EDIT_PART': '编辑零件',
    'EDIT_MACHINE': '编辑设备',
    'EDIT_WORK_ORDER': '编辑工单',
    'EDIT_PLAN_TASK': '编辑计划任务',
    'EDIT_ROUTING': '编辑工艺路径',
    'DELETE_PROJECT': '删除项目',
    'DELETE_PART': '删除零件',
    'DELETE_MACHINE': '删除设备',
    'DELETE_WORK_ORDER': '删除工单',
    'DELETE_PLAN_TASK': '删除计划任务',
    'DELETE_ROUTING': '删除工艺路径',
    'UPDATE_MACHINE_STATUS': '更新设备状态',
    'SUBMIT_QUALITY_DATA': '提交质量数据',
    'EXECUTE_TASK': '执行任务',
    'START_TASK': '开始任务',
    'COMPLETE_TASK': '完成任务',
    'PAUSE_TASK': '暂停任务',
    'MANAGE_USERS': '管理用户',
    'MANAGE_ROLES': '管理角色',
    'MANAGE_SKILLS': '管理技能组'
  };

  // 获取角色的菜单权限
  const getRoleMenus = (role: string): string[] => {
    if (role === ROLES.ADMIN) {
      return Object.keys(PAGE_PERMISSIONS);
    }
    return ROLE_MENU_CONFIG[role as keyof typeof ROLE_MENU_CONFIG] as string[] || [];
  };

  // 获取角色的功能权限
  const getRoleFeatures = (role: string): string[] => {
    const features: string[] = [];
    Object.entries(FEATURE_PERMISSIONS).forEach(([feature, allowedRoles]) => {
      if (allowedRoles.includes(role)) {
        features.push(feature);
      }
    });
    return features;
  };

  // 角色数据
  const roleData: RoleConfig[] = Object.values(ROLES).map(role => ({
    role,
    displayName: roleDisplayNames[role as keyof typeof roleDisplayNames],
    description: roleDescriptions[role as keyof typeof roleDescriptions],
    menus: getRoleMenus(role),
    features: getRoleFeatures(role)
  }));

  const handleViewRole = (role: string) => {
    setSelectedRole(role);
    setIsModalVisible(true);
  };

  const columns = [
    {
      title: '角色',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 150,
      render: (text: string, record: RoleConfig) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.role}
          </Text>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '可访问菜单',
      dataIndex: 'menus',
      key: 'menus',
      width: 200,
      render: (menus: string[]) => (
        <div>
          {menus.slice(0, 3).map(menu => (
            <Tag key={menu} size="small" style={{ marginBottom: 2 }}>
              {menuDisplayNames[menu as keyof typeof menuDisplayNames] || menu}
            </Tag>
          ))}
          {menus.length > 3 && (
            <Tag size="small" color="blue">
              +{menus.length - 3}个
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '功能权限',
      dataIndex: 'features',
      key: 'features',
      width: 200,
      render: (features: string[]) => (
        <div>
          {features.slice(0, 3).map(feature => (
            <Tag key={feature} size="small" color="green" style={{ marginBottom: 2 }}>
              {featureDisplayNames[feature as keyof typeof featureDisplayNames] || feature}
            </Tag>
          ))}
          {features.length > 3 && (
            <Tag size="small" color="green">
              +{features.length - 3}个
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: RoleConfig) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewRole(record.role)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const selectedRoleData = roleData.find(r => r.role === selectedRole);

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              角色权限管理
            </Title>
            <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
              查看和管理系统角色的菜单访问权限和功能权限
            </Typography.Paragraph>
          </div>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={() => window.location.href = '/permission-config'}
          >
            权限配置
          </Button>
        </div>
      </div>

      {/* 权限说明卡片 */}
      <Card style={{ marginBottom: 24 }} title="权限设计说明">
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
          <div>
            <Text strong style={{ color: '#1890ff' }}>计划与执行分离</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>计划员负责任务调度和时间安排</li>
              <li>操作员决定何时开始执行任务</li>
              <li>操作员只能操作分配给自己技能组的任务</li>
            </ul>
          </div>
          <div>
            <Text strong style={{ color: '#52c41a' }}>角色权限控制</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>操作员无法访问设备管理页面</li>
              <li>操作员无法修改设备状态</li>
              <li>只有管理员可以管理用户和角色</li>
            </ul>
          </div>
          <div>
            <Text strong style={{ color: '#faad14' }}>安全设计</Text>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              <li>基于角色的访问控制 (RBAC)</li>
              <li>菜单级别和功能级别双重权限控制</li>
              <li>最小权限原则</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 角色权限表格 */}
      <Card title="系统角色权限概览">
        <Table
          columns={columns}
          dataSource={roleData}
          rowKey="role"
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 角色详情模态框 */}
      <Modal
        title={`角色详情 - ${selectedRoleData?.displayName}`}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setSelectedRole(null);
        }}
        footer={[
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRoleData && (
          <div>
            <div style={{ marginBottom: 20 }}>
              <Text strong>角色描述：</Text>
              <p style={{ marginTop: 8 }}>{selectedRoleData.description}</p>
            </div>

            <Divider />

            <div style={{ marginBottom: 20 }}>
              <Text strong>可访问菜单 ({selectedRoleData.menus.length}个)：</Text>
              <div style={{ marginTop: 8 }}>
                {selectedRoleData.menus.map(menu => (
                  <Tag key={menu} style={{ marginBottom: 4 }}>
                    {menuDisplayNames[menu as keyof typeof menuDisplayNames] || menu}
                  </Tag>
                ))}
              </div>
            </div>

            <Divider />

            <div>
              <Text strong>功能权限 ({selectedRoleData.features.length}个)：</Text>
              <div style={{ marginTop: 8 }}>
                {selectedRoleData.features.map(feature => (
                  <Tag key={feature} color="green" style={{ marginBottom: 4 }}>
                    {featureDisplayNames[feature as keyof typeof featureDisplayNames] || feature}
                  </Tag>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RolePermissions;
