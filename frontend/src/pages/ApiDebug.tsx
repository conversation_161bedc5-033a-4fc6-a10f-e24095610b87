import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Spin } from 'antd';
import { apiClient } from '@/lib/api';

const { Title, Text } = Typography;

const ApiDebug: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});
  const [errors, setErrors] = useState<any>({});

  const testApi = async (name: string, apiCall: () => Promise<any>) => {
    setLoading(true);
    try {
      console.log(`Testing ${name}...`);
      const result = await apiCall();
      console.log(`${name} result:`, result);
      setResults(prev => ({ ...prev, [name]: result }));
      setErrors(prev => ({ ...prev, [name]: null }));
    } catch (error) {
      console.error(`${name} error:`, error);
      setResults(prev => ({ ...prev, [name]: null }));
      setErrors(prev => ({ ...prev, [name]: error }));
    }
    setLoading(false);
  };

  const testAllApis = async () => {
    setLoading(true);
    setResults({});
    setErrors({});

    const tests = [
      { name: 'Dashboard Overview', call: () => apiClient.getDashboardOverview() },
      { name: 'Projects', call: () => apiClient.getProjects() },
      { name: 'Parts', call: () => apiClient.getParts() },
      { name: 'Machines', call: () => apiClient.getMachines() },
      { name: 'Work Orders', call: () => apiClient.getWorkOrders() },
      { name: 'Current User', call: () => apiClient.getCurrentUser() },
    ];

    for (const test of tests) {
      await testApi(test.name, test.call);
      // 添加小延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    setLoading(false);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>API 调试工具</Title>
      
      <Card style={{ marginBottom: '16px' }}>
        <Space>
          <Button type="primary" onClick={testAllApis} loading={loading}>
            测试所有API
          </Button>
          <Button onClick={() => testApi('Dashboard', () => apiClient.getDashboardOverview())}>
            测试仪表板
          </Button>
          <Button onClick={() => testApi('Projects', () => apiClient.getProjects())}>
            测试项目
          </Button>
        </Space>
      </Card>

      {Object.keys(results).length > 0 && (
        <Card title="API 测试结果" style={{ marginBottom: '16px' }}>
          {Object.entries(results).map(([name, result]) => (
            <Card key={name} type="inner" title={name} style={{ marginBottom: '8px' }}>
              {errors[name] ? (
                <Alert
                  message="API 调用失败"
                  description={errors[name]?.message || '未知错误'}
                  type="error"
                  showIcon
                />
              ) : (
                <div>
                  <Text strong>数据类型: </Text>
                  <Text code>{Array.isArray(result) ? 'Array' : typeof result}</Text>
                  <br />
                  {Array.isArray(result) && (
                    <>
                      <Text strong>数组长度: </Text>
                      <Text code>{result.length}</Text>
                      <br />
                    </>
                  )}
                  <Text strong>原始数据:</Text>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    maxHeight: '300px',
                    overflow: 'auto',
                    marginTop: '8px'
                  }}>
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </div>
              )}
            </Card>
          ))}
        </Card>
      )}

      <Card title="网络信息">
        <Text strong>当前URL: </Text>
        <Text code>{window.location.href}</Text>
        <br />
        <Text strong>API Base URL: </Text>
        <Text code>/api</Text>
        <br />
        <Text strong>认证状态: </Text>
        <Text code>{localStorage.getItem('token') ? '已登录' : '未登录'}</Text>
        <br />
        <Text strong>Token: </Text>
        <Text code>{localStorage.getItem('token')?.substring(0, 50)}...</Text>
      </Card>
    </div>
  );
};

export default ApiDebug;
