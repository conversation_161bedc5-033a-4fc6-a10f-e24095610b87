import React, { useState } from 'react';
import {
  Table,
  Typography,
  Card,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Popconfirm,
  Descriptions
} from 'antd';
import { PlusOutlined, EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { WorkOrder, CreateWorkOrderRequest, ProjectBom } from '@/types/api';
import dayjs from 'dayjs';

const { Title } = Typography;

const WorkOrders: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [editingWorkOrder, setEditingWorkOrder] = useState<WorkOrder | null>(null);
  const [viewingWorkOrder, setViewingWorkOrder] = useState<WorkOrder | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const { data: workOrders, isLoading } = useQuery(
    'work-orders',
    () => apiClient.getWorkOrders()
  );

  // Fetch all project BOMs for the dropdown
  const { data: projectBoms = [] } = useQuery(
    'all-project-boms',
    () => apiClient.getAllProjectBoms()
  );

  const createMutation = useMutation(
    (data: CreateWorkOrderRequest) => apiClient.createWorkOrder(data),
    {
      onSuccess: () => {
        success('工单创建成功');
        queryClient.invalidateQueries('work-orders');
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        error('工单创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateWorkOrderRequest> }) =>
      apiClient.updateWorkOrder(id, data),
    {
      onSuccess: () => {
        success('工单更新成功');
        queryClient.invalidateQueries('work-orders');
        setIsModalVisible(false);
        setEditingWorkOrder(null);
        form.resetFields();
      },
      onError: () => {
        error('工单更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteWorkOrder(id),
    {
      onSuccess: () => {
        success('工单删除成功');
        queryClient.invalidateQueries('work-orders');
      },
      onError: () => {
        error('工单删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingWorkOrder(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleView = (workOrder: WorkOrder) => {
    setViewingWorkOrder(workOrder);
    setIsViewModalVisible(true);
  };

  const handleEdit = (workOrder: WorkOrder) => {
    setEditingWorkOrder(workOrder);
    setIsModalVisible(true);
    form.setFieldsValue({
      project_bom_id: workOrder.project_bom_id,
      quantity: workOrder.quantity,
      due_date: workOrder.due_date ? dayjs(workOrder.due_date) : null,
    });
  };

  const handleDelete = (id: number) => {
    deleteMutation.mutate(id);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const submitData = {
        ...values,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : undefined,
      };

      if (editingWorkOrder) {
        updateMutation.mutate({ id: editingWorkOrder.id, data: submitData });
      } else {
        createMutation.mutate(submitData);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'default', text: '待处理' },
      'in-progress': { color: 'blue', text: '进行中' },
      completed: { color: 'green', text: '已完成' },
      cancelled: { color: 'red', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      render: (text: string) => text || '未知项目',
    },
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string) => text || '未知零件',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '截止日期',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD') : '未设置',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: WorkOrder) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个工单吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            工单管理
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建工单
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={workOrders}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: workOrders?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingWorkOrder ? '编辑工单' : '新建工单'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingWorkOrder(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="project_bom_id"
            label="项目BOM"
            rules={[{ required: true, message: '请选择项目BOM' }]}
          >
            <Select
              placeholder="选择项目BOM"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={projectBoms.map(bom => ({
                value: bom.id,
                label: `${bom.project_name || '未知项目'} - ${bom.part_number || '未知零件'} (数量: ${bom.quantity})`,
              }))}
            />
          </Form.Item>

          <Form.Item
            name="quantity"
            label="生产数量"
            rules={[
              { required: true, message: '请输入生产数量' },
              { type: 'number', min: 1, message: '数量必须大于0' },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: '100%' }}
              placeholder="输入生产数量"
            />
          </Form.Item>

          <Form.Item
            name="due_date"
            label="截止日期"
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="选择截止日期"
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看工单详情模态框 */}
      <Modal
        title="工单详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false);
          setViewingWorkOrder(null);
        }}
        footer={[
          <Button key="close" onClick={() => setIsViewModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingWorkOrder && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="工单ID">{viewingWorkOrder.id}</Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(viewingWorkOrder.status)}
              </Descriptions.Item>
              <Descriptions.Item label="项目名称">{viewingWorkOrder.project_name}</Descriptions.Item>
              <Descriptions.Item label="客户名称">{viewingWorkOrder.customer_name}</Descriptions.Item>
              <Descriptions.Item label="零件编号">{viewingWorkOrder.part_number}</Descriptions.Item>
              <Descriptions.Item label="零件名称">{viewingWorkOrder.part_name || '未命名'}</Descriptions.Item>
              <Descriptions.Item label="版本">{viewingWorkOrder.version}</Descriptions.Item>
              <Descriptions.Item label="生产数量">{viewingWorkOrder.quantity}</Descriptions.Item>
              <Descriptions.Item label="BOM数量">{viewingWorkOrder.bom_quantity}</Descriptions.Item>
              <Descriptions.Item label="截止日期">
                {viewingWorkOrder.due_date ? dayjs(viewingWorkOrder.due_date).format('YYYY-MM-DD') : '未设置'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(viewingWorkOrder.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            {viewingWorkOrder.specifications && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>零件规格</Title>
                <div style={{
                  padding: 12,
                  backgroundColor: '#f5f5f5',
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {viewingWorkOrder.specifications}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default WorkOrders;
