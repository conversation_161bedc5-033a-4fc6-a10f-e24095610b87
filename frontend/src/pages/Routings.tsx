import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Popconfirm,
  Typography,
  Card,
  Tag,
  Tabs,
  Divider,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SwapOutlined,
  PartitionOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import ProcessFlowChart from '@/components/ProcessFlowChart';
import ProcessWizard from '@/components/ProcessWizard';
import type {
  RoutingWithPartInfo,
  CreateRoutingRequest,
  UpdateRoutingRequest,
  Part,
  PartRoutingSteps,
  CopyRoutingRequest,
  SkillGroup,
  RoutingStep,
} from '@/types/api';

const { Title } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

const Routings: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRouting, setEditingRouting] = useState<RoutingWithPartInfo | null>(null);
  const [isCopyModalVisible, setIsCopyModalVisible] = useState(false);
  const [copyingFromPartId, setCopyingFromPartId] = useState<number | null>(null);
  const [isWizardVisible, setIsWizardVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('list');
  const [selectedPartId, setSelectedPartId] = useState<number | null>(null);
  const [form] = Form.useForm();
  const [copyForm] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  // 获取所有工艺路由
  const { data: routings = [], isLoading } = useQuery(
    'routings',
    () => apiClient.getRoutings()
  );

  // 获取所有零件
  const { data: parts = [] } = useQuery(
    'parts',
    () => apiClient.getParts()
  );

  // 注释：移除设备列表获取，工艺路由只指向技能组
  // const { data: machines = [] } = useQuery(
  //   'machines',
  //   () => apiClient.getMachines()
  // );

  // 获取技能组列表
  const { data: skillGroups = [] } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  // 获取特定零件的工艺路由
  const { data: partRouting } = useQuery(
    ['part-routing', selectedPartId],
    () => selectedPartId ? apiClient.getPartRouting(selectedPartId) : Promise.resolve(null),
    { enabled: !!selectedPartId }
  );

  // 创建工艺路由
  const createMutation = useMutation(
    (data: CreateRoutingRequest) => apiClient.createRouting(data),
    {
      onSuccess: () => {
        success('工艺路由创建成功');
        queryClient.invalidateQueries('routings');
        if (selectedPartId) {
          queryClient.invalidateQueries(['part-routing', selectedPartId]);
        }
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        error('工艺路由创建失败');
      },
    }
  );

  // 更新工艺路由
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: UpdateRoutingRequest }) =>
      apiClient.updateRouting(id, data),
    {
      onSuccess: () => {
        success('工艺路由更新成功');
        queryClient.invalidateQueries('routings');
        if (selectedPartId) {
          queryClient.invalidateQueries(['part-routing', selectedPartId]);
        }
        setIsModalVisible(false);
        setEditingRouting(null);
        form.resetFields();
      },
      onError: () => {
        error('工艺路由更新失败');
      },
    }
  );

  // 删除工艺路由
  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteRouting(id),
    {
      onSuccess: () => {
        success('工艺路由删除成功');
        queryClient.invalidateQueries('routings');
        if (selectedPartId) {
          queryClient.invalidateQueries(['part-routing', selectedPartId]);
        }
      },
      onError: () => {
        error('工艺路由删除失败');
      },
    }
  );

  // 复制工艺路由
  const copyMutation = useMutation(
    ({ sourcePartId, data }: { sourcePartId: number; data: CopyRoutingRequest }) =>
      apiClient.copyRouting(sourcePartId, data),
    {
      onSuccess: () => {
        success('工艺路由复制成功');
        queryClient.invalidateQueries('routings');
        setIsCopyModalVisible(false);
        setCopyingFromPartId(null);
        copyForm.resetFields();
      },
      onError: () => {
        error('工艺路由复制失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingRouting(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (routing: RoutingWithPartInfo) => {
    setEditingRouting(routing);
    setIsModalVisible(true);
    form.setFieldsValue({
      part_id: routing.part_id,
      step_number: routing.step_number,
      process_name: routing.process_name,
      work_instructions: routing.work_instructions,
      standard_hours: routing.standard_hours,
    });
  };

  const handleDelete = (id: number) => {
    deleteMutation.mutate(id);
  };

  const handleCopy = (partId: number) => {
    setCopyingFromPartId(partId);
    setIsCopyModalVisible(true);
    copyForm.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingRouting) {
        updateMutation.mutate({ id: editingRouting.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCopySubmit = async () => {
    try {
      const values = await copyForm.validateFields();
      if (copyingFromPartId) {
        copyMutation.mutate({
          sourcePartId: copyingFromPartId,
          data: { target_part_id: values.target_part_id }
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 处理流程图中的步骤操作
  const handleFlowChartStepAdd = (stepData: Omit<any, 'id'>) => {
    if (selectedPartId) {
      const createData: CreateRoutingRequest = {
        part_id: selectedPartId,
        step_number: stepData.step_number,
        process_name: stepData.process_name,
        work_instructions: stepData.work_instructions,
        standard_hours: stepData.standard_hours,
      };
      createMutation.mutate(createData);
    }
  };

  const handleFlowChartStepEdit = (stepId: number, stepData: any) => {
    updateMutation.mutate({ id: stepId, data: stepData });
  };

  const handleFlowChartStepDelete = (stepId: number) => {
    deleteMutation.mutate(stepId);
  };

  // 处理向导完成
  const handleWizardFinish = async (partId: number, steps: Omit<RoutingStep, 'id'>[]) => {
    try {
      // 批量创建工艺步骤
      for (const step of steps) {
        const createData: CreateRoutingRequest = {
          part_id: partId,
          step_number: step.step_number,
          process_name: step.process_name,
          work_instructions: step.work_instructions,
          standard_hours: step.standard_hours,
        };
        await apiClient.createRouting(createData);
      }

      success(`成功创建 ${steps.length} 个工艺步骤`);
      queryClient.invalidateQueries('routings');
      if (selectedPartId) {
        queryClient.invalidateQueries(['part-routing', selectedPartId]);
      }
      setIsWizardVisible(false);

      // 切换到按零件查看标签页并选择刚创建的零件
      setActiveTab('by-part');
      setSelectedPartId(partId);
    } catch (err) {
      error('工艺路由创建失败');
      console.error('Failed to create routing:', err);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
      render: (text: string) => text || '未命名',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '工序编号',
      dataIndex: 'step_number',
      key: 'step_number',
      sorter: (a: RoutingWithPartInfo, b: RoutingWithPartInfo) => a.step_number - b.step_number,
    },
    {
      title: '工艺名称',
      dataIndex: 'process_name',
      key: 'process_name',
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '时间预算',
      dataIndex: 'standard_hours',
      key: 'standard_hours',
      render: (hours: number) => hours ? `${hours}h` : '未设置',
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      render: (_: any, record: RoutingWithPartInfo) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(record.part_id)}
          >
            复制
          </Button>
          <Popconfirm
            title="确定要删除这个工艺路由吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            工艺路由管理
          </Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsWizardVisible(true)}
            >
              工艺创建向导
            </Button>
            <Button
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              快速添加
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'list',
              label: (
                <span>
                  <TableOutlined />
                  工艺路由列表
                </span>
              ),
              children: (
                <Table
                  columns={columns}
                  dataSource={routings}
                  rowKey="id"
                  loading={isLoading}
                  pagination={{
                    total: routings.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                />
              ),
            },
            {
              key: 'by-part',
              label: (
                <span>
                  <PartitionOutlined />
                  按零件查看
                </span>
              ),
              children: (
                <div>
                  <Row gutter={16} style={{ marginBottom: 16 }}>
                    <Col span={12}>
                      <Select
                        placeholder="选择零件查看工艺路由"
                        style={{ width: '100%' }}
                        value={selectedPartId}
                        onChange={setSelectedPartId}
                        showSearch
                        optionFilterProp="children"
                      >
                        {parts.map((part: Part) => (
                          <Select.Option key={part.id} value={part.id}>
                            {part.part_number} - {part.part_name || '未命名'} ({part.version})
                          </Select.Option>
                        ))}
                      </Select>
                    </Col>
                  </Row>

                  {partRouting && (
                    <div>
                      <Title level={4} style={{ marginBottom: 16 }}>
                        {partRouting.part_number} - {partRouting.part_name || '未命名'} ({partRouting.version})
                      </Title>

                      {/* 工艺流程图 */}
                      <ProcessFlowChart
                        steps={partRouting.routing_steps}
                        onStepAdd={handleFlowChartStepAdd}
                        onStepEdit={handleFlowChartStepEdit}
                        onStepDelete={handleFlowChartStepDelete}
                        machines={[]}
                        skillGroups={skillGroups}
                        editable={true}
                      />

                      <Divider />

                      {/* 详细表格 */}
                      <Table
                        columns={[
                          { title: '工序编号', dataIndex: 'step_number', key: 'step_number', width: 100 },
                          { title: '工艺名称', dataIndex: 'process_name', key: 'process_name' },
                          { title: '工作指导', dataIndex: 'work_instructions', key: 'work_instructions' },
                          { title: '时间预算', dataIndex: 'standard_hours', key: 'standard_hours', render: (hours: number) => hours ? `${hours}h` : '未设置' },
                        ]}
                        dataSource={partRouting.routing_steps}
                        rowKey="id"
                        pagination={false}
                        size="small"
                      />
                    </div>
                  )}
                </div>
              ),
            },
          ]}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingRouting ? '编辑工艺路由' : '新建工艺路由'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingRouting(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
        okText="确定"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="part_id"
            label="零件"
            rules={[{ required: true, message: '请选择零件' }]}
          >
            <Select
              placeholder="选择零件"
              showSearch
              optionFilterProp="children"
              disabled={!!editingRouting}
            >
              {parts.map((part: Part) => (
                <Select.Option key={part.id} value={part.id}>
                  {part.part_number} - {part.part_name || '未命名'} ({part.version})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="step_number"
            label="工序编号"
            rules={[
              { required: true, message: '请输入工序编号' },
              { type: 'number', min: 1, message: '工序编号必须大于0' },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: '100%' }}
              placeholder="输入工序编号"
              disabled={!!editingRouting}
            />
          </Form.Item>

          <Form.Item
            name="process_name"
            label="工艺名称"
            rules={[{ required: true, message: '请选择或输入工艺名称' }]}
          >
            <Select
              placeholder="选择技能组或手动输入工艺名称"
              showSearch
              allowClear
              mode="combobox"
              optionFilterProp="children"
            >
              <Select.OptGroup label="技能组">
                {skillGroups.map((skillGroup: SkillGroup) => (
                  <Select.Option key={`skill-${skillGroup.id}`} value={skillGroup.group_name}>
                    {skillGroup.group_name}
                  </Select.Option>
                ))}
              </Select.OptGroup>
            </Select>
          </Form.Item>

          <Form.Item
            name="work_instructions"
            label="工作指导"
          >
            <TextArea
              rows={4}
              placeholder="输入工作指导书内容"
            />
          </Form.Item>

          <Form.Item
            name="standard_hours"
            label="时间预算（小时）"
          >
            <InputNumber
              min={0}
              step={0.1}
              style={{ width: '100%' }}
              placeholder="输入时间预算"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 复制工艺路由模态框 */}
      <Modal
        title="复制工艺路由"
        open={isCopyModalVisible}
        onOk={handleCopySubmit}
        onCancel={() => {
          setIsCopyModalVisible(false);
          setCopyingFromPartId(null);
          copyForm.resetFields();
        }}
        confirmLoading={copyMutation.isLoading}
        okText="复制"
        cancelText="取消"
      >
        <Form
          form={copyForm}
          layout="vertical"
        >
          <Form.Item
            name="target_part_id"
            label="目标零件"
            rules={[{ required: true, message: '请选择目标零件' }]}
          >
            <Select
              placeholder="选择要复制到的零件"
              showSearch
              optionFilterProp="children"
            >
              {parts
                .filter((part: Part) => part.id !== copyingFromPartId)
                .map((part: Part) => (
                  <Select.Option key={part.id} value={part.id}>
                    {part.part_number} - {part.part_name || '未命名'} ({part.version})
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 工艺创建向导 */}
      <ProcessWizard
        visible={isWizardVisible}
        onCancel={() => setIsWizardVisible(false)}
        onFinish={handleWizardFinish}
        parts={parts}
        machines={[]}
        skillGroups={skillGroups}
      />
    </div>
  );
};

export default Routings;
