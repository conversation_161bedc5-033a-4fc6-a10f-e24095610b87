import React, { useState } from 'react';
import { <PERSON>, Typo<PERSON>, Space, Button, Divider, Al<PERSON>, Row, Col, Statistic } from 'antd';
import { ThunderboltOutlined, ClockCircleOutlined } from '@ant-design/icons';
import PartSelector from '@/components/PartSelector';
import VirtualizedSelect from '@/components/VirtualizedSelect';
import type { Part } from '@/types/api';

const { Title, Paragraph, Text } = Typography;

const PartSelectionDemo: React.FC = () => {
  const [selectedPart1, setSelectedPart1] = useState<number | undefined>();
  const [selectedPart2, setSelectedPart2] = useState<number | undefined>();
  const [selectedPart3, setSelectedPart3] = useState<number | undefined>();
  const [selectedPartInfo, setSelectedPartInfo] = useState<Part | null>(null);
  const [renderTime, setRenderTime] = useState<number>(0);

  const handlePartSelect = (_partId: number, part: Part) => {
    const startTime = performance.now();
    setSelectedPartInfo(part);
    const endTime = performance.now();
    setRenderTime(endTime - startTime);
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <ThunderboltOutlined style={{ marginRight: 8 }} />
          零件选择组件优化演示
        </Title>
        <Paragraph>
          本页面展示了针对工艺和计划操作优化的零件选择组件，特别是在处理大量零件数据时的性能提升。
        </Paragraph>
      </div>

      <Alert
        message="性能优化特性"
        description={
          <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
            <li><strong>项目索引：</strong>支持按项目快速筛选零件，减少数据量</li>
            <li><strong>虚拟滚动：</strong>只渲染可见区域的选项，支持大量数据</li>
            <li><strong>懒加载：</strong>分页加载数据，避免一次性加载过多内容</li>
            <li><strong>智能缓存：</strong>缓存已加载的数据，减少重复请求</li>
            <li><strong>搜索优化：</strong>支持多维度快速搜索和过滤</li>
          </ul>
        }
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        <Col span={12}>
          <Card title="标准零件选择器" style={{ marginBottom: 24 }}>
            <Paragraph>
              <Text type="secondary">
                传统的零件选择方式，适用于零件数量较少的场景。
              </Text>
            </Paragraph>
            <PartSelector
              value={selectedPart1}
              onChange={(partId, part) => {
                setSelectedPart1(partId);
                handlePartSelect(partId, part);
              }}
              placeholder="选择零件（标准模式）"
              showPartInfo={false}
              mode="all"
            />
          </Card>
        </Col>

        <Col span={12}>
          <Card title="项目索引选择器" style={{ marginBottom: 24 }}>
            <Paragraph>
              <Text type="secondary">
                支持按项目分组的零件选择，可以快速从项目BOM中选择零件。
              </Text>
            </Paragraph>
            <PartSelector
              value={selectedPart2}
              onChange={(partId, part) => {
                setSelectedPart2(partId);
                handlePartSelect(partId, part);
              }}
              placeholder="选择零件（项目索引）"
              showPartInfo={false}
              mode="project-based"
            />
          </Card>
        </Col>
      </Row>

      <Card title="高性能虚拟化选择器" style={{ marginBottom: 24 }}>
        <Paragraph>
          <Text type="secondary">
            使用虚拟滚动技术，支持处理大量零件数据而不影响性能。适用于零件数量超过1000个的场景。
          </Text>
        </Paragraph>
        <Space direction="vertical" style={{ width: '100%' }}>
          <VirtualizedSelect
            value={selectedPart3}
            onChange={(partId, part) => {
              setSelectedPart3(partId);
              handlePartSelect(partId, part);
            }}
            placeholder="选择零件（虚拟化模式）"
            style={{ width: '100%' }}
          />
          
          <Alert
            message="虚拟化优势"
            description="即使有数万个零件，也能保持流畅的滚动和搜索体验。只渲染可见的选项，大大减少DOM节点数量。"
            type="success"
            showIcon
          />
        </Space>
      </Card>

      <Card title="完整功能演示" style={{ marginBottom: 24 }}>
        <Paragraph>
          <Text type="secondary">
            集成所有优化功能的零件选择器，包括项目索引、虚拟化、搜索等。
          </Text>
        </Paragraph>
        <PartSelector
          placeholder="选择零件（完整功能）"
          showPartInfo={true}
          mode="project-based"
          enableVirtualization={true}
          onChange={handlePartSelect}
        />
      </Card>

      {selectedPartInfo && (
        <Card title="选择结果" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic title="零件编号" value={selectedPartInfo.part_number} />
            </Col>
            <Col span={6}>
              <Statistic title="零件名称" value={selectedPartInfo.part_name || '未命名'} />
            </Col>
            <Col span={6}>
              <Statistic title="版本" value={selectedPartInfo.version} />
            </Col>
            <Col span={6}>
              <Statistic 
                title="响应时间" 
                value={renderTime.toFixed(2)} 
                suffix="ms"
                prefix={<ClockCircleOutlined />}
              />
            </Col>
          </Row>
          {selectedPartInfo.specifications && (
            <div style={{ marginTop: 16 }}>
              <Text strong>规格说明：</Text>
              <div>{selectedPartInfo.specifications}</div>
            </div>
          )}
        </Card>
      )}

      <Card title="使用建议">
        <Row gutter={24}>
          <Col span={8}>
            <Card size="small" title="小型项目（< 100个零件）">
              <Paragraph>
                使用标准模式即可，简单直接，加载速度快。
              </Paragraph>
              <Button type="primary" size="small">推荐：标准模式</Button>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="中型项目（100-1000个零件）">
              <Paragraph>
                建议使用项目索引模式，可以按项目快速筛选零件。
              </Paragraph>
              <Button type="primary" size="small">推荐：项目索引</Button>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="大型项目（> 1000个零件）">
              <Paragraph>
                必须使用虚拟化模式，确保界面响应流畅。
              </Paragraph>
              <Button type="primary" size="small">推荐：虚拟化</Button>
            </Card>
          </Col>
        </Row>
      </Card>

      <Divider />
      
      <div style={{ textAlign: 'center', color: '#666' }}>
        <Text>
          这些优化显著提升了工艺创建和计划制定时的零件选择效率，特别是在处理大量零件数据时。
        </Text>
      </div>
    </div>
  );
};

export default PartSelectionDemo;
