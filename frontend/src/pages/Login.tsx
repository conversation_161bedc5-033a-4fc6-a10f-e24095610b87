import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Form, Input, Button, Card, Typography } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/store/auth';
import { useMessage } from '@/hooks/useMessage';
import type { LoginRequest } from '@/types/api';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [loginResult, setLoginResult] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const navigate = useNavigate();
  const { login, isAuthenticated, user } = useAuthStore();
  const { success, error } = useMessage();
  const messageShownRef = useRef(false);

  // 如果已经登录，直接跳转到仪表板
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('User already authenticated, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  // 使用useEffect处理消息显示，避免并发模式警告
  useEffect(() => {
    if (loginResult && !messageShownRef.current) {
      messageShownRef.current = true;
      console.log('Processing login result:', loginResult);

      if (loginResult.type === 'success') {
        success(loginResult.message);
        console.log('Navigating to dashboard...');
        navigate('/dashboard', { replace: true });
      } else {
        error(loginResult.message);
      }

      // 重置状态
      const timer = setTimeout(() => {
        setLoginResult(null);
        messageShownRef.current = false;
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [loginResult, success, error, navigate]);

  const onFinish = useCallback(async (values: LoginRequest) => {
    try {
      setLoading(true);
      console.log('Starting login process...');
      await login(values);
      console.log('Login successful, setting success result');
      setLoginResult({ type: 'success', message: '登录成功' });
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = error?.response?.data?.message ||
                         error?.message ||
                         '登录失败，请检查用户名和密码';
      setLoginResult({ type: 'error', message: errorMessage });
    } finally {
      setLoading(false);
    }
  }, [login]);

  return (
    <div className="login-container">
      <Card className="login-form">
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            MES 制造执行系统
          </Title>
          <Text type="secondary">Manufacturing Execution System</Text>
        </div>
        
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%' }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            默认管理员账号: admin / admin123
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
