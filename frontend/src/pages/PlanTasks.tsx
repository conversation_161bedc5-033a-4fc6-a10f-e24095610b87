import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  Typography,
  Card,
  Tag,
  Button,
  Space,
  Tabs,
  Modal,
  Form,
  DatePicker,
  Select,
  message
} from 'antd';
import { CalendarOutlined, BarChartOutlined, TableOutlined, PlusOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { PlanTaskWithDetails, WorkOrder } from '@/types/api';
import GanttChart from '@/components/GanttChart';
import WorkOrderSelector from '@/components/WorkOrderSelector';
import { hasFeatureAccess } from '@/utils/permissions';
import { useAuthStore } from '@/store/auth';
import dayjs from 'dayjs';

const { Title } = Typography;

const PlanTasks: React.FC = () => {
  const [activeTab, setActiveTab] = useState('table');
  const [isRescheduleModalVisible, setIsRescheduleModalVisible] = useState(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<PlanTaskWithDetails | null>(null);
  const [form] = Form.useForm();
  const [createForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取当前用户信息和权限
  const { user } = useAuthStore();
  const userRoles = user?.roles || [];

  // 权限检查
  const canCreateTask = hasFeatureAccess(userRoles, 'CREATE_PLAN_TASK');
  const canEditTask = hasFeatureAccess(userRoles, 'EDIT_PLAN_TASK');

  // 技能组名称中文映射
  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
    };
    return skillGroupNameMap[groupName] || groupName;
  };



  const { data: planTasks, isLoading } = useQuery(
    'plan-tasks',
    () => apiClient.getPlanTasks()
  );

  const { data: workOrders } = useQuery(
    'work-orders',
    () => apiClient.getWorkOrders()
  );

  const { data: routings } = useQuery(
    'routings',
    () => apiClient.getRoutings()
  );

  // 状态管理：选择的工单和对应的零件ID
  const [selectedWorkOrderId, setSelectedWorkOrderId] = useState<number | null>(null);
  const [selectedPartId, setSelectedPartId] = useState<number | null>(null);

  // 根据选择的工单获取对应的零件ID
  useEffect(() => {
    if (selectedWorkOrderId && workOrders) {
      const selectedWorkOrder = workOrders.find(wo => wo.id === selectedWorkOrderId);
      if (selectedWorkOrder) {
        setSelectedPartId(selectedWorkOrder.part_id);
      }
    } else {
      setSelectedPartId(null);
    }
  }, [selectedWorkOrderId, workOrders]);

  // 根据选择的零件过滤工艺步骤
  const filteredRoutings = useMemo(() => {
    if (!selectedPartId || !routings) {
      return [];
    }
    return routings.filter(routing => routing.part_id === selectedPartId);
  }, [selectedPartId, routings]);

  const updateTaskMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => apiClient.updatePlanTask(id, data),
    {
      onSuccess: () => {
        message.success('任务更新成功');
        queryClient.invalidateQueries('plan-tasks');
        setIsRescheduleModalVisible(false);
        setSelectedTask(null);
        form.resetFields();
      },
      onError: () => {
        message.error('任务更新失败');
      },
    }
  );

  const createTaskMutation = useMutation(
    (data: any) => apiClient.createPlanTask(data),
    {
      onSuccess: () => {
        message.success('计划任务创建成功');
        queryClient.invalidateQueries('plan-tasks');
        setIsCreateModalVisible(false);
        createForm.resetFields();
        setSelectedWorkOrderId(null);
        setSelectedPartId(null);
      },
      onError: () => {
        message.error('计划任务创建失败');
      },
    }
  );



  const handleReschedule = (task: PlanTaskWithDetails) => {
    setSelectedTask(task);
    setIsRescheduleModalVisible(true);
    form.setFieldsValue({
      planned_start: task.planned_start ? dayjs(task.planned_start) : null,
      planned_end: task.planned_end ? dayjs(task.planned_end) : null,
    });
  };

  // 移除handleStartTask - 任务开始应该由操作员在执行页面进行

  const handleRescheduleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (selectedTask) {
        // 验证时间间隔至少30分钟
        const startTime = values.planned_start;
        const endTime = values.planned_end;
        if (startTime && endTime) {
          const diffMinutes = endTime.diff(startTime, 'minute');
          if (diffMinutes < 30) {
            message.error('计划结束时间必须比开始时间至少晚30分钟');
            return;
          }
        }

        updateTaskMutation.mutate({
          id: selectedTask.id,
          data: {
            planned_start: values.planned_start?.format('YYYY-MM-DD HH:mm:00'),
            planned_end: values.planned_end?.format('YYYY-MM-DD HH:mm:00'),
          }
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCreateSubmit = async () => {
    try {
      const values = await createForm.validateFields();

      // 验证时间间隔至少30分钟
      const startTime = values.planned_start;
      const endTime = values.planned_end;
      if (startTime && endTime) {
        const diffMinutes = endTime.diff(startTime, 'minute');
        if (diffMinutes < 30) {
          message.error('计划结束时间必须比开始时间至少晚30分钟');
          return;
        }
      }

      createTaskMutation.mutate({
        work_order_id: values.work_order_id,
        routing_step_id: values.routing_step_id,
        skill_group_id: values.skill_group_id,
        planned_start: values.planned_start?.toISOString(),
        planned_end: values.planned_end?.toISOString(),
      });
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'default', text: '待开始' },
      'in-progress': { color: 'blue', text: '进行中' },
      completed: { color: 'green', text: '已完成' },
      paused: { color: 'orange', text: '已暂停' },
      cancelled: { color: 'red', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工单ID',
      dataIndex: 'work_order_id',
      key: 'work_order_id',
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      key: 'project_name',
    },
    {
      title: '零件',
      dataIndex: 'part_number',
      key: 'part_number',
    },
    {
      title: '工艺',
      dataIndex: 'process_name',
      key: 'process_name',
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      render: (text: string) => text ? getSkillGroupDisplayName(text) : '未分配',
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      render: (text: string) => dayjs(text).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: PlanTaskWithDetails) => (
        <Space>
          {canEditTask && (
            <Button
              type="link"
              icon={<CalendarOutlined />}
              onClick={() => handleReschedule(record)}
            >
              调度
            </Button>
          )}
          {/* 注意：开始任务应该由操作员在执行页面进行，这里移除开始按钮 */}
          {/* 计划员只负责调度，不负责执行 */}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2} style={{ margin: 0 }}>
          生产计划
        </Title>
        {canCreateTask && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            添加计划
          </Button>
        )}
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'table',
              label: (
                <span>
                  <TableOutlined />
                  列表视图
                </span>
              ),
              children: (
                <Table
                  columns={columns}
                  dataSource={planTasks}
                  rowKey="id"
                  loading={isLoading}
                  pagination={{
                    total: planTasks?.length || 0,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                />
              ),
            },
            {
              key: 'gantt',
              label: (
                <span>
                  <BarChartOutlined />
                  甘特图
                </span>
              ),
              children: (
                <GanttChart
                  tasks={planTasks || []}
                  title="生产计划甘特图"
                  height={500}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 任务调度模态框 */}
      <Modal
        title="任务调度"
        open={isRescheduleModalVisible}
        onOk={handleRescheduleSubmit}
        onCancel={() => {
          setIsRescheduleModalVisible(false);
          setSelectedTask(null);
          form.resetFields();
        }}
        confirmLoading={updateTaskMutation.isLoading}
        okText="确定"
        cancelText="取消"
      >
        {selectedTask && (
          <div style={{ marginBottom: 16 }}>
            <p><strong>任务：</strong>{selectedTask.process_name}</p>
            <p><strong>项目：</strong>{selectedTask.project_name}</p>
            <p><strong>零件：</strong>{selectedTask.part_number}</p>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="planned_start"
            label="计划开始时间"
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划开始时间"
            />
          </Form.Item>

          <Form.Item
            name="planned_end"
            label="计划结束时间"
            rules={[{ required: true, message: '请选择计划结束时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划结束时间"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建计划任务模态框 */}
      <Modal
        title="创建计划任务"
        open={isCreateModalVisible}
        onOk={handleCreateSubmit}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
          setSelectedWorkOrderId(null);
          setSelectedPartId(null);
        }}
        confirmLoading={createTaskMutation.isLoading}
        okText="创建"
        cancelText="取消"
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
        >
          <Form.Item
            name="work_order_id"
            label="工单"
            rules={[{ required: true, message: '请选择工单' }]}
          >
            <WorkOrderSelector
              placeholder="请选择工单"
              onChange={(workOrderId, workOrder) => {
                setSelectedWorkOrderId(workOrderId);
                setSelectedPartId(workOrder.part_id || null);
                // 清空工艺步骤选择
                createForm.setFieldsValue({ routing_step_id: undefined });
              }}
              showWorkOrderInfo={true}
              mode="project-based"
            />
          </Form.Item>

          <Form.Item
            name="routing_step_id"
            label="工艺步骤"
            rules={[{ required: true, message: '请选择工艺步骤' }]}
          >
            <Select
              placeholder={selectedWorkOrderId ? "请选择工艺步骤" : "请先选择工单"}
              showSearch
              disabled={!selectedWorkOrderId}
            >
              {filteredRoutings?.map(routing => (
                <Select.Option key={routing.id} value={routing.id}>
                  步骤{routing.step_number}: {routing.process_name}
                  {routing.work_instructions && (
                    <span style={{ color: '#999', marginLeft: 8 }}>
                      - {routing.work_instructions.substring(0, 30)}
                      {routing.work_instructions.length > 30 ? '...' : ''}
                    </span>
                  )}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="skill_group_id"
            label="技能组"
            rules={[{ required: true, message: '请选择技能组' }]}
          >
            <Select placeholder="请选择技能组">
              <Select.Option value={1}>CNC加工</Select.Option>
              <Select.Option value={2}>铣削加工</Select.Option>
              <Select.Option value={3}>车削加工</Select.Option>
              <Select.Option value={4}>磨削加工</Select.Option>
              <Select.Option value={5}>装配</Select.Option>
              <Select.Option value={6}>质量控制</Select.Option>
              <Select.Option value={7}>包装</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="planned_start"
            label="计划开始时间"
            rules={[{ required: true, message: '请选择计划开始时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划开始时间"
            />
          </Form.Item>

          <Form.Item
            name="planned_end"
            label="计划结束时间"
            rules={[{ required: true, message: '请选择计划结束时间' }]}
          >
            <DatePicker
              showTime={{
                minuteStep: 30,
                hideDisabledOptions: true,
                format: 'HH:mm',
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
              placeholder="选择计划结束时间"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PlanTasks;
