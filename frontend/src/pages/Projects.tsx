import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Popconfirm,
  Typography,
  Card,
  Tag,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { Project, CreateProjectRequest } from '@/types/api';
import dayjs from 'dayjs';

const { Title } = Typography;

const Projects: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const { data: projects, isLoading } = useQuery(
    'projects',
    () => apiClient.getProjects(),
    { refetchInterval: 30000 }
  );

  const createMutation = useMutation(
    (data: CreateProjectRequest) => apiClient.createProject(data),
    {
      onSuccess: () => {
        success('项目创建成功');
        queryClient.invalidateQueries('projects');
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        error('项目创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateProjectRequest> }) =>
      apiClient.updateProject(id, data),
    {
      onSuccess: () => {
        success('项目更新成功');
        queryClient.invalidateQueries('projects');
        setIsModalVisible(false);
        setEditingProject(null);
        form.resetFields();
      },
      onError: () => {
        error('项目更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteProject(id),
    {
      onSuccess: () => {
        success('项目删除成功');
        queryClient.invalidateQueries('projects');
      },
      onError: () => {
        error('项目删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingProject(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    setIsModalVisible(true);
    form.setFieldsValue({
      project_name: project.project_name,
      customer_name: project.customer_name,
    });
  };

  const handleDelete = (id: number) => {
    deleteMutation.mutate(id);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingProject) {
        updateMutation.mutate({ id: editingProject.id, data: values });
      } else {
        console.log('Creating project with data:', values);
        createMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (text: string) => text || <Tag color="default">未指定</Tag>,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Project) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => {/* TODO: View project details */}}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            项目管理
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建项目
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: projects?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingProject ? '编辑项目' : '新建项目'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingProject(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="project_name"
            label="项目名称"
            rules={[
              { required: true, message: '请输入项目名称' },
              { min: 2, message: '项目名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="customer_name"
            label="客户名称"
          >
            <Input placeholder="请输入客户名称（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Projects;
