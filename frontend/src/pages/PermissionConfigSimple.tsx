import React, { useState } from 'react';
import {
  Card,
  Select,
  Button,
  message,
  Typography,
  Space,
  Alert,
  Spin
} from 'antd';
import { useQuery } from 'react-query';
import { useAuthStore } from '@/store/auth';
import { hasFeatureAccess } from '@/utils/permissions';

const { Title } = Typography;
const { Option } = Select;

interface Role {
  id: number;
  role_name: string;
  role_type?: string;
}

interface RolesResponse {
  roles: Role[];
}

const PermissionConfigSimple: React.FC = () => {
  const { user } = useAuthStore();
  const [selectedRole, setSelectedRole] = useState<number | null>(null);

  // 检查权限
  if (!user || !hasFeatureAccess(user.roles || [], 'MANAGE_PERMISSIONS')) {
    return (
      <Alert
        message="权限不足"
        description="您没有权限访问权限配置功能。"
        type="error"
        showIcon
      />
    );
  }

  // 获取所有角色
  const { data: rolesData, isLoading: rolesLoading, error: rolesError } = useQuery<RolesResponse>(
    'roles',
    async () => {
      const response = await fetch('/api/auth/roles', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      return response.json();
    }
  );

  const roles = rolesData?.roles || [];

  // 获取所有权限
  const { data: permissions, isLoading: permissionsLoading } = useQuery(
    'permissions',
    async () => {
      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      return response.json();
    }
  );

  // 获取角色权限
  const { data: rolePermissions, isLoading: rolePermissionsLoading } = useQuery(
    ['role-permissions', selectedRole],
    async () => {
      if (!selectedRole) return null;
      const response = await fetch(`/api/roles/${selectedRole}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch role permissions');
      }
      return response.json();
    },
    {
      enabled: !!selectedRole,
    }
  );

  if (rolesError) {
    return (
      <Alert
        message="加载失败"
        description={`无法加载角色数据: ${rolesError}`}
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      <div className="page-header">
        <Title level={2}>权限配置管理 (简化版)</Title>
      </div>

      <Card title="调试信息" style={{ marginBottom: 16 }}>
        <p>用户: {user?.username}</p>
        <p>用户角色: {user?.roles?.join(', ')}</p>
        <p>角色数据加载状态: {rolesLoading ? '加载中' : '已加载'}</p>
        <p>角色数据类型: {typeof rolesData}</p>
        <p>角色数组长度: {roles.length}</p>
        <p>权限数据长度: {permissions?.length || 0}</p>
        {rolesData && (
          <details>
            <summary>原始角色数据</summary>
            <pre>{JSON.stringify(rolesData, null, 2)}</pre>
          </details>
        )}
      </Card>

      <Card title="角色选择">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Select
            style={{ width: '100%' }}
            placeholder="请选择要配置的角色"
            value={selectedRole}
            onChange={setSelectedRole}
            loading={rolesLoading}
          >
            {roles.map((role: Role) => (
              <Option key={role.id} value={role.id}>
                {role.role_name}
                {role.role_type === 'system' && ' (系统角色)'}
              </Option>
            ))}
          </Select>

          {selectedRole && (
            <Card title={`角色权限信息`} size="small">
              {rolePermissionsLoading ? (
                <Spin />
              ) : rolePermissions ? (
                <div>
                  <p>角色名称: {rolePermissions.role_name}</p>
                  <p>角色类型: {rolePermissions.role_type}</p>
                  <p>权限数量: {rolePermissions.permissions?.length || 0}</p>
                  <p>已授权权限数量: {rolePermissions.permissions?.filter((p: any) => p.granted).length || 0}</p>
                </div>
              ) : (
                <p>无法加载角色权限信息</p>
              )}
            </Card>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default PermissionConfigSimple;
