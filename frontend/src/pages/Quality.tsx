import React, { useState } from 'react';
import {
  Table,
  Typography,
  Card,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Select,
  Input,
  message
} from 'antd';
import { PlusOutlined, SafetyCertificateOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { QualityInspection, CreateQualityInspectionRequest, PlanTask, User } from '@/types/api';
import dayjs from 'dayjs';

const { Title } = Typography;

const Quality: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { data: inspections, isLoading } = useQuery(
    'quality-inspections',
    () => apiClient.getQualityInspections()
  );

  // 获取计划任务列表
  const { data: planTasks = [] } = useQuery(
    'plan-tasks',
    () => apiClient.getPlanTasks()
  );

  // 获取用户列表
  const { data: users = [] } = useQuery(
    'users',
    () => apiClient.getUsers()
  );

  const createMutation = useMutation(
    (data: CreateQualityInspectionRequest) => apiClient.createQualityInspection(data),
    {
      onSuccess: () => {
        message.success('质量检验创建成功');
        queryClient.invalidateQueries('quality-inspections');
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        message.error('质量检验创建失败');
      },
    }
  );

  const handleCreate = () => {
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      createMutation.mutate(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'default', text: '待检验' },
      'in-progress': { color: 'blue', text: '检验中' },
      passed: { color: 'green', text: '合格' },
      failed: { color: 'red', text: '不合格' },
      cancelled: { color: 'orange', text: '已取消' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getResultTag = (result: string) => {
    const resultMap: Record<string, { color: string; text: string }> = {
      pass: { color: 'green', text: '合格' },
      fail: { color: 'red', text: '不合格' },
      pending: { color: 'default', text: '待定' },
    };
    const config = resultMap[result] || { color: 'default', text: result };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务ID',
      dataIndex: 'plan_task_id',
      key: 'plan_task_id',
    },
    {
      title: '检验员',
      dataIndex: 'inspector_user_id',
      key: 'inspector_user_id',
    },
    {
      title: '检验类型',
      dataIndex: 'inspection_type',
      key: 'inspection_type',
      render: (text: string) => <Tag>{text}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      render: (result: string) => getResultTag(result),
    },
    {
      title: '检验日期',
      dataIndex: 'inspection_date',
      key: 'inspection_date',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: QualityInspection) => (
        <Space>
          <Button
            type="link"
            icon={<SafetyCertificateOutlined />}
            onClick={() => {/* TODO: View inspection details */}}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            质量管理
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建检验
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={inspections}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: inspections?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 新建质量检验模态框 */}
      <Modal
        title="新建质量检验"
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="plan_task_id"
            label="计划任务"
            rules={[{ required: true, message: '请选择计划任务' }]}
          >
            <Select
              placeholder="选择计划任务"
              showSearch
              optionFilterProp="children"
            >
              {planTasks.map((task: PlanTask) => (
                <Select.Option key={task.id} value={task.id}>
                  {task.project_name} - {task.part_number} - {task.process_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="inspector_user_id"
            label="检验员"
            rules={[{ required: true, message: '请选择检验员' }]}
          >
            <Select
              placeholder="选择检验员"
              showSearch
              optionFilterProp="children"
            >
              {users.map((user: User) => (
                <Select.Option key={user.id} value={user.id}>
                  {user.full_name || user.username}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="inspection_type"
            label="检验类型"
            rules={[{ required: true, message: '请选择检验类型' }]}
          >
            <Select placeholder="选择检验类型">
              <Select.Option value="incoming">来料检验</Select.Option>
              <Select.Option value="in-process">过程检验</Select.Option>
              <Select.Option value="final">最终检验</Select.Option>
              <Select.Option value="outgoing">出货检验</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea
              rows={4}
              placeholder="输入检验备注"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Quality;
