import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tabs,
  Statistic,
  Row,
  Col,
  Alert,
  Tag,
  Spin,
} from 'antd';
import {
  DatabaseOutlined,
  ReloadOutlined,
  TableOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const DatabaseViewer: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // 查询各种数据
  const { data: projects, isLoading: projectsLoading, refetch: refetchProjects } = useQuery(
    'db-projects',
    () => apiClient.getProjects()
  );

  const { data: parts, isLoading: partsLoading, refetch: refetchParts } = useQuery(
    'db-parts',
    () => apiClient.getParts()
  );

  const { data: machines, isLoading: machinesLoading, refetch: refetchMachines } = useQuery(
    'db-machines',
    () => apiClient.getMachines()
  );

  const { data: workOrders, isLoading: workOrdersLoading, refetch: refetchWorkOrders } = useQuery(
    'db-work-orders',
    () => apiClient.getWorkOrders()
  );

  const { data: users, isLoading: usersLoading, refetch: refetchUsers } = useQuery(
    'db-users',
    () => apiClient.getUsers()
  );

  const { data: dashboard, isLoading: dashboardLoading, refetch: refetchDashboard } = useQuery(
    'db-dashboard',
    () => apiClient.getDashboardOverview()
  );

  const refreshAll = () => {
    refetchProjects();
    refetchParts();
    refetchMachines();
    refetchWorkOrders();
    refetchUsers();
    refetchDashboard();
  };

  const isLoading = projectsLoading || partsLoading || machinesLoading || workOrdersLoading || usersLoading || dashboardLoading;

  // 项目表格列定义
  const projectColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (text: string) => text || <Tag color="default">未指定</Tag>,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // 零件表格列定义
  const partColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '零件编号',
      dataIndex: 'part_number',
      key: 'part_number',
      render: (text: string) => <Text code>{text}</Text>,
    },
    {
      title: '零件名称',
      dataIndex: 'part_name',
      key: 'part_name',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '规格说明',
      dataIndex: 'specifications',
      key: 'specifications',
      ellipsis: true,
    },
  ];

  // 设备表格列定义
  const machineColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '设备名称',
      dataIndex: 'machine_name',
      key: 'machine_name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      render: (text: string) => <Tag color="green">{text}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: { [key: string]: string } = {
          available: 'green',
          in_use: 'blue',
          maintenance: 'orange',
          offline: 'red',
        };
        const textMap: { [key: string]: string } = {
          available: '可用',
          in_use: '使用中',
          maintenance: '维护中',
          offline: '离线',
        };
        return <Tag color={colorMap[status]}>{textMap[status] || status}</Tag>;
      },
    },
  ];

  // 工单表格列定义
  const workOrderColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: 'BOM ID',
      dataIndex: 'project_bom_id',
      key: 'project_bom_id',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: { [key: string]: string } = {
          pending: 'orange',
          planned: 'blue',
          in_progress: 'cyan',
          completed: 'green',
          cancelled: 'red',
        };
        const textMap: { [key: string]: string } = {
          pending: '待处理',
          planned: '已计划',
          in_progress: '进行中',
          completed: '已完成',
          cancelled: '已取消',
        };
        return <Tag color={colorMap[status]}>{textMap[status] || status}</Tag>;
      },
    },
    {
      title: '交期',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD') : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // 用户表格列定义
  const userColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => <Text code>{text}</Text>,
    },
    {
      title: '全名',
      dataIndex: 'full_name',
      key: 'full_name',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string[]) => (
        <>
          {roles?.map(role => (
            <Tag key={role} color="purple">{role}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '技能',
      dataIndex: 'skills',
      key: 'skills',
      render: (skills: string[]) => (
        <>
          {skills?.slice(0, 2).map(skill => (
            <Tag key={skill} color="cyan">{skill}</Tag>
          ))}
          {skills?.length > 2 && <Tag>+{skills.length - 2}</Tag>}
        </>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '激活' : '禁用'}
        </Tag>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <DatabaseOutlined /> 数据库查看器
        </Title>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={refreshAll}
          loading={isLoading}
        >
          刷新数据
        </Button>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<span><BarChartOutlined />数据概览</span>} key="overview">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="项目总数"
                  value={projects?.length || 0}
                  prefix={<DatabaseOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="零件总数"
                  value={parts?.length || 0}
                  prefix={<TableOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="设备总数"
                  value={machines?.length || 0}
                  prefix={<DatabaseOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="工单总数"
                  value={workOrders?.length || 0}
                  prefix={<TableOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>

          {dashboard && (
            <Card title="系统状态" style={{ marginTop: '16px' }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Alert
                    message="设备利用率"
                    description={`${dashboard.machine_status?.utilization_rate || 0}%`}
                    type="info"
                    showIcon
                  />
                </Col>
                <Col span={8}>
                  <Alert
                    message="质量合格率"
                    description={`${dashboard.quality_metrics?.quality_rate || 0}%`}
                    type="success"
                    showIcon
                  />
                </Col>
                <Col span={8}>
                  <Alert
                    message="今日完成任务"
                    description={`${dashboard.production_summary?.tasks_completed_today || 0} 个`}
                    type="warning"
                    showIcon
                  />
                </Col>
              </Row>
            </Card>
          )}
        </TabPane>

        <TabPane tab={<span><TableOutlined />项目数据</span>} key="projects">
          <Card title={`项目列表 (${projects?.length || 0} 条记录)`}>
            <Spin spinning={projectsLoading}>
              <Table
                columns={projectColumns}
                dataSource={projects}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><TableOutlined />零件数据</span>} key="parts">
          <Card title={`零件列表 (${parts?.length || 0} 条记录)`}>
            <Spin spinning={partsLoading}>
              <Table
                columns={partColumns}
                dataSource={parts}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><TableOutlined />设备数据</span>} key="machines">
          <Card title={`设备列表 (${machines?.length || 0} 条记录)`}>
            <Spin spinning={machinesLoading}>
              <Table
                columns={machineColumns}
                dataSource={machines}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><TableOutlined />工单数据</span>} key="work-orders">
          <Card title={`工单列表 (${workOrders?.length || 0} 条记录)`}>
            <Spin spinning={workOrdersLoading}>
              <Table
                columns={workOrderColumns}
                dataSource={workOrders}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><TableOutlined />用户数据</span>} key="users">
          <Card title={`用户列表 (${users?.length || 0} 条记录)`}>
            <Spin spinning={usersLoading}>
              <Table
                columns={userColumns}
                dataSource={users}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DatabaseViewer;
