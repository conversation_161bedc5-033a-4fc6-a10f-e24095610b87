import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Divider,
  Alert,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Row,
  Col,
  Spin,
} from 'antd';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { CreateProjectRequest, CreatePartRequest, CreateMachineRequest, CreateWorkOrderRequest } from '@/types/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const ApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  // Forms
  const [projectForm] = Form.useForm();
  const [partForm] = Form.useForm();
  const [machineForm] = Form.useForm();
  const [workOrderForm] = Form.useForm();

  // Queries
  const { data: projects } = useQuery('projects', () => apiClient.getProjects());
  const { data: parts } = useQuery('parts', () => apiClient.getParts());
  const { data: machines } = useQuery('machines', () => apiClient.getMachines());
  const { data: workOrders } = useQuery('work-orders', () => apiClient.getWorkOrders());
  const { data: skillGroups } = useQuery('skill-groups', () => apiClient.getSkillGroups());
  const { data: dashboard } = useQuery('dashboard', () => apiClient.getDashboardOverview());

  // Mutations
  const createProjectMutation = useMutation(
    (data: CreateProjectRequest) => apiClient.createProject(data),
    {
      onSuccess: (data) => {
        success('项目创建成功');
        queryClient.invalidateQueries('projects');
        projectForm.resetFields();
        addTestResult('创建项目', true, data);
      },
      onError: (err) => {
        error('项目创建失败');
        addTestResult('创建项目', false, err);
      },
    }
  );

  const createPartMutation = useMutation(
    (data: CreatePartRequest) => apiClient.createPart(data),
    {
      onSuccess: (data) => {
        success('零件创建成功');
        queryClient.invalidateQueries('parts');
        partForm.resetFields();
        addTestResult('创建零件', true, data);
      },
      onError: (err) => {
        error('零件创建失败');
        addTestResult('创建零件', false, err);
      },
    }
  );

  const createMachineMutation = useMutation(
    (data: CreateMachineRequest) => apiClient.createMachine(data),
    {
      onSuccess: (data) => {
        success('设备创建成功');
        queryClient.invalidateQueries('machines');
        machineForm.resetFields();
        addTestResult('创建设备', true, data);
      },
      onError: (err) => {
        error('设备创建失败');
        addTestResult('创建设备', false, err);
      },
    }
  );

  const createWorkOrderMutation = useMutation(
    (data: CreateWorkOrderRequest) => apiClient.createWorkOrder(data),
    {
      onSuccess: (data) => {
        success('工单创建成功');
        queryClient.invalidateQueries('work-orders');
        workOrderForm.resetFields();
        addTestResult('创建工单', true, data);
      },
      onError: (err) => {
        error('工单创建失败');
        addTestResult('创建工单', false, err);
      },
    }
  );

  const addTestResult = (action: string, success: boolean, data: any) => {
    const result = {
      timestamp: new Date().toLocaleTimeString(),
      action,
      success,
      data: JSON.stringify(data, null, 2),
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  const testAllApis = async () => {
    setLoading(true);
    try {
      // Test dashboard
      const dashboardData = await apiClient.getDashboardOverview();
      addTestResult('获取仪表板数据', true, dashboardData);

      // Test projects
      const projectsData = await apiClient.getProjects();
      addTestResult('获取项目列表', true, projectsData);

      // Test parts
      const partsData = await apiClient.getParts();
      addTestResult('获取零件列表', true, partsData);

      // Test machines
      const machinesData = await apiClient.getMachines();
      addTestResult('获取设备列表', true, machinesData);

      // Test work orders
      const workOrdersData = await apiClient.getWorkOrders();
      addTestResult('获取工单列表', true, workOrdersData);

      success('所有API测试完成');
    } catch (err) {
      error('API测试失败');
      addTestResult('API测试', false, err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async () => {
    try {
      const values = await projectForm.validateFields();
      createProjectMutation.mutate(values);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleCreatePart = async () => {
    try {
      const values = await partForm.validateFields();
      createPartMutation.mutate(values);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleCreateMachine = async () => {
    try {
      const values = await machineForm.validateFields();
      createMachineMutation.mutate(values);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleCreateWorkOrder = async () => {
    try {
      const values = await workOrderForm.validateFields();
      // Convert date to string
      if (values.due_date) {
        values.due_date = values.due_date.format('YYYY-MM-DD');
      }
      createWorkOrderMutation.mutate(values);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>API 接口测试</Title>
      <Paragraph>
        这个页面用于测试前端与后端API的连接状态，并提供快速添加测试数据的功能。
      </Paragraph>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="API 连接测试" extra={
            <Button type="primary" onClick={testAllApis} loading={loading}>
              测试所有API
            </Button>
          }>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="API状态"
                description={`当前已连接到后端服务，共有 ${projects?.length || 0} 个项目，${parts?.length || 0} 个零件，${machines?.length || 0} 台设备，${workOrders?.length || 0} 个工单`}
                type="success"
                showIcon
              />
              
              {dashboard && (
                <Alert
                  message="仪表板数据"
                  description={`设备利用率: ${dashboard.machine_status?.utilization_rate || 0}%，质量合格率: ${dashboard.quality_metrics?.quality_rate || 0}%`}
                  type="info"
                  showIcon
                />
              )}
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="创建项目" size="small">
            <Form form={projectForm} layout="vertical">
              <Form.Item
                name="project_name"
                label="项目名称"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input placeholder="例如: 模具项目001" />
              </Form.Item>
              <Form.Item name="customer_name" label="客户名称">
                <Input placeholder="例如: ABC公司" />
              </Form.Item>
              <Button 
                type="primary" 
                onClick={handleCreateProject}
                loading={createProjectMutation.isLoading}
                block
              >
                创建项目
              </Button>
            </Form>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="创建零件" size="small">
            <Form form={partForm} layout="vertical">
              <Form.Item
                name="part_number"
                label="零件编号"
                rules={[{ required: true, message: '请输入零件编号' }]}
              >
                <Input placeholder="例如: PART-004" />
              </Form.Item>
              <Form.Item name="part_name" label="零件名称">
                <Input placeholder="例如: 底座" />
              </Form.Item>
              <Form.Item
                name="version"
                label="版本"
                rules={[{ required: true, message: '请输入版本' }]}
              >
                <Input placeholder="例如: v1.0" />
              </Form.Item>
              <Form.Item name="specifications" label="规格说明">
                <TextArea rows={2} placeholder="例如: 铝合金材质，尺寸100x50x10mm" />
              </Form.Item>
              <Button 
                type="primary" 
                onClick={handleCreatePart}
                loading={createPartMutation.isLoading}
                block
              >
                创建零件
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      <Divider />

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="创建设备" size="small">
            <Form form={machineForm} layout="vertical">
              <Form.Item
                name="machine_name"
                label="设备名称"
                rules={[{ required: true, message: '请输入设备名称' }]}
              >
                <Input placeholder="例如: CNC-003" />
              </Form.Item>
              <Form.Item
                name="skill_group_id"
                label="技能组"
                rules={[{ required: true, message: '请选择技能组' }]}
              >
                <Select placeholder="选择技能组">
                  {skillGroups?.map(group => (
                    <Select.Option key={group.id} value={group.id}>
                      {group.group_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="status" label="状态" initialValue="available">
                <Select>
                  <Select.Option value="available">可用</Select.Option>
                  <Select.Option value="in_use">使用中</Select.Option>
                  <Select.Option value="maintenance">维护中</Select.Option>
                  <Select.Option value="offline">离线</Select.Option>
                </Select>
              </Form.Item>
              <Button 
                type="primary" 
                onClick={handleCreateMachine}
                loading={createMachineMutation.isLoading}
                block
              >
                创建设备
              </Button>
            </Form>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="创建工单" size="small">
            <Form form={workOrderForm} layout="vertical">
              <Form.Item
                name="project_bom_id"
                label="项目BOM"
                rules={[{ required: true, message: '请选择项目BOM' }]}
              >
                <Select placeholder="选择项目BOM">
                  <Select.Option value={1}>项目1 - Base Plate (BOM ID: 1)</Select.Option>
                  <Select.Option value={2}>项目1 - Cover (BOM ID: 2)</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="quantity"
                label="数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <InputNumber min={1} placeholder="例如: 50" style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="due_date" label="交期">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
              <Button 
                type="primary" 
                onClick={handleCreateWorkOrder}
                loading={createWorkOrderMutation.isLoading}
                block
              >
                创建工单
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      <Divider />

      <Card title="测试结果" size="small">
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {testResults.length === 0 ? (
            <Text type="secondary">暂无测试结果</Text>
          ) : (
            testResults.map((result, index) => (
              <Card key={index} size="small" style={{ marginBottom: 8 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <Text strong>{result.timestamp}</Text>
                    <Text>{result.action}</Text>
                    <Text type={result.success ? 'success' : 'danger'}>
                      {result.success ? '✅ 成功' : '❌ 失败'}
                    </Text>
                  </Space>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {result.data}
                  </pre>
                </Space>
              </Card>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};

export default ApiTest;
