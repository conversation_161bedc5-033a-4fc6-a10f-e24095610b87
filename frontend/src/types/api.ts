// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message: string;
  timestamp: string;
}

export interface ApiError {
  error: string;
  message: string;
  timestamp: string;
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface User {
  id: number;
  username: string;
  full_name?: string;
  is_active: boolean;
  roles: string[];
  skills: string[];
  created_at?: string; // 可选字段，因为登录响应中可能不包含
}

export interface Role {
  id: number;
  role_name: string;
}

export interface SkillGroup {
  id: number;
  group_name: string;
}

export interface CreateRoleRequest {
  role_name: string;
}

export interface RoleDependencyInfo {
  can_delete: boolean;
  is_system_role: boolean;
  affected_users: UserInfo[];
  warnings: string[];
}

export interface CreateSkillGroupRequest {
  group_name: string;
}

export interface SkillGroupDependencyInfo {
  can_delete: boolean;
  is_system_skill_group: boolean;
  affected_users: UserInfo[];
  affected_machines: MachineInfo[];
  affected_plan_tasks: PlanTaskInfo[];
  warnings: string[];
}

export interface UserInfo {
  id: number;
  username: string;
  full_name?: string;
}

export interface MachineInfo {
  id: number;
  machine_name: string;
}

export interface PlanTaskInfo {
  id: number;
  task_name: string;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  full_name?: string;
  role_ids: number[];
  skill_group_ids: number[];
}

export interface CreateRoleRequest {
  role_name: string;
  description?: string;
}

export interface CreateSkillGroupRequest {
  group_name: string;
  description?: string;
}

export interface RoleDependencyInfo {
  role_id: number;
  role_name: string;
  can_delete: boolean;
  blocking_reason?: string;
  affected_users: {
    id: number;
    username: string;
    full_name?: string;
  }[];
  user_count: number;
}

export interface SkillGroupDependencyInfo {
  skill_group_id: number;
  group_name: string;
  can_delete: boolean;
  blocking_reason?: string;
  affected_users: {
    id: number;
    username: string;
    full_name?: string;
  }[];
  affected_machines: {
    id: number;
    machine_name: string;
  }[];
  affected_plan_tasks: {
    id: number;
    work_order_id: number;
  }[];
  user_count: number;
  machine_count: number;
  plan_task_count: number;
}

// Project Management Types
export interface Project {
  id: number;
  project_name: string;
  customer_name?: string;
  created_at: string;
}

export interface CreateProjectRequest {
  project_name: string;
  customer_name?: string;
}

// Parts Management Types
export interface Part {
  id: number;
  part_number: string;
  part_name?: string;
  version: string;
  specifications?: string;
}

export interface CreatePartRequest {
  part_number: string;
  part_name?: string;
  version: string;
  specifications?: string;
}

// BOM Types
export interface ProjectBom {
  id: number;
  project_id: number;
  part_id: number;
  quantity: number;
  part_number?: string;
  part_name?: string;
  version?: string;
  specifications?: string;
  part?: Part;
}

export interface CreateProjectBomRequest {
  part_id: number;
  quantity: number;
}

// Machine Types
export interface Machine {
  id: number;
  machine_name: string;
  skill_group_id: number;
  status: string;
  skill_group_name?: string;
}

export interface CreateMachineRequest {
  machine_name: string;
  skill_group_id: number;
  status?: string;
}

// Work Order Types
export interface WorkOrder {
  id: number;
  project_bom_id: number;
  quantity: number;
  status: string;
  due_date?: string;
  created_at: string;
  project_name?: string;
  part_number?: string;
  part_id?: number; // 添加part_id字段
}

export interface CreateWorkOrderRequest {
  project_bom_id: number;
  quantity: number;
  due_date?: string;
  priority?: string;
}

// Plan Task Types
export interface PlanTask {
  id: number;
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  planned_start: string;
  planned_end: string;
  status: string;
  work_order?: WorkOrder;
  skill_group_name?: string;
}

export interface CreatePlanTaskRequest {
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  planned_start: string;
  planned_end: string;
}

export interface UpdatePlanTaskRequest {
  skill_group_id?: number;
  planned_start?: string;
  planned_end?: string;
  status?: string;
}

export interface ReschedulePlanTaskRequest {
  planned_start: string;
  planned_end: string;
}

export interface CreatePlanTasksFromWorkOrderRequest {
  start_date: string;
  skill_group_assignments?: SkillGroupAssignment[];
}

export interface SkillGroupAssignment {
  routing_step_id: number;
  skill_group_id: number;
}

export interface PlanTaskWithDetails {
  id: number;
  work_order_id: number;
  routing_step_id: number;
  skill_group_id: number;
  planned_start: string;
  planned_end: string;
  status: string;
  // Work order details
  work_order_quantity: number;
  work_order_status: string;
  work_order_due_date?: string;
  // Project details
  project_id: number;
  project_name: string;
  customer_name?: string;
  // Part details
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  // Routing step details
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
  // Skill group details
  skill_group_name: string;
}

export interface PlanTaskSearchResult {
  plan_tasks: PlanTaskWithDetails[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface GanttChartData {
  tasks: GanttTask[];
  skill_groups: GanttSkillGroup[];
  time_range: GanttTimeRange;
}

export interface GanttTask {
  id: number;
  name: string;
  start: string;
  end: string;
  skill_group_id: number;
  work_order_id: number;
  part_number: string;
  process_name: string;
  status: string;
  progress: number; // 0.0 to 1.0
}

export interface GanttSkillGroup {
  id: number;
  name: string;
  machines: GanttMachine[];
}

export interface GanttMachine {
  id: number;
  name: string;
  status: string;
}

export interface GanttTimeRange {
  start: string;
  end: string;
}

// Execution Types
export interface ExecutionLog {
  id: number;
  plan_task_id: number;
  machine_id?: number;
  user_id: number;
  event_type: string;
  event_time: string;
  notes?: string;
}

export interface TaskExecutionRequest {
  plan_task_id: number;
  machine_id?: number;
  notes?: string;
}

// Quality Types
export interface QualityInspection {
  id: number;
  plan_task_id: number;
  inspector_user_id: number;
  inspection_type: string;
  status: string;
  result: string;
  notes?: string;
  inspection_date: string;
  created_at: string;
}

export interface CreateQualityInspectionRequest {
  plan_task_id: number;
  inspector_user_id: number;
  inspection_type: string;
  notes?: string;
}

// Routing Types
export interface Routing {
  id: number;
  part_id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface RoutingWithPartInfo {
  id: number;
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface CreateRoutingRequest {
  part_id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface UpdateRoutingRequest {
  process_name?: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface PartRoutingSteps {
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  routing_steps: RoutingStep[];
}

export interface RoutingStep {
  id: number;
  step_number: number;
  process_name: string;
  work_instructions?: string;
  standard_hours?: number;
}

export interface RoutingQuery {
  part_id?: number;
  process_name?: string;
}

export interface ReorderStepsRequest {
  step_orders: StepOrder[];
}

export interface StepOrder {
  routing_id: number;
  new_step_number: number;
}

export interface CopyRoutingRequest {
  target_part_id: number;
}

// Dashboard Types
export interface DashboardOverview {
  machine_status: {
    available_machines: number;
    in_use_machines: number;
    maintenance_machines: number;
    offline_machines: number;
    total_machines: number;
    utilization_rate: number;
  };
  production_summary: {
    on_time_delivery_rate: number;
    overall_efficiency: number;
    tasks_completed_today: number;
    tasks_in_progress: number;
    tasks_pending: number;
    total_work_orders: number;
  };
  quality_metrics: {
    defect_rate: number;
    failed_inspections: number;
    passed_inspections: number;
    quality_rate: number;
    total_inspections: number;
  };
  work_order_status: {
    cancelled_orders: number;
    completed_orders: number;
    in_progress_orders: number;
    overdue_orders: number;
    pending_orders: number;
    planned_orders: number;
  };
  recent_activities: any[];
}

export interface ProductionSummary {
  total_work_orders: number;
  completed_work_orders: number;
  in_progress_work_orders: number;
  pending_work_orders: number;
  total_tasks: number;
  completed_tasks: number;
  efficiency_rate: number;
}

// Query Parameters
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

export interface DateRangeParams {
  start_date?: string;
  end_date?: string;
}

export interface SearchParams extends PaginationParams, DateRangeParams {
  status?: string;
  user_id?: number;
  project_id?: number;
}


