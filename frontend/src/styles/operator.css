/* 操作员界面专用样式 */

/* 操作员界面整体样式 */
.operator-interface {
  font-size: 16px;
}

/* 大按钮样式 */
.operator-interface .ant-btn-large {
  height: 48px;
  font-size: 18px;
  font-weight: 500;
}

/* 大输入框样式 */
.operator-interface .ant-input-large {
  height: 48px;
  font-size: 16px;
}

.operator-interface .ant-input-number-large {
  height: 48px;
  font-size: 16px;
}

.operator-interface .ant-select-large .ant-select-selector {
  height: 48px;
  font-size: 16px;
}

/* 操作员输入组件样式 */
.operator-input-container {
  margin-bottom: 16px;
}

.operator-input-container .input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.operator-input-container .input-label {
  font-weight: bold;
  font-size: 16px;
}

.operator-input-container .input-mode-toggle {
  display: flex;
  gap: 8px;
}

/* 卡片样式 */
.operator-task-card,
.operator-machine-card,
.operator-quality-card,
.operator-completion-card {
  margin-bottom: 16px;
}

.operator-task-card .ant-card-head-title,
.operator-machine-card .ant-card-head-title,
.operator-quality-card .ant-card-head-title,
.operator-completion-card .ant-card-head-title {
  font-size: 18px;
  font-weight: bold;
}

/* 当前任务卡片样式 */
.current-task-card {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.current-task-card .ant-card-head {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.current-task-card .ant-card-head-title {
  color: white;
}

/* 任务信息卡片样式 */
.task-info-card {
  border: 1px solid #52c41a;
}

.task-info-card .ant-card-head {
  background-color: #f6ffed;
  border-bottom: 1px solid #52c41a;
}

/* 设备信息卡片样式 */
.machine-info-card {
  border: 1px solid #722ed1;
}

.machine-info-card .ant-card-head {
  background-color: #f9f0ff;
  border-bottom: 1px solid #722ed1;
}

/* 零件信息卡片样式 */
.part-info-card {
  border: 1px solid #fa8c16;
}

.part-info-card .ant-card-head {
  background-color: #fff7e6;
  border-bottom: 1px solid #fa8c16;
}

/* 占位符卡片样式 */
.task-info-placeholder,
.machine-info-placeholder,
.part-info-placeholder,
.task-completion-placeholder {
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
}

/* 质量检测项样式 */
.quality-measurements {
  width: 100%;
}

.measurement-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.measurement-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

/* 检验结果样式 */
.inspection-result .ant-radio-button-wrapper {
  height: 48px;
  line-height: 46px;
  font-size: 16px;
  font-weight: 500;
}

.inspection-result .ant-radio-button-wrapper-checked {
  font-weight: bold;
}

/* 数量输入样式 */
.quantity-input,
.defect-quantity-input {
  width: 100%;
}

.quantity-input label,
.defect-quantity-input label {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  display: block;
}

/* 统计卡片样式 */
.daily-stats-card .ant-statistic-title {
  font-size: 14px;
  margin-bottom: 8px;
}

.daily-stats-card .ant-statistic-content {
  font-size: 20px;
  font-weight: bold;
}

/* 我的设备样式 */
.my-machines-card .machine-status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.my-machines-card .machine-status-item:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operator-interface {
    font-size: 14px;
  }
  
  .operator-interface .ant-btn-large {
    height: 44px;
    font-size: 16px;
  }
  
  .operator-interface .ant-input-large {
    height: 44px;
    font-size: 14px;
  }
  
  .operator-input-container .input-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .operator-input-container .input-mode-toggle {
    width: 100%;
    justify-content: center;
  }
}

/* 扫码动画效果 */
.scanning-animation {
  position: relative;
  overflow: hidden;
}

.scanning-animation::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.3), transparent);
  animation: scanning 2s infinite;
}

@keyframes scanning {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 成功状态样式 */
.success-state {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

/* 错误状态样式 */
.error-state {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

/* 警告状态样式 */
.warning-state {
  background-color: #fffbe6;
  border-color: #ffe58f;
  color: #faad14;
}
