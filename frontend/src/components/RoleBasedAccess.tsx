import React from 'react';
import { useAuthStore } from '@/store/auth';

interface RoleBasedAccessProps {
  allowedRoles: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAll?: boolean; // 是否需要拥有所有角色
}

/**
 * 基于角色的访问控制组件
 * 根据用户角色决定是否显示子组件
 */
const RoleBasedAccess: React.FC<RoleBasedAccessProps> = ({
  allowedRoles,
  children,
  fallback = null,
  requireAll = false
}) => {
  const { user } = useAuthStore();
  
  if (!user || !user.roles) {
    return <>{fallback}</>;
  }

  const userRoles = user.roles;
  
  // 检查权限
  const hasAccess = requireAll 
    ? allowedRoles.every(role => userRoles.includes(role))
    : allowedRoles.some(role => userRoles.includes(role));

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default RoleBasedAccess;
