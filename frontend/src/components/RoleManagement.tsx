import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Popconfirm,
  Alert,
  Select,
  Typography,
  Divider,
  List,
  Card
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { Role, CreateRoleRequest, RoleDependencyInfo } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface RoleManagementProps {
  onRoleChange?: () => void;
}

const RoleManagement: React.FC<RoleManagementProps> = ({ onRoleChange }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deletingRole, setDeletingRole] = useState<Role | null>(null);
  const [dependencyInfo, setDependencyInfo] = useState<RoleDependencyInfo | null>(null);
  const [replacementRoleId, setReplacementRoleId] = useState<number | undefined>();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const { data: roles = [], isLoading } = useQuery(
    'roles',
    () => apiClient.getRoles()
  );

  const createMutation = useMutation(
    (data: CreateRoleRequest) => apiClient.createRole(data),
    {
      onSuccess: () => {
        success('角色创建成功');
        queryClient.invalidateQueries('roles');
        setIsModalVisible(false);
        form.resetFields();
        onRoleChange?.();
      },
      onError: () => {
        error('角色创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateRoleRequest> }) =>
      apiClient.updateRole(id, data),
    {
      onSuccess: () => {
        success('角色更新成功');
        queryClient.invalidateQueries('roles');
        setIsModalVisible(false);
        form.resetFields();
        onRoleChange?.();
      },
      onError: () => {
        error('角色更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    ({ id, replacementId }: { id: number; replacementId?: number }) =>
      apiClient.deleteRole(id, replacementId),
    {
      onSuccess: () => {
        success('角色删除成功');
        queryClient.invalidateQueries('roles');
        setDeleteModalVisible(false);
        setDeletingRole(null);
        setDependencyInfo(null);
        setReplacementRoleId(undefined);
        onRoleChange?.();
      },
      onError: () => {
        error('角色删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingRole(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setIsModalVisible(true);
    form.setFieldsValue({
      role_name: role.role_name,
    });
  };

  const handleDelete = async (role: Role) => {
    try {
      const deps = await apiClient.checkRoleDependencies(role.id);
      setDeletingRole(role);
      setDependencyInfo(deps);
      setDeleteModalVisible(true);
    } catch (err) {
      error('检查角色依赖失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingRole) {
        updateMutation.mutate({ id: editingRole.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleConfirmDelete = () => {
    if (!deletingRole) return;
    
    if (dependencyInfo && !dependencyInfo.can_delete && !replacementRoleId) {
      error('请选择替换角色');
      return;
    }

    deleteMutation.mutate({
      id: deletingRole.id,
      replacementId: replacementRoleId,
    });
  };

  // 角色名称中文映射
  const getRoleDisplayName = (roleName: string) => {
    const roleNameMap: Record<string, string> = {
      'admin': '系统管理员',
      'process_engineer': '工艺工程师',
      'planner': '生产计划员',
      'operator': '操作员',
      'quality_inspector': '质量检验员',
    };
    return roleNameMap[roleName] || roleName;
  };

  const getSystemRoleTag = (roleName: string) => {
    const systemRoles = ['admin', 'process_engineer', 'planner', 'operator', 'quality_inspector'];
    return systemRoles.includes(roleName) ? (
      <Tag color="red" icon={<SafetyOutlined />}>系统角色</Tag>
    ) : (
      <Tag color="blue">自定义角色</Tag>
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'role_name',
      key: 'role_name',
      render: (text: string) => <strong>{getRoleDisplayName(text)}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'role_name',
      key: 'type',
      render: (roleName: string) => getSystemRoleTag(roleName),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Role) => {
        const isSystemRole = ['admin', 'process_engineer', 'planner', 'operator', 'quality_inspector'].includes(record.role_name);
        
        return (
          <Space>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={isSystemRole}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
              disabled={isSystemRole}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          新建角色
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={roles}
        rowKey="id"
        loading={isLoading}
        pagination={false}
      />

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新建角色'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingRole(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="role_name"
            label="角色名称"
            rules={[
              { required: true, message: '请输入角色名称' },
              { min: 2, message: '角色名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入角色描述（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 安全删除模态框 */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            删除角色确认
          </Space>
        }
        open={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeletingRole(null);
          setDependencyInfo(null);
          setReplacementRoleId(undefined);
        }}
        confirmLoading={deleteMutation.isLoading}
        width={600}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        {dependencyInfo && (
          <div>
            <Alert
              message="警告"
              description={`删除角色 "${getRoleDisplayName(dependencyInfo.role_name)}" 将影响 ${dependencyInfo.user_count} 个用户`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {dependencyInfo.affected_users.length > 0 && (
              <Card size="small" title="受影响的用户" style={{ marginBottom: 16 }}>
                <List
                  size="small"
                  dataSource={dependencyInfo.affected_users}
                  renderItem={(user) => (
                    <List.Item>
                      <Space>
                        <UserOutlined />
                        <Text>{user.username}</Text>
                        {user.full_name && <Text type="secondary">({user.full_name})</Text>}
                      </Space>
                    </List.Item>
                  )}
                />
              </Card>
            )}

            {!dependencyInfo.can_delete && (
              <>
                <Divider />
                <Form.Item
                  label="选择替换角色"
                  required
                >
                  <Select
                    placeholder="请选择替换角色"
                    value={replacementRoleId}
                    onChange={setReplacementRoleId}
                    options={roles
                      .filter(role => role.id !== dependencyInfo.role_id)
                      .map(role => ({
                        value: role.id,
                        label: getRoleDisplayName(role.role_name),
                      }))}
                  />
                </Form.Item>
                <Alert
                  message="必须选择替换角色"
                  description="由于该角色被用户使用，删除前必须指定一个替换角色来保证用户权限的连续性。"
                  type="info"
                  showIcon
                />
              </>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default RoleManagement;
