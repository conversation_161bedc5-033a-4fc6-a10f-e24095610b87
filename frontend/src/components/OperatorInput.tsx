import React, { useState } from 'react';
import { Button, Input, Space, message } from 'antd';
import { ScanOutlined, EditOutlined } from '@ant-design/icons';

interface OperatorInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  scanEnabled?: boolean;
  inputType?: 'workOrder' | 'partNumber' | 'batchNumber' | 'serialNumber';
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
}

// 输入验证规则
const INPUT_VALIDATION = {
  workOrder: {
    pattern: /^WO\d{8}$/,
    message: '工单号格式：WO + 8位数字'
  },
  partNumber: {
    pattern: /^P\d{6}$/,
    message: '零件号格式：P + 6位数字'
  },
  batchNumber: {
    pattern: /^B\d{8}$/,
    message: '批次号格式：B + 8位数字'
  },
  serialNumber: {
    pattern: /^S\d{10}$/,
    message: '序列号格式：S + 10位数字'
  }
};

const validateInput = (value: string, type?: string): boolean => {
  if (!type || !value) return true;
  const rule = INPUT_VALIDATION[type as keyof typeof INPUT_VALIDATION];
  return rule ? rule.pattern.test(value) : true;
};

/**
 * 操作员输入组件 - 支持扫码和手动输入
 */
const OperatorInput: React.FC<OperatorInputProps> = ({
  label,
  value,
  onChange,
  scanEnabled = true,
  inputType,
  placeholder,
  size = 'large'
}) => {
  const [inputMode, setInputMode] = useState<'scan' | 'manual'>('scan');
  const [isScanning, setIsScanning] = useState(false);

  const handleScanClick = () => {
    if (!scanEnabled) return;
    
    setIsScanning(true);
    setInputMode('scan');
    
    // 模拟扫码过程
    setTimeout(() => {
      setIsScanning(false);
      // 这里应该集成实际的扫码功能
      message.info('请使用扫码设备扫描条码');
    }, 1000);
  };

  const handleManualInput = (inputValue: string) => {
    onChange(inputValue);
    
    // 验证输入格式
    if (inputType && inputValue && !validateInput(inputValue, inputType)) {
      const rule = INPUT_VALIDATION[inputType];
      message.warning(rule.message);
    }
  };

  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    if (inputMode === 'scan') return `请扫描${label}`;
    return `请输入${label}`;
  };

  return (
    <div className="operator-input-container" style={{ marginBottom: 16 }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 8 
      }}>
        <span style={{ fontWeight: 'bold', fontSize: 16 }}>{label}</span>
        <Space>
          <Button 
            type={inputMode === 'scan' ? 'primary' : 'default'}
            icon={<ScanOutlined />}
            onClick={handleScanClick}
            disabled={!scanEnabled}
            loading={isScanning}
            size={size}
          >
            扫码
          </Button>
          <Button 
            type={inputMode === 'manual' ? 'primary' : 'default'}
            icon={<EditOutlined />}
            onClick={() => setInputMode('manual')}
            size={size}
          >
            手动
          </Button>
        </Space>
      </div>
      
      <Input
        value={value}
        onChange={(e) => handleManualInput(e.target.value)}
        placeholder={getPlaceholder()}
        size={size}
        disabled={inputMode === 'scan' && isScanning}
        style={{ fontSize: 16 }}
      />
      
      {inputType && (
        <div style={{ 
          fontSize: 12, 
          color: '#666', 
          marginTop: 4 
        }}>
          {INPUT_VALIDATION[inputType]?.message}
        </div>
      )}
    </div>
  );
};

export default OperatorInput;
