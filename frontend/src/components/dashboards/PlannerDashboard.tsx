import React from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Progress, Timeline } from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  BarChartOutlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

const { Title, Text } = Typography;

/**
 * 生产计划员专用仪表板
 * 专注于生产计划、设备利用率和交期管理
 */
const PlannerDashboard: React.FC = () => {
  // 模拟计划数据
  const planningData = {
    todayStats: {
      totalPlans: 45,
      onSchedule: 38,
      delayed: 5,
      completed: 32,
      equipmentUtilization: 87.5
    },
    weeklyProgress: [
      { day: '周一', planned: 40, completed: 38, utilization: 85 },
      { day: '周二', planned: 45, completed: 42, utilization: 88 },
      { day: '周三', planned: 42, planned: 39, utilization: 82 },
      { day: '周四', planned: 48, completed: 45, utilization: 90 },
      { day: '周五', planned: 45, completed: 32, utilization: 87 },
    ],
    equipmentStatus: [
      { name: 'CNC设备', total: 12, running: 10, idle: 1, maintenance: 1, utilization: 83.3 },
      { name: '铣床', total: 8, running: 7, idle: 1, maintenance: 0, utilization: 87.5 },
      { name: '车床', total: 6, running: 5, idle: 1, maintenance: 0, utilization: 83.3 },
      { name: '磨床', total: 4, running: 3, idle: 1, maintenance: 0, utilization: 75.0 },
    ],
    urgentTasks: [
      {
        key: '1',
        workOrder: 'WO20241206001',
        partName: '齿轮轴',
        dueDate: '2024-12-06 18:00',
        progress: 75,
        status: 'on_track',
        assignedTo: '张三'
      },
      {
        key: '2',
        workOrder: 'WO20241206002',
        partName: '轴承座',
        dueDate: '2024-12-06 20:00',
        progress: 45,
        status: 'at_risk',
        assignedTo: '李四'
      },
      {
        key: '3',
        workOrder: 'WO20241206003',
        partName: '连接器',
        dueDate: '2024-12-07 10:00',
        progress: 20,
        status: 'delayed',
        assignedTo: '王五'
      },
    ],
    upcomingMilestones: [
      { time: '16:00', event: '批次*********完成', status: 'pending' },
      { time: '17:30', event: '设备CNC-001维护', status: 'scheduled' },
      { time: '18:00', event: '工单WO001交付', status: 'critical' },
      { time: '19:00', event: '夜班交接', status: 'normal' },
    ]
  };

  const taskColumns = [
    {
      title: '工单号',
      dataIndex: 'workOrder',
      key: 'workOrder',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '零件名称',
      dataIndex: 'partName',
      key: 'partName',
    },
    {
      title: '交期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string) => <Text>{date}</Text>
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress 
          percent={progress} 
          size="small" 
          strokeColor={progress >= 80 ? '#52c41a' : progress >= 50 ? '#faad14' : '#ff4d4f'}
        />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          on_track: { color: 'green', text: '正常' },
          at_risk: { color: 'orange', text: '风险' },
          delayed: { color: 'red', text: '延期' }
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
    },
  ];

  const equipmentColumns = [
    {
      title: '设备类型',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '总数',
      dataIndex: 'total',
      key: 'total',
    },
    {
      title: '运行中',
      dataIndex: 'running',
      key: 'running',
      render: (count: number) => <Text style={{ color: '#52c41a' }}>{count}</Text>
    },
    {
      title: '空闲',
      dataIndex: 'idle',
      key: 'idle',
      render: (count: number) => <Text style={{ color: '#faad14' }}>{count}</Text>
    },
    {
      title: '维护中',
      dataIndex: 'maintenance',
      key: 'maintenance',
      render: (count: number) => <Text style={{ color: '#ff4d4f' }}>{count}</Text>
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small" 
          strokeColor={rate >= 85 ? '#52c41a' : rate >= 70 ? '#faad14' : '#ff4d4f'}
        />
      )
    },
  ];

  return (
    <div>
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          生产计划仪表板
        </Title>
        <Text type="secondary">
          今日计划执行概况 | 设备利用率目标：≥85%
        </Text>
      </div>

      {/* 今日统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总计划数"
              value={planningData.todayStats.totalPlans}
              suffix="个"
              prefix={<CalendarOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="按时进行"
              value={planningData.todayStats.onSchedule}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="延期任务"
              value={planningData.todayStats.delayed}
              suffix="个"
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="设备利用率"
              value={planningData.todayStats.equipmentUtilization}
              suffix="%"
              prefix={<SettingOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="本周计划执行情况">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={planningData.weeklyProgress}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="planned" fill="#91d5ff" name="计划任务" />
                <Bar dataKey="completed" fill="#1890ff" name="完成任务" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="今日重要节点">
            <Timeline
              items={planningData.upcomingMilestones.map(milestone => ({
                color: milestone.status === 'critical' ? 'red' : 
                       milestone.status === 'scheduled' ? 'blue' : 'green',
                children: (
                  <div>
                    <Text strong>{milestone.time}</Text>
                    <br />
                    <Text>{milestone.event}</Text>
                  </div>
                )
              }))}
            />
          </Card>
        </Col>
      </Row>

      {/* 紧急任务和设备状态 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={14}>
          <Card 
            title="紧急任务跟踪"
            extra={<Text type="secondary">{planningData.urgentTasks.length} 个紧急任务</Text>}
          >
            <Table
              columns={taskColumns}
              dataSource={planningData.urgentTasks}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={10}>
          <Card title="设备利用率概览">
            <Table
              columns={equipmentColumns}
              dataSource={planningData.equipmentStatus}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PlannerDashboard;
