import React, { useState } from 'react';
import { Card, Space, Button, InputNumber, message, Row, Col, Descriptions, Alert } from 'antd';
import { CheckCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import OperatorInput from '@/components/OperatorInput';

/**
 * 任务确认组件
 * 支持扫码和手动输入工单号、零件号、批次号等信息
 */
const TaskConfirmation: React.FC = () => {
  const [workOrderNumber, setWorkOrderNumber] = useState('');
  const [partNumber, setPartNumber] = useState('');
  const [batchNumber, setBatchNumber] = useState('');
  const [quantity, setQuantity] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [taskInfo, setTaskInfo] = useState<any>(null);

  const handleLookupTask = async () => {
    if (!workOrderNumber) {
      message.warning('请输入工单号');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用查询任务信息
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟返回的任务信息
      const mockTaskInfo = {
        workOrderNumber,
        partName: '齿轮轴',
        partNumber: 'P123456',
        specification: '直径50mm，长度200mm',
        plannedQuantity: 100,
        completedQuantity: 25,
        remainingQuantity: 75,
        priority: 'high',
        dueDate: '2024-12-06 18:00',
        processSteps: [
          { step: 1, name: '粗加工', status: 'completed' },
          { step: 2, name: '精加工', status: 'in_progress' },
          { step: 3, name: '质量检验', status: 'pending' },
        ]
      };
      
      setTaskInfo(mockTaskInfo);
      setPartNumber(mockTaskInfo.partNumber);
      message.success('任务信息加载成功');
    } catch (error) {
      message.error('查询任务信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTaskConfirm = async () => {
    if (!workOrderNumber || !partNumber || !batchNumber || !quantity) {
      message.warning('请填写完整信息');
      return;
    }

    if (quantity <= 0) {
      message.warning('加工数量必须大于0');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用确认任务
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      message.success('任务确认成功，开始生产！');
      
      // 重置表单
      setWorkOrderNumber('');
      setPartNumber('');
      setBatchNumber('');
      setQuantity(0);
      setTaskInfo(null);
    } catch (error) {
      message.error('任务确认失败');
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = () => {
    return workOrderNumber && partNumber && batchNumber && quantity > 0;
  };

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="任务信息录入" className="operator-task-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              
              {/* 工单号输入 */}
              <OperatorInput
                label="工单号"
                value={workOrderNumber}
                onChange={setWorkOrderNumber}
                inputType="workOrder"
                placeholder="扫描或输入工单号"
              />
              
              <Button 
                type="default" 
                onClick={handleLookupTask}
                loading={isLoading}
                disabled={!workOrderNumber}
                style={{ width: '100%' }}
                size="large"
              >
                查询任务信息
              </Button>

              {/* 零件号输入 */}
              <OperatorInput
                label="零件号"
                value={partNumber}
                onChange={setPartNumber}
                inputType="partNumber"
                placeholder="扫描或输入零件号"
              />
              
              {/* 批次号输入 */}
              <OperatorInput
                label="批次号"
                value={batchNumber}
                onChange={setBatchNumber}
                inputType="batchNumber"
                placeholder="扫描或输入批次号"
              />
              
              {/* 数量输入 */}
              <div className="quantity-input">
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  本次加工数量
                </label>
                <InputNumber
                  value={quantity}
                  onChange={(value) => setQuantity(value || 0)}
                  min={1}
                  max={taskInfo?.remainingQuantity || 999}
                  style={{ width: '100%' }}
                  size="large"
                  placeholder="请输入加工数量"
                />
                {taskInfo && (
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    剩余数量：{taskInfo.remainingQuantity} 件
                  </div>
                )}
              </div>
              
              {/* 确认按钮 */}
              <Button 
                type="primary" 
                size="large" 
                block
                onClick={handleTaskConfirm}
                disabled={!isFormValid()}
                loading={isLoading}
                icon={<PlayCircleOutlined />}
              >
                确认开始任务
              </Button>
              
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          {taskInfo ? (
            <Card title="任务详情" className="task-info-card">
              <Alert
                message="任务信息已加载"
                description="请确认任务信息无误后开始生产"
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Descriptions column={1} size="small">
                <Descriptions.Item label="工单号">
                  <strong>{taskInfo.workOrderNumber}</strong>
                </Descriptions.Item>
                <Descriptions.Item label="零件名称">
                  {taskInfo.partName}
                </Descriptions.Item>
                <Descriptions.Item label="零件号">
                  {taskInfo.partNumber}
                </Descriptions.Item>
                <Descriptions.Item label="规格">
                  {taskInfo.specification}
                </Descriptions.Item>
                <Descriptions.Item label="计划数量">
                  {taskInfo.plannedQuantity} 件
                </Descriptions.Item>
                <Descriptions.Item label="已完成">
                  {taskInfo.completedQuantity} 件
                </Descriptions.Item>
                <Descriptions.Item label="剩余数量">
                  <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                    {taskInfo.remainingQuantity} 件
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="交期">
                  <span style={{ color: taskInfo.priority === 'high' ? '#ff4d4f' : '#52c41a' }}>
                    {taskInfo.dueDate}
                  </span>
                </Descriptions.Item>
              </Descriptions>

              <div style={{ marginTop: 16 }}>
                <h4>工艺步骤</h4>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {taskInfo.processSteps.map((step: any) => (
                    <div key={step.step} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      padding: '8px 0',
                      borderBottom: '1px solid #f0f0f0'
                    }}>
                      <span>步骤{step.step}: {step.name}</span>
                      <span style={{ 
                        color: step.status === 'completed' ? '#52c41a' : 
                               step.status === 'in_progress' ? '#1890ff' : '#666'
                      }}>
                        {step.status === 'completed' ? '已完成' : 
                         step.status === 'in_progress' ? '进行中' : '待开始'}
                      </span>
                    </div>
                  ))}
                </Space>
              </div>
            </Card>
          ) : (
            <Card title="任务详情" className="task-info-placeholder">
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 0',
                color: '#999'
              }}>
                <CheckCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>请先输入工单号并查询任务信息</p>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default TaskConfirmation;
