import React, { useState } from 'react';
import { Card, Space, Button, InputNumber, Input, message, Row, Col, Descriptions, Alert, Progress } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import OperatorInput from '@/components/OperatorInput';

const { TextArea } = Input;

/**
 * 任务完工确认组件
 * 确认任务完成，录入完成数量和备注
 */
const TaskCompletion: React.FC = () => {
  const [workOrderNumber, setWorkOrderNumber] = useState('');
  const [batchNumber, setBatchNumber] = useState('');
  const [completedQuantity, setCompletedQuantity] = useState<number>(0);
  const [defectQuantity, setDefectQuantity] = useState<number>(0);
  const [completionNotes, setCompletionNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [taskInfo, setTaskInfo] = useState<any>(null);

  const handleLookupTask = async () => {
    if (!workOrderNumber) {
      message.warning('请输入工单号');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用查询任务信息
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟返回的任务信息
      const mockTaskInfo = {
        workOrderNumber,
        partName: '齿轮轴',
        partNumber: 'P123456',
        plannedQuantity: 100,
        completedQuantity: 75,
        remainingQuantity: 25,
        currentBatch: 'B20241206001',
        operator: '张三',
        startTime: '2024-12-06 08:00',
        estimatedEndTime: '2024-12-06 18:00',
        actualWorkTime: 8.5,
        processStep: '精加工',
        machineId: 'CNC-001',
        priority: 'high'
      };
      
      setTaskInfo(mockTaskInfo);
      setBatchNumber(mockTaskInfo.currentBatch);
      message.success('任务信息加载成功');
    } catch (error) {
      message.error('查询任务信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTaskComplete = async () => {
    if (!workOrderNumber || !batchNumber || completedQuantity <= 0) {
      message.warning('请填写完整信息');
      return;
    }

    if (completedQuantity > (taskInfo?.remainingQuantity || 0)) {
      message.warning('完成数量不能超过剩余数量');
      return;
    }

    if (defectQuantity > completedQuantity) {
      message.warning('不良品数量不能超过完成数量');
      return;
    }

    setIsLoading(true);
    try {
      // 模拟API调用确认完工
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const qualityRate = ((completedQuantity - defectQuantity) / completedQuantity * 100).toFixed(1);
      message.success(`任务完工确认成功！完成数量：${completedQuantity}件，合格率：${qualityRate}%`);
      
      // 重置表单
      setWorkOrderNumber('');
      setBatchNumber('');
      setCompletedQuantity(0);
      setDefectQuantity(0);
      setCompletionNotes('');
      setTaskInfo(null);
    } catch (error) {
      message.error('任务完工确认失败');
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = () => {
    return workOrderNumber && batchNumber && completedQuantity > 0 && 
           completedQuantity <= (taskInfo?.remainingQuantity || 0) &&
           defectQuantity <= completedQuantity;
  };

  const getProgressPercent = () => {
    if (!taskInfo) return 0;
    return Math.round(((taskInfo.completedQuantity + completedQuantity) / taskInfo.plannedQuantity) * 100);
  };

  const getQualityRate = () => {
    if (completedQuantity === 0) return 100;
    return ((completedQuantity - defectQuantity) / completedQuantity * 100).toFixed(1);
  };

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="任务完工确认" className="operator-completion-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              
              {/* 工单号输入 */}
              <OperatorInput
                label="工单号"
                value={workOrderNumber}
                onChange={setWorkOrderNumber}
                inputType="workOrder"
                placeholder="扫描或输入工单号"
              />
              
              <Button 
                type="default" 
                onClick={handleLookupTask}
                loading={isLoading}
                disabled={!workOrderNumber}
                style={{ width: '100%' }}
                size="large"
              >
                查询任务信息
              </Button>

              {/* 批次号输入 */}
              <OperatorInput
                label="批次号"
                value={batchNumber}
                onChange={setBatchNumber}
                inputType="batchNumber"
                placeholder="扫描或输入批次号"
              />
              
              {/* 完成数量 */}
              <div className="quantity-input">
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  完成数量
                </label>
                <InputNumber
                  value={completedQuantity}
                  onChange={(value) => setCompletedQuantity(value || 0)}
                  min={1}
                  max={taskInfo?.remainingQuantity || 999}
                  style={{ width: '100%' }}
                  size="large"
                  placeholder="请输入完成数量"
                  disabled={!taskInfo}
                />
                {taskInfo && (
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    剩余数量：{taskInfo.remainingQuantity} 件
                  </div>
                )}
              </div>

              {/* 不良品数量 */}
              <div className="defect-quantity-input">
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  不良品数量
                </label>
                <InputNumber
                  value={defectQuantity}
                  onChange={(value) => setDefectQuantity(value || 0)}
                  min={0}
                  max={completedQuantity}
                  style={{ width: '100%' }}
                  size="large"
                  placeholder="请输入不良品数量"
                  disabled={!taskInfo || completedQuantity === 0}
                />
                {completedQuantity > 0 && (
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    合格率：{getQualityRate()}%
                  </div>
                )}
              </div>
              
              {/* 完工备注 */}
              <div>
                <label style={{ fontWeight: 'bold', fontSize: 16, marginBottom: 8, display: 'block' }}>
                  完工备注（可选）
                </label>
                <TextArea
                  placeholder="请输入完工备注，如遇到的问题、改进建议等"
                  value={completionNotes}
                  onChange={(e) => setCompletionNotes(e.target.value)}
                  rows={3}
                  disabled={!taskInfo}
                />
              </div>
              
              {/* 确认按钮 */}
              <Button 
                type="primary" 
                size="large" 
                block
                onClick={handleTaskComplete}
                disabled={!isFormValid()}
                loading={isLoading}
                icon={<CheckCircleOutlined />}
              >
                确认完工
              </Button>
              
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          {taskInfo ? (
            <Card title="任务详情" className="task-completion-info">
              <Alert
                message="任务信息已加载"
                description="请确认完成数量并提交完工信息"
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Descriptions column={1} size="small">
                <Descriptions.Item label="工单号">
                  <strong>{taskInfo.workOrderNumber}</strong>
                </Descriptions.Item>
                <Descriptions.Item label="零件名称">
                  {taskInfo.partName} - {taskInfo.partNumber}
                </Descriptions.Item>
                <Descriptions.Item label="计划数量">
                  {taskInfo.plannedQuantity} 件
                </Descriptions.Item>
                <Descriptions.Item label="已完成">
                  {taskInfo.completedQuantity} 件
                </Descriptions.Item>
                <Descriptions.Item label="剩余数量">
                  <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                    {taskInfo.remainingQuantity} 件
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="当前批次">
                  {taskInfo.currentBatch}
                </Descriptions.Item>
                <Descriptions.Item label="工艺步骤">
                  {taskInfo.processStep}
                </Descriptions.Item>
                <Descriptions.Item label="加工设备">
                  {taskInfo.machineId}
                </Descriptions.Item>
                <Descriptions.Item label="开始时间">
                  {taskInfo.startTime}
                </Descriptions.Item>
                <Descriptions.Item label="实际工时">
                  {taskInfo.actualWorkTime} 小时
                </Descriptions.Item>
              </Descriptions>

              {/* 进度显示 */}
              <div style={{ marginTop: 16 }}>
                <h4>完成进度</h4>
                <Progress 
                  percent={getProgressPercent()} 
                  strokeColor={getProgressPercent() >= 100 ? '#52c41a' : '#1890ff'}
                  format={(percent) => `${percent}%`}
                />
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  marginTop: 8,
                  fontSize: 12,
                  color: '#666'
                }}>
                  <span>已完成：{taskInfo.completedQuantity + completedQuantity} 件</span>
                  <span>总计：{taskInfo.plannedQuantity} 件</span>
                </div>
              </div>

              {/* 质量统计 */}
              {completedQuantity > 0 && (
                <div style={{ 
                  marginTop: 16, 
                  padding: 12, 
                  backgroundColor: '#f6ffed', 
                  border: '1px solid #b7eb8f',
                  borderRadius: 6 
                }}>
                  <strong>本次完工统计：</strong>
                  <br />
                  <div style={{ marginTop: 8 }}>
                    <span>完成数量：<strong>{completedQuantity}</strong> 件</span>
                    <br />
                    <span>不良品：<strong>{defectQuantity}</strong> 件</span>
                    <br />
                    <span>合格率：<strong style={{ color: '#52c41a' }}>{getQualityRate()}%</strong></span>
                  </div>
                </div>
              )}
            </Card>
          ) : (
            <Card title="任务详情" className="task-completion-placeholder">
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 0',
                color: '#999'
              }}>
                <ClockCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>请先输入工单号并查询任务信息</p>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default TaskCompletion;
