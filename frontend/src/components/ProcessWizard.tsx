import React, { useState } from 'react';
import { Modal, Steps, Form, Select, Card, Typography, Row, Col, Button, Space, Divider } from 'antd';
import { CheckCircleOutlined, ToolOutlined, PartitionOutlined } from '@ant-design/icons';
import ProcessFlowChart from './ProcessFlowChart';
import type { Part, Machine, SkillGroup, RoutingStep } from '@/types/api';

const { Title, Text } = Typography;
const { Step } = Steps;

interface ProcessWizardProps {
  visible: boolean;
  onCancel: () => void;
  onFinish: (partId: number, steps: Omit<RoutingStep, 'id'>[]) => void;
  parts: Part[];
  machines: Machine[];
  skillGroups: SkillGroup[];
}

const ProcessWizard: React.FC<ProcessWizardProps> = ({
  visible,
  onCancel,
  onFinish,
  parts,
  machines,
  skillGroups,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [processSteps, setProcessSteps] = useState<Omit<RoutingStep, 'id'>[]>([]);
  const [form] = Form.useForm();

  const handlePartSelect = (partId: number) => {
    const part = parts.find(p => p.id === partId);
    setSelectedPart(part || null);
    setCurrentStep(1);
  };

  const handleStepAdd = (stepData: Omit<RoutingStep, 'id'>) => {
    setProcessSteps(prev => [...prev, stepData]);
  };

  const handleStepEdit = (stepIndex: number, stepData: Partial<RoutingStep>) => {
    setProcessSteps(prev => 
      prev.map((step, index) => 
        index === stepIndex ? { ...step, ...stepData } : step
      )
    );
  };

  const handleStepDelete = (stepIndex: number) => {
    setProcessSteps(prev => prev.filter((_, index) => index !== stepIndex));
  };

  const handleFinish = () => {
    if (selectedPart && processSteps.length > 0) {
      onFinish(selectedPart.id, processSteps);
      handleReset();
    }
  };

  const handleReset = () => {
    setCurrentStep(0);
    setSelectedPart(null);
    setProcessSteps([]);
    form.resetFields();
  };

  const handleCancel = () => {
    handleReset();
    onCancel();
  };

  const steps = [
    {
      title: '选择零件',
      icon: <PartitionOutlined />,
      description: '选择要创建工艺的零件',
    },
    {
      title: '设计工艺',
      icon: <ToolOutlined />,
      description: '设计工艺流程和步骤',
    },
    {
      title: '确认创建',
      icon: <CheckCircleOutlined />,
      description: '确认并创建工艺路由',
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div style={{ padding: '24px 0' }}>
            <Title level={4}>选择零件</Title>
            <Text type="secondary">请选择要创建工艺路由的零件</Text>
            <div style={{ marginTop: 16 }}>
              <Select
                placeholder="搜索并选择零件"
                style={{ width: '100%' }}
                showSearch
                optionFilterProp="children"
                size="large"
                onChange={handlePartSelect}
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {parts.map(part => (
                  <Select.Option key={part.id} value={part.id}>
                    <div>
                      <strong>{part.part_number}</strong>
                      {part.part_name && <span> - {part.part_name}</span>}
                      <span style={{ color: '#999', marginLeft: 8 }}>({part.version})</span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        );

      case 1:
        return (
          <div style={{ padding: '24px 0' }}>
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>设计工艺流程</Title>
              <Text type="secondary">
                为零件 <strong>{selectedPart?.part_number}</strong> 设计工艺流程
              </Text>
            </div>
            
            {selectedPart && (
              <Card style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Text strong>零件编号:</Text>
                    <div>{selectedPart.part_number}</div>
                  </Col>
                  <Col span={8}>
                    <Text strong>零件名称:</Text>
                    <div>{selectedPart.part_name || '未命名'}</div>
                  </Col>
                  <Col span={8}>
                    <Text strong>版本:</Text>
                    <div>{selectedPart.version}</div>
                  </Col>
                </Row>
                {selectedPart.specifications && (
                  <div style={{ marginTop: 8 }}>
                    <Text strong>规格说明:</Text>
                    <div>{selectedPart.specifications}</div>
                  </div>
                )}
              </Card>
            )}

            <ProcessFlowChart
              steps={processSteps.map((step, index) => ({ ...step, id: index }))}
              onStepAdd={handleStepAdd}
              onStepEdit={(stepId, stepData) => handleStepEdit(stepId, stepData)}
              onStepDelete={handleStepDelete}
              machines={machines}
              skillGroups={skillGroups}
              editable={true}
            />

            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Space>
                <Button onClick={() => setCurrentStep(0)}>上一步</Button>
                <Button 
                  type="primary" 
                  onClick={() => setCurrentStep(2)}
                  disabled={processSteps.length === 0}
                >
                  下一步
                </Button>
              </Space>
            </div>
          </div>
        );

      case 2:
        return (
          <div style={{ padding: '24px 0' }}>
            <Title level={4}>确认创建工艺路由</Title>
            <Text type="secondary">请确认以下工艺路由信息</Text>
            
            <Card style={{ margin: '16px 0' }}>
              <Title level={5}>零件信息</Title>
              <Row gutter={16}>
                <Col span={8}>
                  <Text strong>零件编号:</Text>
                  <div>{selectedPart?.part_number}</div>
                </Col>
                <Col span={8}>
                  <Text strong>零件名称:</Text>
                  <div>{selectedPart?.part_name || '未命名'}</div>
                </Col>
                <Col span={8}>
                  <Text strong>版本:</Text>
                  <div>{selectedPart?.version}</div>
                </Col>
              </Row>
            </Card>

            <Card>
              <Title level={5}>工艺步骤 ({processSteps.length} 个工序)</Title>
              {processSteps.map((step, index) => (
                <div key={index} style={{ 
                  padding: '12px', 
                  border: '1px solid #f0f0f0', 
                  borderRadius: '6px',
                  marginBottom: '8px'
                }}>
                  <Row gutter={16}>
                    <Col span={4}>
                      <Text strong>工序 {step.step_number}</Text>
                    </Col>
                    <Col span={6}>
                      <Text>{step.process_name}</Text>
                    </Col>
                    <Col span={8}>
                      <Text type="secondary">
                        {step.work_instructions || '无工作指导'}
                      </Text>
                    </Col>
                    <Col span={6}>
                      <Text type="secondary">
                        {step.standard_hours ? `${step.standard_hours}h` : '无时间预算'}
                      </Text>
                    </Col>
                  </Row>
                </div>
              ))}
            </Card>

            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Space>
                <Button onClick={() => setCurrentStep(1)}>上一步</Button>
                <Button type="primary" onClick={handleFinish}>
                  创建工艺路由
                </Button>
              </Space>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title="工艺路由创建向导"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
      {renderStepContent()}
    </Modal>
  );
};

export default ProcessWizard;
