import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Alert,
  Select,
  Typography,
  Divider,
  List,
  Card,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ToolOutlined,
  SafetyOutlined,
  ScheduleOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { SkillGroup, CreateSkillGroupRequest, SkillGroupDependencyInfo } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface SkillGroupManagementProps {
  onSkillGroupChange?: () => void;
}

const SkillGroupManagement: React.FC<SkillGroupManagementProps> = ({ onSkillGroupChange }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingSkillGroup, setEditingSkillGroup] = useState<SkillGroup | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deletingSkillGroup, setDeletingSkillGroup] = useState<SkillGroup | null>(null);
  const [dependencyInfo, setDependencyInfo] = useState<SkillGroupDependencyInfo | null>(null);
  const [replacementSkillGroupId, setReplacementSkillGroupId] = useState<number | undefined>();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  const { data: skillGroups = [], isLoading } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  const createMutation = useMutation(
    (data: CreateSkillGroupRequest) => apiClient.createSkillGroup(data),
    {
      onSuccess: () => {
        success('技能组创建成功');
        queryClient.invalidateQueries('skill-groups');
        setIsModalVisible(false);
        form.resetFields();
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组创建失败');
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateSkillGroupRequest> }) =>
      apiClient.updateSkillGroup(id, data),
    {
      onSuccess: () => {
        success('技能组更新成功');
        queryClient.invalidateQueries('skill-groups');
        setIsModalVisible(false);
        form.resetFields();
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    ({ id, replacementId }: { id: number; replacementId?: number }) =>
      apiClient.deleteSkillGroup(id, replacementId),
    {
      onSuccess: () => {
        success('技能组删除成功');
        queryClient.invalidateQueries('skill-groups');
        setDeleteModalVisible(false);
        setDeletingSkillGroup(null);
        setDependencyInfo(null);
        setReplacementSkillGroupId(undefined);
        onSkillGroupChange?.();
      },
      onError: () => {
        error('技能组删除失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingSkillGroup(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (skillGroup: SkillGroup) => {
    setEditingSkillGroup(skillGroup);
    setIsModalVisible(true);
    form.setFieldsValue({
      group_name: skillGroup.group_name,
    });
  };

  const handleDelete = async (skillGroup: SkillGroup) => {
    try {
      const deps = await apiClient.checkSkillGroupDependencies(skillGroup.id);
      setDeletingSkillGroup(skillGroup);
      setDependencyInfo(deps);
      setDeleteModalVisible(true);
    } catch (err) {
      error('检查技能组依赖失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingSkillGroup) {
        updateMutation.mutate({ id: editingSkillGroup.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const handleConfirmDelete = () => {
    if (!deletingSkillGroup) return;
    
    if (dependencyInfo && !dependencyInfo.can_delete && !replacementSkillGroupId) {
      error('请选择替换技能组');
      return;
    }

    deleteMutation.mutate({
      id: deletingSkillGroup.id,
      replacementId: replacementSkillGroupId,
    });
  };

  // 技能组名称中文映射
  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
    };
    return skillGroupNameMap[groupName] || groupName;
  };

  const getSystemSkillGroupTag = (groupName: string) => {
    const systemSkillGroups = ['CNC Machining', 'Milling', 'Turning', 'Grinding', 'Assembly', 'Quality Control'];
    return systemSkillGroups.includes(groupName) ? (
      <Tag color="red" icon={<SafetyOutlined />}>系统技能组</Tag>
    ) : (
      <Tag color="green">自定义技能组</Tag>
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '技能组名称',
      dataIndex: 'group_name',
      key: 'group_name',
      render: (text: string) => <strong>{getSkillGroupDisplayName(text)}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'group_name',
      key: 'type',
      render: (groupName: string) => getSystemSkillGroupTag(groupName),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: SkillGroup) => {
        const isSystemSkillGroup = ['CNC Machining', 'Milling', 'Turning', 'Grinding', 'Assembly', 'Quality Control'].includes(record.group_name);
        
        return (
          <Space>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={isSystemSkillGroup}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
              disabled={isSystemSkillGroup}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          新建技能组
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={skillGroups}
        rowKey="id"
        loading={isLoading}
        pagination={false}
      />

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingSkillGroup ? '编辑技能组' : '新建技能组'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingSkillGroup(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="group_name"
            label="技能组名称"
            rules={[
              { required: true, message: '请输入技能组名称' },
              { min: 2, message: '技能组名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入技能组名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入技能组描述（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 安全删除模态框 */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            删除技能组确认
          </Space>
        }
        open={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeletingSkillGroup(null);
          setDependencyInfo(null);
          setReplacementSkillGroupId(undefined);
        }}
        confirmLoading={deleteMutation.isLoading}
        width={700}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        {dependencyInfo && (
          <div>
            <Alert
              message="警告"
              description={`删除技能组 "${getSkillGroupDisplayName(dependencyInfo.group_name)}" 将影响 ${dependencyInfo.user_count} 个用户、${dependencyInfo.machine_count} 台设备、${dependencyInfo.plan_task_count} 个生产计划`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Tabs defaultActiveKey="users">
              {dependencyInfo.affected_users.length > 0 && (
                <TabPane tab={`用户 (${dependencyInfo.user_count})`} key="users">
                  <List
                    size="small"
                    dataSource={dependencyInfo.affected_users}
                    renderItem={(user) => (
                      <List.Item>
                        <Space>
                          <UserOutlined />
                          <Text>{user.username}</Text>
                          {user.full_name && <Text type="secondary">({user.full_name})</Text>}
                        </Space>
                      </List.Item>
                    )}
                  />
                </TabPane>
              )}

              {dependencyInfo.affected_machines.length > 0 && (
                <TabPane tab={`设备 (${dependencyInfo.machine_count})`} key="machines">
                  <List
                    size="small"
                    dataSource={dependencyInfo.affected_machines}
                    renderItem={(machine) => (
                      <List.Item>
                        <Space>
                          <ToolOutlined />
                          <Text>{machine.machine_name}</Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </TabPane>
              )}

              {dependencyInfo.affected_plan_tasks.length > 0 && (
                <TabPane tab={`生产计划 (${dependencyInfo.plan_task_count})`} key="plans">
                  <List
                    size="small"
                    dataSource={dependencyInfo.affected_plan_tasks}
                    renderItem={(task) => (
                      <List.Item>
                        <Space>
                          <ScheduleOutlined />
                          <Text>工单 #{task.work_order_id} - 任务 #{task.id}</Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </TabPane>
              )}
            </Tabs>

            {!dependencyInfo.can_delete && (
              <>
                <Divider />
                <Form.Item
                  label="选择替换技能组"
                  required
                >
                  <Select
                    placeholder="请选择替换技能组"
                    value={replacementSkillGroupId}
                    onChange={setReplacementSkillGroupId}
                    options={skillGroups
                      .filter(sg => sg.id !== dependencyInfo.skill_group_id)
                      .map(sg => ({
                        value: sg.id,
                        label: getSkillGroupDisplayName(sg.group_name),
                      }))}
                  />
                </Form.Item>
                <Alert
                  message="必须选择替换技能组"
                  description="由于该技能组被用户、设备或生产计划使用，删除前必须指定一个替换技能组来保证系统的连续性。"
                  type="info"
                  showIcon
                />
              </>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default SkillGroupManagement;
