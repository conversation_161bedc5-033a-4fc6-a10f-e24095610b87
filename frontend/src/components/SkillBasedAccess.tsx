import React from 'react';
import { useAuthStore } from '@/store/auth';

interface SkillBasedAccessProps {
  requiredSkills: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAll?: boolean; // 是否需要拥有所有技能
}

/**
 * 基于技能的访问控制组件
 * 根据用户技能决定是否显示子组件
 */
const SkillBasedAccess: React.FC<SkillBasedAccessProps> = ({
  requiredSkills,
  children,
  fallback = null,
  requireAll = false
}) => {
  const { user } = useAuthStore();
  
  if (!user || !user.skills) {
    return <>{fallback}</>;
  }

  const userSkills = user.skills;
  
  // 检查技能权限
  const hasSkill = requireAll 
    ? requiredSkills.every(skill => userSkills.includes(skill))
    : requiredSkills.some(skill => userSkills.includes(skill));

  return hasSkill ? <>{children}</> : <>{fallback}</>;
};

export default SkillBasedAccess;
