import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Collapse, Space } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  WarningOutlined 
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 在开发环境下打印错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 React Error Boundary');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.groupEnd();
    }

    // 在生产环境下可以发送错误报告到监控服务
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  // 错误报告函数（可以集成第三方错误监控服务）
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 这里可以发送到错误监控服务
    // 例如：Sentry, LogRocket, Bugsnag 等
    console.log('Error Report:', errorReport);
    
    // 示例：发送到后端API
    // fetch('/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorReport),
    // }).catch(console.error);
  };

  // 重置错误状态
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  // 刷新页面
  private handleReload = () => {
    window.location.reload();
  };

  // 返回首页
  private handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  // 复制错误信息
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorText = `
错误ID: ${errorId}
时间: ${new Date().toLocaleString()}
错误信息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
页面URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('错误信息已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback UI，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, errorId } = this.state;
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <div style={{ 
          padding: '24px', 
          minHeight: '100vh', 
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card style={{ maxWidth: '800px', width: '100%' }}>
            <Result
              status="error"
              icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
              title="页面出现错误"
              subTitle={
                <div>
                  <Paragraph>
                    抱歉，页面遇到了一个意外错误。我们已经记录了这个问题，
                    技术团队会尽快修复。
                  </Paragraph>
                  {isDevelopment && (
                    <Paragraph type="secondary">
                      <WarningOutlined /> 开发模式：详细错误信息如下
                    </Paragraph>
                  )}
                </div>
              }
              extra={
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Space wrap>
                    <Button 
                      type="primary" 
                      icon={<ReloadOutlined />}
                      onClick={this.handleReset}
                    >
                      重试
                    </Button>
                    <Button 
                      icon={<ReloadOutlined />}
                      onClick={this.handleReload}
                    >
                      刷新页面
                    </Button>
                    <Button 
                      icon={<HomeOutlined />}
                      onClick={this.handleGoHome}
                    >
                      返回首页
                    </Button>
                  </Space>

                  {isDevelopment && error && (
                    <Card 
                      size="small" 
                      title="错误详情" 
                      style={{ textAlign: 'left' }}
                      extra={
                        <Button 
                          size="small" 
                          onClick={this.handleCopyError}
                        >
                          复制错误信息
                        </Button>
                      }
                    >
                      <Collapse ghost>
                        <Panel header="基本信息" key="basic">
                          <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
                            <div><strong>错误ID:</strong> {errorId}</div>
                            <div><strong>时间:</strong> {new Date().toLocaleString()}</div>
                            <div><strong>页面:</strong> {window.location.href}</div>
                          </div>
                        </Panel>
                        
                        <Panel header="错误信息" key="message">
                          <Text code style={{ fontSize: '12px' }}>
                            {error.message}
                          </Text>
                        </Panel>

                        <Panel header="错误堆栈" key="stack">
                          <pre style={{ 
                            fontSize: '11px', 
                            maxHeight: '200px', 
                            overflow: 'auto',
                            backgroundColor: '#f5f5f5',
                            padding: '8px',
                            borderRadius: '4px'
                          }}>
                            {error.stack}
                          </pre>
                        </Panel>

                        {errorInfo && (
                          <Panel header="组件堆栈" key="component">
                            <pre style={{ 
                              fontSize: '11px', 
                              maxHeight: '200px', 
                              overflow: 'auto',
                              backgroundColor: '#f5f5f5',
                              padding: '8px',
                              borderRadius: '4px'
                            }}>
                              {errorInfo.componentStack}
                            </pre>
                          </Panel>
                        )}
                      </Collapse>
                    </Card>
                  )}

                  {!isDevelopment && (
                    <Card size="small" style={{ textAlign: 'left' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        错误ID: {errorId} | 时间: {new Date().toLocaleString()}
                      </Text>
                    </Card>
                  )}
                </Space>
              }
            />
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// 高阶组件：为组件添加错误边界
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WithErrorBoundaryComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = 
    `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
};

// Hook：在函数组件中使用错误边界
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: ErrorInfo) => {
    // 在函数组件中，我们可以抛出错误让上层的 ErrorBoundary 捕获
    throw error;
  };

  return handleError;
};

export default ErrorBoundary;
