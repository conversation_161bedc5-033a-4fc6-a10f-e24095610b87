import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Select, Spin, Empty } from 'antd';
import { FixedSizeList as List } from 'react-window';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import type { Part } from '@/types/api';

interface VirtualizedSelectProps {
  value?: number;
  onChange?: (value: number, option: Part) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  allowClear?: boolean;
  projectId?: number;
  searchParams?: Record<string, any>;
}

interface OptionData {
  items: Part[];
  hasNextPage: boolean;
  isLoading: boolean;
  onLoadMore: () => void;
}

const ITEM_HEIGHT = 50;
const VISIBLE_ITEMS = 8;

const VirtualizedSelect: React.FC<VirtualizedSelectProps> = ({
  value,
  onChange,
  placeholder = "搜索并选择零件",
  style,
  disabled = false,
  allowClear = true,
  projectId,
  searchParams = {},
}) => {
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [page, setPage] = useState(1);
  const [allParts, setAllParts] = useState<Part[]>([]);
  const listRef = useRef<List>(null);

  const pageSize = 50;

  // 获取零件数据
  const { data: partsData, isLoading, isFetching } = useQuery(
    ['parts-virtualized', searchText, page, projectId, searchParams],
    () => apiClient.getParts({
      ...searchParams,
      limit: pageSize,
      offset: (page - 1) * pageSize,
    } as any),
    {
      keepPreviousData: true,
      staleTime: 30000, // 30秒缓存
    }
  );

  // 累积零件数据
  useEffect(() => {
    if (partsData) {
      if (page === 1) {
        setAllParts(partsData);
      } else {
        setAllParts(prev => [...prev, ...partsData]);
      }
    }
  }, [partsData, page]);

  // 重置数据当搜索条件改变
  useEffect(() => {
    setPage(1);
    setAllParts([]);
  }, [searchText, projectId, JSON.stringify(searchParams)]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (!isFetching && partsData && partsData.length === pageSize) {
      setPage(prev => prev + 1);
    }
  }, [isFetching, partsData, pageSize]);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理选择
  const handleSelect = useCallback((partId: number) => {
    const part = allParts.find(p => p.id === partId);
    if (part && onChange) {
      onChange(partId, part);
    }
    setOpen(false);
  }, [allParts, onChange]);

  // 虚拟化列表项渲染
  const OptionItem = React.memo(({ index, style, data }: {
    index: number;
    style: React.CSSProperties;
    data: OptionData;
  }) => {
    const { items, hasNextPage, isLoading, onLoadMore } = data;
    
    // 如果是最后几项，触发加载更多
    if (index >= items.length - 3 && hasNextPage && !isLoading) {
      onLoadMore();
    }

    // 加载指示器
    if (index >= items.length) {
      return (
        <div style={style} className="ant-select-item ant-select-item-option">
          <div style={{ padding: '8px 12px', textAlign: 'center' }}>
            <Spin size="small" />
          </div>
        </div>
      );
    }

    const part = items[index];
    if (!part) return null;

    return (
      <div
        style={style}
        className="ant-select-item ant-select-item-option"
        onClick={() => handleSelect(part.id)}
      >
        <div style={{ padding: '8px 12px', cursor: 'pointer' }}>
          <div style={{ fontWeight: 'bold' }}>{part.part_number}</div>
          {part.part_name && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              {part.part_name}
            </div>
          )}
          <div style={{ fontSize: '11px', color: '#999' }}>
            版本: {part.version}
          </div>
        </div>
      </div>
    );
  });

  // 虚拟化数据
  const virtualizedData: OptionData = useMemo(() => ({
    items: allParts,
    hasNextPage: partsData?.length === pageSize,
    isLoading: isFetching,
    onLoadMore: loadMore,
  }), [allParts, partsData?.length, pageSize, isFetching, loadMore]);

  // 计算列表高度
  const listHeight = Math.min(
    VISIBLE_ITEMS * ITEM_HEIGHT,
    (allParts.length + (isFetching ? 1 : 0)) * ITEM_HEIGHT
  );

  // 自定义下拉内容
  const dropdownRender = useCallback(() => {
    if (isLoading && allParts.length === 0) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Spin />
        </div>
      );
    }

    if (allParts.length === 0) {
      return (
        <div style={{ padding: '20px' }}>
          <Empty description="暂无数据" />
        </div>
      );
    }

    return (
      <List
        ref={listRef}
        height={listHeight}
        width="100%"
        itemCount={allParts.length + (isFetching ? 1 : 0)}
        itemSize={ITEM_HEIGHT}
        itemData={virtualizedData}
        overscanCount={5}
      >
        {OptionItem}
      </List>
    );
  }, [isLoading, allParts.length, listHeight, virtualizedData, OptionItem, isFetching]);

  // 获取显示值
  const displayValue = useMemo(() => {
    if (!value) return undefined;
    const part = allParts.find(p => p.id === value);
    return part ? `${part.part_number} - ${part.part_name || '未命名'}` : undefined;
  }, [value, allParts]);

  return (
    <Select
      value={displayValue}
      placeholder={placeholder}
      style={style}
      disabled={disabled}
      allowClear={allowClear}
      showSearch
      open={open}
      onDropdownVisibleChange={setOpen}
      onSearch={handleSearch}
      filterOption={false}
      dropdownRender={dropdownRender}
      notFoundContent={null}
    />
  );
};

export default VirtualizedSelect;
