import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Select, Card, Row, Col, Typography, Tabs, Input, Space, Button, Empty, Spin, Switch } from 'antd';
import { SearchOutlined, ProjectOutlined, PartitionOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import VirtualizedSelect from './VirtualizedSelect';
import type { Part, Project, ProjectBomWithDetails } from '@/types/api';

const { Text, Title } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;

interface PartSelectorProps {
  value?: number;
  onChange?: (partId: number, part: Part) => void;
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  disabled?: boolean;
  allowClear?: boolean;
  showPartInfo?: boolean;
  mode?: 'all' | 'project-based'; // 'all' 显示所有零件，'project-based' 按项目分组
  enableVirtualization?: boolean; // 是否启用虚拟化（用于大量数据）
}

interface PartWithProject extends Part {
  project_name?: string;
  project_id?: number;
  bom_quantity?: number;
}

const PartSelector: React.FC<PartSelectorProps> = ({
  value,
  onChange,
  placeholder = "搜索并选择零件",
  size = 'middle',
  style,
  disabled = false,
  allowClear = true,
  showPartInfo = false,
  mode = 'all',
  enableVirtualization = false,
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>(mode === 'project-based' ? 'by-project' : 'all-parts');
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [useVirtualization, setUseVirtualization] = useState(enableVirtualization);

  // 获取所有项目
  const { data: projects = [] } = useQuery(
    'projects',
    () => apiClient.getProjects(),
    { enabled: mode === 'project-based' || activeTab === 'by-project' }
  );

  // 获取所有零件（分页）
  const { data: allPartsData, isLoading: isLoadingAllParts } = useQuery(
    ['parts', searchText, selectedProject],
    () => apiClient.getParts({
      limit: 100 // 增加每页数量
    } as any),
    {
      enabled: activeTab === 'all-parts' || (activeTab === 'by-project' && !!selectedProject),
      keepPreviousData: true,
    }
  );

  // 获取项目BOM
  const { data: projectBom = [], isLoading: isLoadingProjectBom } = useQuery(
    ['project-bom', selectedProject],
    () => selectedProject ? apiClient.getProjectBom(selectedProject) : Promise.resolve([]),
    { 
      enabled: !!selectedProject && activeTab === 'by-project',
      keepPreviousData: true,
    }
  );

  // 处理零件数据
  const processedParts = useMemo(() => {
    if (activeTab === 'all-parts') {
      return allPartsData || [];
    } else if (activeTab === 'by-project' && projectBom.length > 0) {
      // 将项目BOM转换为零件格式
      return projectBom.map((bomItem: ProjectBomWithDetails): PartWithProject => ({
        id: bomItem.part_id,
        part_number: bomItem.part_number,
        part_name: bomItem.part_name,
        version: bomItem.version,
        specifications: bomItem.specifications,
        project_id: bomItem.project_id,
        bom_quantity: bomItem.quantity,
      }));
    }
    return [];
  }, [activeTab, allPartsData, projectBom]);

  // 过滤零件
  const filteredParts = useMemo(() => {
    if (!searchText) return processedParts;
    
    const searchLower = searchText.toLowerCase();
    return processedParts.filter(part => 
      part.part_number.toLowerCase().includes(searchLower) ||
      (part.part_name && part.part_name.toLowerCase().includes(searchLower))
    );
  }, [processedParts, searchText]);

  // 处理零件选择
  const handlePartSelect = useCallback((partId: number) => {
    const part = filteredParts.find(p => p.id === partId);
    if (part && onChange) {
      setSelectedPart(part);
      onChange(partId, part);
    }
  }, [filteredParts, onChange]);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理项目选择
  const handleProjectSelect = useCallback((projectId: number) => {
    setSelectedProject(projectId);
    setSearchText(''); // 清空搜索
  }, []);

  // 获取选中的零件信息
  useEffect(() => {
    if (value && filteredParts.length > 0) {
      const part = filteredParts.find(p => p.id === value);
      setSelectedPart(part || null);
    }
  }, [value, filteredParts]);

  const renderPartOption = (part: PartWithProject) => (
    <Select.Option key={part.id} value={part.id}>
      <div>
        <strong>{part.part_number}</strong>
        {part.part_name && <span> - {part.part_name}</span>}
        <span style={{ color: '#999', marginLeft: 8 }}>({part.version})</span>
        {part.bom_quantity && (
          <span style={{ color: '#1890ff', marginLeft: 8 }}>
            数量: {part.bom_quantity}
          </span>
        )}
      </div>
    </Select.Option>
  );

  const renderAllPartsTab = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Search
              placeholder="搜索零件编号或名称"
              allowClear
              onSearch={handleSearch}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ flex: 1, marginRight: 8 }}
            />
            <div style={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
              <ThunderboltOutlined style={{ marginRight: 4 }} />
              <Switch
                size="small"
                checked={useVirtualization}
                onChange={setUseVirtualization}
                title="启用虚拟化（适用于大量数据）"
              />
            </div>
          </div>
        </Space>
      </div>

      {useVirtualization ? (
        <VirtualizedSelect
          value={value}
          placeholder={placeholder}
          style={{ width: '100%', ...style }}
          disabled={disabled}
          allowClear={allowClear}
          onChange={onChange}
          searchParams={{ part_number: searchText || undefined }}
        />
      ) : (
        <Select
          value={value}
          placeholder={placeholder}
          style={{ width: '100%', ...style }}
          size={size}
          disabled={disabled}
          allowClear={allowClear}
          showSearch
          loading={isLoadingAllParts}
          onChange={handlePartSelect}
          optionFilterProp="children"
          filterOption={false} // 使用自定义过滤
        >
          {filteredParts.map(renderPartOption)}
        </Select>
      )}
    </div>
  );

  const renderProjectBasedTab = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Select
            placeholder="先选择项目"
            style={{ width: '100%' }}
            value={selectedProject}
            onChange={handleProjectSelect}
            allowClear
          >
            {projects.map(project => (
              <Select.Option key={project.id} value={project.id}>
                <div>
                  <strong>{project.project_name}</strong>
                  {project.customer_name && (
                    <span style={{ color: '#999', marginLeft: 8 }}>
                      ({project.customer_name})
                    </span>
                  )}
                </div>
              </Select.Option>
            ))}
          </Select>
          
          {selectedProject && (
            <Search
              placeholder="在项目零件中搜索"
              allowClear
              onSearch={handleSearch}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '100%' }}
            />
          )}
        </Space>
      </div>

      {selectedProject ? (
        <Select
          value={value}
          placeholder="从项目BOM中选择零件"
          style={{ width: '100%', ...style }}
          size={size}
          disabled={disabled}
          allowClear={allowClear}
          loading={isLoadingProjectBom}
          onChange={handlePartSelect}
          notFoundContent={
            isLoadingProjectBom ? <Spin size="small" /> : 
            <Empty description="该项目暂无零件" />
          }
        >
          {filteredParts.map(renderPartOption)}
        </Select>
      ) : (
        <div style={{ 
          border: '1px dashed #d9d9d9', 
          borderRadius: 6, 
          padding: 40, 
          textAlign: 'center',
          color: '#999'
        }}>
          <ProjectOutlined style={{ fontSize: 24, marginBottom: 8 }} />
          <div>请先选择项目</div>
        </div>
      )}
    </div>
  );

  // 简单模式：只显示选择器
  if (mode === 'all') {
    return renderAllPartsTab();
  }

  // 完整模式：显示标签页
  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <span>
              <PartitionOutlined />
              所有零件
            </span>
          } 
          key="all-parts"
        >
          {renderAllPartsTab()}
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <ProjectOutlined />
              按项目选择
            </span>
          } 
          key="by-project"
        >
          {renderProjectBasedTab()}
        </TabPane>
      </Tabs>

      {/* 显示选中零件的详细信息 */}
      {showPartInfo && selectedPart && (
        <Card size="small" style={{ marginTop: 16 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 8 }}>选中零件信息</Title>
          <Row gutter={16}>
            <Col span={8}>
              <Text strong>零件编号:</Text>
              <div>{selectedPart.part_number}</div>
            </Col>
            <Col span={8}>
              <Text strong>零件名称:</Text>
              <div>{selectedPart.part_name || '未命名'}</div>
            </Col>
            <Col span={8}>
              <Text strong>版本:</Text>
              <div>{selectedPart.version}</div>
            </Col>
          </Row>
          {selectedPart.specifications && (
            <div style={{ marginTop: 8 }}>
              <Text strong>规格说明:</Text>
              <div>{selectedPart.specifications}</div>
            </div>
          )}
          {(selectedPart as PartWithProject).bom_quantity && (
            <div style={{ marginTop: 8 }}>
              <Text strong>BOM数量:</Text>
              <div>{(selectedPart as PartWithProject).bom_quantity}</div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default PartSelector;
