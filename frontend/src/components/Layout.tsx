import React, { useState, useMemo } from 'react';
import { Layout as AntLayout, Menu, Button, Dropdown, Avatar, Space, Typography } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  ToolOutlined,
  SettingOutlined,
  FileTextOutlined,
  CalendarOutlined,
  PlayCircleOutlined,
  SafetyCertificateOutlined,
  TeamOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  UserOutlined,
  ApartmentOutlined,
  ApiOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { getUserMenuItems, hasPageAccess } from '@/utils/permissions';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // 所有可能的菜单项配置
  const allMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">仪表板</Link>,
      title: '仪表板'
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: <Link to="/projects">项目管理</Link>,
      title: '项目管理'
    },
    {
      key: '/parts',
      icon: <ToolOutlined />,
      label: <Link to="/parts">零件管理</Link>,
      title: '零件管理'
    },
    {
      key: '/machines',
      icon: <SettingOutlined />,
      label: <Link to="/machines">设备管理</Link>,
      title: '设备管理'
    },
    {
      key: '/work-orders',
      icon: <FileTextOutlined />,
      label: <Link to="/work-orders">工单管理</Link>,
      title: '工单管理'
    },
    {
      key: '/plan-tasks',
      icon: <CalendarOutlined />,
      label: <Link to="/plan-tasks">生产计划</Link>,
      title: '生产计划'
    },
    {
      key: '/execution',
      icon: <PlayCircleOutlined />,
      label: <Link to="/execution">执行跟踪</Link>,
      title: '执行跟踪'
    },
    {
      key: '/operator-execution',
      icon: <PlayCircleOutlined />,
      label: <Link to="/operator-execution">生产执行</Link>,
      title: '生产执行'
    },
    {
      key: '/quality',
      icon: <SafetyCertificateOutlined />,
      label: <Link to="/quality">质量管理</Link>,
      title: '质量管理'
    },
    {
      key: '/bom',
      icon: <ApartmentOutlined />,
      label: <Link to="/bom">BOM管理</Link>,
      title: 'BOM管理'
    },
    {
      key: '/routings',
      icon: <SettingOutlined />,
      label: <Link to="/routings">工艺管理</Link>,
      title: '工艺管理'
    },
    {
      key: '/users',
      icon: <TeamOutlined />,
      label: <Link to="/users">用户管理</Link>,
      title: '用户管理'
    },
    {
      key: '/role-permissions',
      icon: <SettingOutlined />,
      label: <Link to="/role-permissions">角色权限</Link>,
      title: '角色权限管理'
    },
    {
      key: '/api-test',
      icon: <ApiOutlined />,
      label: <Link to="/api-test">API测试</Link>,
      title: 'API测试'
    },
    {
      key: '/database',
      icon: <DatabaseOutlined />,
      label: <Link to="/database">数据库查看</Link>,
      title: '数据库查看'
    },
  ];

  // 根据用户角色动态生成菜单项
  const menuItems = useMemo(() => {
    if (!user || !user.roles) {
      return [];
    }

    const userRoles = user.roles;
    const accessibleMenus = getUserMenuItems(userRoles);

    return allMenuItems.filter(item =>
      accessibleMenus.includes(item.key) ||
      hasPageAccess(userRoles, item.key)
    );
  }, [user]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <AntLayout>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
        }}>
          {collapsed ? 'MES' : 'MES 系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
        />
      </Sider>
      <AntLayout style={{ marginLeft: collapsed ? 80 : 200 }}>
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space>
            <Text>欢迎，{user?.full_name || user?.username}</Text>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Avatar 
                style={{ backgroundColor: '#1890ff', cursor: 'pointer' }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          </Space>
        </Header>
        <Content style={{ 
          margin: '16px',
          overflow: 'initial',
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
