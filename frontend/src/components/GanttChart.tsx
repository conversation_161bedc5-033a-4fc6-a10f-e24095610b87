import React, { useMemo } from 'react';
import { Card, Typography, Row, Col, Tag, Tooltip } from 'antd';
import { format, parseISO, differenceInDays, addDays, startOfDay } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { PlanTaskWithDetails } from '@/types/api';

const { Title, Text } = Typography;

interface GanttChartProps {
  tasks: PlanTaskWithDetails[];
  title?: string;
  height?: number;
}

interface GanttTask {
  id: number;
  name: string;
  startDate: Date;
  endDate: Date;
  status: string;
  progress: number;
  dependencies?: number[];
  assignee?: string;
}

const GanttChart: React.FC<GanttChartProps> = ({ 
  tasks, 
  title = "生产计划甘特图", 
  height = 400 
}) => {
  const ganttTasks: GanttTask[] = useMemo(() => {
    return tasks.map(task => ({
      id: task.id,
      name: `${task.process_name} - ${task.part_number}`,
      startDate: parseISO(task.planned_start),
      endDate: parseISO(task.planned_end),
      status: task.status,
      progress: 0, // 可以根据状态计算进度
      assignee: task.skill_group_name,
    }));
  }, [tasks]);

  const { startDate, endDate, totalDays } = useMemo(() => {
    if (ganttTasks.length === 0) {
      const today = new Date();
      return {
        startDate: today,
        endDate: addDays(today, 30),
        totalDays: 30,
      };
    }

    const dates = ganttTasks.flatMap(task => [task.startDate, task.endDate]);
    const minDate = startOfDay(new Date(Math.min(...dates.map(d => d.getTime()))));
    const maxDate = startOfDay(new Date(Math.max(...dates.map(d => d.getTime()))));
    
    return {
      startDate: minDate,
      endDate: maxDate,
      totalDays: differenceInDays(maxDate, minDate) + 1,
    };
  }, [ganttTasks]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return '#d9d9d9';
      case 'in_progress':
        return '#1890ff';
      case 'completed':
        return '#52c41a';
      case 'cancelled':
        return '#ff4d4f';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return '待开始';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const generateDateHeaders = () => {
    const headers = [];
    for (let i = 0; i < totalDays; i++) {
      const date = addDays(startDate, i);
      headers.push(
        <div
          key={i}
          className="gantt-date-header"
          style={{
            width: '40px',
            minWidth: '40px',
            padding: '8px 4px',
            borderRight: '1px solid #f0f0f0',
            textAlign: 'center',
            fontSize: '12px',
            backgroundColor: '#fafafa',
          }}
        >
          <div>{format(date, 'MM/dd', { locale: zhCN })}</div>
          <div style={{ fontSize: '10px', color: '#999' }}>
            {format(date, 'EEE', { locale: zhCN })}
          </div>
        </div>
      );
    }
    return headers;
  };

  const generateTaskBar = (task: GanttTask) => {
    const taskStart = Math.max(0, differenceInDays(task.startDate, startDate));
    const taskDuration = differenceInDays(task.endDate, task.startDate) + 1;
    const taskWidth = Math.max(1, taskDuration);

    return (
      <div
        key={task.id}
        className="gantt-task-row"
        style={{
          display: 'flex',
          alignItems: 'center',
          minHeight: '60px',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        {/* Task Info */}
        <div
          style={{
            width: '200px',
            minWidth: '200px',
            padding: '8px 12px',
            borderRight: '1px solid #f0f0f0',
            backgroundColor: '#fafafa',
          }}
        >
          <div style={{ fontWeight: 500, marginBottom: '4px' }}>
            {task.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <Tag color={getStatusColor(task.status)} size="small">
              {getStatusText(task.status)}
            </Tag>
            {task.progress > 0 && (
              <span style={{ marginLeft: '8px' }}>
                进度: {task.progress}%
              </span>
            )}
          </div>
        </div>

        {/* Timeline */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            position: 'relative',
            height: '60px',
            alignItems: 'center',
          }}
        >
          {/* Empty cells before task */}
          {Array.from({ length: taskStart }, (_, i) => (
            <div
              key={`empty-${i}`}
              style={{
                width: '40px',
                minWidth: '40px',
                height: '100%',
                borderRight: '1px solid #f0f0f0',
              }}
            />
          ))}

          {/* Task bar */}
          <Tooltip
            title={
              <div>
                <div><strong>{task.name}</strong></div>
                <div>开始: {format(task.startDate, 'yyyy-MM-dd')}</div>
                <div>结束: {format(task.endDate, 'yyyy-MM-dd')}</div>
                <div>状态: {getStatusText(task.status)}</div>
                {task.progress > 0 && <div>进度: {task.progress}%</div>}
              </div>
            }
          >
            <div
              style={{
                width: `${taskWidth * 40}px`,
                height: '24px',
                backgroundColor: getStatusColor(task.status),
                borderRadius: '4px',
                position: 'relative',
                cursor: 'pointer',
                border: '1px solid rgba(0,0,0,0.1)',
              }}
            >
              {/* Progress bar */}
              {task.progress > 0 && (
                <div
                  style={{
                    width: `${task.progress}%`,
                    height: '100%',
                    backgroundColor: task.status === 'completed' ? '#389e0d' : '#096dd9',
                    borderRadius: '3px',
                    transition: 'width 0.3s ease',
                  }}
                />
              )}
              
              {/* Task text */}
              <div
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: '11px',
                  color: '#fff',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '100%',
                  padding: '0 4px',
                }}
              >
                {taskWidth > 2 ? task.name : ''}
              </div>
            </div>
          </Tooltip>

          {/* Empty cells after task */}
          {Array.from({ length: Math.max(0, totalDays - taskStart - taskWidth) }, (_, i) => (
            <div
              key={`empty-after-${i}`}
              style={{
                width: '40px',
                minWidth: '40px',
                height: '100%',
                borderRight: '1px solid #f0f0f0',
              }}
            />
          ))}
        </div>
      </div>
    );
  };

  if (ganttTasks.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          暂无计划任务数据
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ marginBottom: '16px' }}>
        <Title level={4} style={{ margin: 0 }}>
          {title}
        </Title>
        <Text type="secondary">
          时间范围: {format(startDate, 'yyyy-MM-dd')} 至 {format(endDate, 'yyyy-MM-dd')} 
          ({totalDays} 天)
        </Text>
      </div>

      <div
        style={{
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          overflow: 'auto',
          maxHeight: height,
        }}
      >
        {/* Header */}
        <div style={{ display: 'flex', backgroundColor: '#fafafa' }}>
          <div
            style={{
              width: '200px',
              minWidth: '200px',
              padding: '12px',
              borderRight: '1px solid #f0f0f0',
              fontWeight: 600,
            }}
          >
            任务名称
          </div>
          <div style={{ display: 'flex', flex: 1 }}>
            {generateDateHeaders()}
          </div>
        </div>

        {/* Task rows */}
        <div>
          {ganttTasks.map(task => generateTaskBar(task))}
        </div>
      </div>

      {/* Legend */}
      <Row gutter={16} style={{ marginTop: '16px' }}>
        <Col>
          <Text strong>状态说明：</Text>
        </Col>
        <Col>
          <Tag color="#d9d9d9">待开始</Tag>
        </Col>
        <Col>
          <Tag color="#1890ff">进行中</Tag>
        </Col>
        <Col>
          <Tag color="#52c41a">已完成</Tag>
        </Col>
        <Col>
          <Tag color="#ff4d4f">已取消</Tag>
        </Col>
      </Row>
    </Card>
  );
};

export default GanttChart;
